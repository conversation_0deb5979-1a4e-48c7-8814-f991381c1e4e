{"name": "xjxq-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:prod": "NEXT_PUBLIC_API_URL=https://next.xjxq.club/api/next next build", "start": "next start", "start:prod": "NODE_ENV=production next start", "lint": "next lint"}, "dependencies": {"@codemirror/autocomplete": "^6.18.6", "@codemirror/closebrackets": "^0.19.2", "@codemirror/commands": "^6.8.0", "@codemirror/lang-cpp": "^6.0.2", "@codemirror/lang-java": "^6.0.1", "@codemirror/lang-javascript": "^6.2.3", "@codemirror/lang-python": "^6.1.7", "@codemirror/language": "^6.10.8", "@codemirror/language-data": "^6.5.1", "@codemirror/lint": "^6.8.4", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.36.4", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@heroicons/react": "^2.2.0", "@pqina/pintura": "^8.92.7", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@reduxjs/toolkit": "^2.6.1", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-datepicker": "^6.2.0", "@types/react-syntax-highlighter": "^15.5.13", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-react": "^1.0.6", "@wangeditor/plugin-md": "^1.0.0", "axios": "^1.8.1", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "highlight.js": "^11.11.1", "katex": "^0.16.21", "lucide-react": "^0.484.0", "markdown-it": "^14.1.0", "markdown-it-texmath": "^1.0.0", "next": "15.2.4", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-datepicker": "^8.3.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-image-crop": "^11.0.7", "react-markdown": "^10.1.0", "react-qr-code": "^2.0.15", "react-quill": "^2.0.0", "react-redux": "^9.2.0", "react-syntax-highlighter": "^15.6.1", "redux-persist": "^6.0.0", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "schema-dts": "^1.1.5", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.2.1", "msw": "^2.7.3", "tailwindcss": "^4", "typescript": "^5"}, "msw": {"workerDirectory": ["xjxq-next/public"]}}