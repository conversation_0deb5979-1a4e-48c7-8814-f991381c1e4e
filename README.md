# 信竞星球 - 少儿编程题库平台

信竞星球是一个专为少儿编程学习设计的在线题库平台，旨在通过丰富的基础题目和算法编程挑战，帮助孩子构建扎实的编程基础，培养逻辑思维和问题解决能力。

## 项目技术栈

- **前端框架**：React.js + Next.js
- **样式方案**：TailwindCSS
- **HTTP请求**：Axios
- **模拟数据**：MSW (Mock Service Worker)

## 功能模块

1. **用户认证**
   - 用户注册
   - 用户登录
   - 个人资料管理

2. **基础题库**
   - 题目列表展示
   - 题目详情页
   - 题目筛选和搜索

3. **编程题库**
   - 编程题目列表
   - 编程题目详情
   - 在线代码编辑和提交

4. **模拟考试**
   - 考试列表
   - 考试详情
   - 考试结果分析

5. **编程资讯**
   - 资讯列表
   - 资讯详情

6. **会员服务**
   - 会员套餐展示
   - 支付功能

## 开发进度

### 2023-11-01
- [x] 项目初始化
- [x] 安装必要依赖
- [x] 配置TailwindCSS
- [x] 封装Axios请求工具
- [x] 设置MSW模拟服务
- [x] 创建基本目录结构
- [x] 实现首页
- [x] 实现用户认证页面（登录/注册）
- [x] 实现基础题库页面
- [x] 实现编程题库页面

### 2024-03-06
- [x] 更新和完善登录页面的验证逻辑
- [x] 实现模拟考试列表页面
  - 支持筛选、排序和搜索功能
  - 支持网格视图和列表视图切换
  - 添加分页功能
- [x] 实现模拟考试详情页面
  - 实现考试计时功能
  - 支持选择题和多选题的作答
  - 支持编程题的代码编辑和运行
  - 添加答题卡和题目导航功能
  - 实现题目标记功能
- [x] 实现考试相关API
  - 添加考试列表API
  - 添加考试详情API

### 2024-03-07
- [x] 优化全局布局结构
  - 将导航栏和页脚组件移至`layout.tsx`，实现全局共享
  - 移除各页面中冗余的导航栏和页脚代码
  - 提高代码复用率和维护性
- [x] 导航栏增强功能
  - 实现导航栏吸顶效果，提升用户体验
  - 导航菜单项高亮显示当前页面
  - 添加平滑过渡动画效果
- [x] 实现编程资讯模块
  - 完成`/api/news`接口，提供资讯列表数据
  - 完成`/api/news/[id]`接口，提供资讯详情数据
  - 开发编程资讯列表页，支持筛选、搜索和分页
  - 开发编程资讯详情页，支持评论、点赞等互动功能

### 2024-03-08
- [x] 实现会员服务和用户中心模块
  - 开发会员服务页面，展示会员套餐和权益对比
  - 开发个人中心页面，展示用户信息和学习数据
  - 开发学习进度分析页面，使用图表展示学习数据
  - 开发支付页面，支持支付宝和微信支付
- [x] 解决图片问题
  - 将所有Unsplash随机图片替换为Picsum图片服务
  - 使用seed参数确保图片一致性
  - 在next.config.js中添加picsum.photos域名配置

### 2024-03-09
- [x] 完善个人资料编辑功能
  - 实现个人资料编辑模态框组件
  - 添加表单验证和错误提示
  - 实现随机更换头像功能
  - 添加个人资料更新API
  - 添加成功/失败消息提示

### 当前问题与待解决事项
- [ ] 页面路由修复
  - 修正编程资讯列表页URL，使用`/news-list`作为统一路径
- [x] API警告修复
  - 解决Next.js动态API路由中`params.id`的警告问题
  - 按照Next.js文档建议，使用`await`处理参数
- [x] 图片加载问题
  - 解决Unsplash随机图片请求失败（404错误）问题
  - 使用Picsum图片服务代替Unsplash，提高稳定性
  - 使用seed参数确保图片的一致性

### 下一步计划
- [x] 实现会员服务页面
- [x] 实现个人中心页面
- [x] 实现学习进度页面
- [x] 实现支付页面
- [ ] 实现用户注册流程
- [x] 完善个人资料编辑功能
- [x] 添加题目收藏与历史记录

### 2024-03-10
- [x] 添加题目收藏与历史记录功能
  - 实现收藏API路由，支持添加和取消收藏
  - 实现历史记录API路由，支持记录和更新浏览记录
  - 创建收藏按钮组件，支持收藏状态切换
  - 在题目详情页集成收藏和历史记录功能
  - 开发"我的收藏"页面，展示收藏的题目
  - 开发"浏览历史"页面，展示历史浏览记录
  - 更新个人中心页面，添加收藏和历史记录快捷入口

### 2024-03-11
- [x] 添加模拟数据
  - 添加用户收藏的模拟数据，支持收藏页面展示
  - 添加历史浏览记录的模拟数据，支持历史记录页面展示
  - 优化用户数据模拟，确保默认用户ID与模拟数据一致
  - 修复类型错误，确保数据类型安全

### 2024-03-12
- [x] 页面优化
  - 优化收藏页面，移除题目的banner图片，改为更简洁的卡片设计
  - 增加题目类型标识，区分基础题和编程题
  - 删除浏览历史页面，简化用户界面
  - 从个人中心页面移除历史记录的入口链接

### 2024-03-13
- [x] 题单功能实现
  - 开发题单列表页面，支持筛选、排序和分页功能
  - 开发题单详情页面，展示题单信息和题目列表
  - 实现题单API路由，提供数据支持
  - 题单详情页支持进度统计和题目过滤功能
  - 更新导航栏，添加题单入口

### 2024-03-14
- [x] 会员订阅流程优化
  - 实现从会员页面跳转到支付页面功能
  - 增加支付信息参数传递，包含会员类型、付费方式和价格
  - 支付页面根据参数动态显示订单信息
  - 添加确认支付逻辑和提示信息

### 2024-03-15
- [x] 修复Next.js警告和优化
  - 解决useSearchParams()需包装在Suspense边界的警告
  - 重构支付页面和题单列表页面，使用Suspense提高性能
  - 在支付页面添加加载状态显示
  - 优化题单列表的搜索功能，实现跨组件状态共享
  - 使用React Context解决组件拆分后的数据传递问题

### 2024-03-16
- [x] 编程题详情页面优化
  - 创建专用布局文件，隐藏页脚提供沉浸式编程体验
  - 引入CodeMirror实现专业代码编辑器，支持语法高亮
  - 优化页面布局，解决长代码导致的显示问题
  - 改进左侧面板折叠功能，折叠时显示图标提高空间利用率
  - 优化执行结果面板，调整为页面高度40%并支持滚动查看
  - 修复编辑器失焦问题，提升代码输入体验
  - 添加自动换行功能，优化长代码显示
  - 增强UI细节，统一内容区域样式
- [x] 动态API路由参数处理优化
  - 解决Next.js动态API路由中`params.id`的警告问题
  - 使用缓存函数`getParams`优化params参数处理
  - 实现全局统一的参数获取模式
  - 重构组件依赖项，减少不必要的重渲染

## 如何运行

```bash
# 安装依赖
npm install

# 开发环境运行
npm run dev

# 构建生产环境
npm run build

# 运行生产环境
npm start
```

## 项目结构

```
xjxq-next/
├── app/                 # Next.js应用页面
│   ├── components/      # React组件
│   ├── login/           # 登录页面
│   ├── register/        # 注册页面
│   ├── basic-problems/  # 基础题库页面
│   │   └── [id]/        # 题目详情页面
│   ├── coding-problems/ # 编程题库页面
│   │   └── [id]/        # 编程题目详情页面
│   ├── problem-lists/   # 题单列表页面
│   │   └── [id]/        # 题单详情页面
│   ├── exams/           # 模拟考试页面
│   │   └── [id]/        # 考试详情页面
│   ├── news-list/       # 编程资讯列表页面
│   ├── news/            # 编程资讯模块
│   │   └── [id]/        # 资讯详情页面
│   ├── membership/      # 会员服务页面
│   ├── profile/         # 个人中心页面
│   │   └── favorites/   # 收藏页面
│   ├── progress/        # 学习进度页面
│   ├── payment/         # 支付页面
│   ├── api/             # API路由
│   │   ├── basic-problems/ # 基础题目API
│   │   ├── coding-problems/ # 编程题目API
│   │   ├── problem-lists/   # 题单API
│   │   ├── auth/           # 认证相关API
│   │   ├── exams/          # 考试相关API
│   │   ├── news/           # 编程资讯API
│   │   ├── favorites/      # 收藏API
│   │   ├── history/        # 历史记录API
│   │   └── profile/        # 个人资料API
│   ├── mocks/           # 模拟数据
│   └── utils/           # 工具函数
├── lib/                 # 公共库
├── public/              # 静态资源
├── .gitignore           # Git忽略文件
├── next.config.js       # Next.js配置
├── package.json         # 项目依赖
├── README.md            # 项目说明
└── tsconfig.json        # TypeScript配置
```

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

## 部署说明

### 使用部署脚本

项目提供了一个自动化部署脚本 `deploy.sh`，用于构建Docker镜像并推送到阿里云容器镜像服务。

#### 基本用法

```bash
# 使用默认配置部署
./deploy.sh

# 指定镜像标签
./deploy.sh --tag v1.0.0

# 指定Registry和命名空间
./deploy.sh -r registry.example.com -n myapp

# 查看帮助信息
./deploy.sh --help
```

#### 可用选项

- `-t, --tag TAG`: 指定镜像标签（默认：latest）
- `-r, --registry REG`: 指定Registry地址
- `-n, --namespace NS`: 指定命名空间（默认：xjxq）
- `-u, --username USER`: 指定用户名
- `-h, --help`: 显示帮助信息

#### 部署流程

1. 构建生产环境（使用 `npm run build:prod`）
2. 构建Docker镜像
3. 登录阿里云Docker Registry
4. 为镜像打标签
5. 推送镜像到阿里云Registry

#### 注意事项

- 首次使用前需要确保已安装Docker
- 需要有效的阿里云容器镜像服务账号
- 确保有足够的磁盘空间用于构建镜像
- 建议在CI/CD流程中使用此脚本
