# Sitemap 配置和使用指南

## 概述

本项目使用 Next.js 13+ 的内置 sitemap 功能，自动生成包含静态页面和动态题目详情页的 XML sitemap。

## 文件结构

```
app/
├── sitemap.ts          # 主 sitemap 生成器
├── robots.txt          # robots.txt 配置
└── ...
```

## 当前配置

### 静态页面 (17个)
- 首页、基础题目列表、编程题目列表等主要页面
- 按优先级排序：首页(1.0) → 核心功能(0.9) → 内容页面(0.6) → 帮助页面(0.5)

### 动态题目页面 (35个)
- **基础题详情页**: `/basic-problems/[id]` (20个)
- **编程题详情页**: `/coding-problems/[id]` (15个)
- 优先级: 0.6，月更新频率

## 配置方式

### 方案1: 模拟数据（当前使用）

适用于开发环境和测试环境：

```typescript
async function getBasicProblemIds(): Promise<string[]> {
  const mockBasicProblemIds = [
    '1', '2', '3', '4', '5', // ... 更多ID
  ]
  return mockBasicProblemIds
}
```

### 方案2: 真实API（生产环境推荐）

取消 `app/sitemap.ts` 中的注释，启用API调用：

```typescript
// 1. 取消import注释
import BaseProblemService from './service/base-problem-service'
import CodingProblemService from './service/coding-problem-service'

// 2. 启用API调用代码
async function getBasicProblemIds(): Promise<string[]> {
  try {
    const allProblemIds: string[] = []
    let page = 1
    const pageSize = 100
    let hasMore = true
    
    while (hasMore) {
      const response = await BaseProblemService.getProblemList({
        pageNum: page,
        pageSize: pageSize
      })
      
      const problemIds = response.data.items.map((problem: any) => problem.id.toString())
      allProblemIds.push(...problemIds)
      
      hasMore = response.data.items.length === pageSize
      page++
      
      // 安全限制：最多获取1000个题目
      if (allProblemIds.length >= 1000) break
    }
    
    return allProblemIds
  } catch (error) {
    console.error('获取基础题目ID列表失败:', error)
    return []
  }
}
```

## 性能优化

### 1. 限制题目数量
- 当前限制：最多1000个题目
- 可根据实际需要调整

### 2. 缓存策略
生产环境建议添加缓存：

```typescript
// app/lib/sitemap-cache.ts
let cachedProblemIds: { basic: string[], coding: string[], lastUpdate: number } | null = null
const CACHE_DURATION = 1000 * 60 * 60 // 1小时

export async function getCachedProblemIds() {
  const now = Date.now()
  
  if (cachedProblemIds && (now - cachedProblemIds.lastUpdate) < CACHE_DURATION) {
    return cachedProblemIds
  }
  
  // 重新获取数据
  const basicIds = await getBasicProblemIds()
  const codingIds = await getCodingProblemIds()
  
  cachedProblemIds = {
    basic: basicIds,
    coding: codingIds,
    lastUpdate: now
  }
  
  return cachedProblemIds
}
```

### 3. 分页sitemap（大型网站）

如果题目数量超过5万个，建议使用 sitemap index：

```typescript
// app/sitemap/index.ts
export default function sitemapIndex() {
  return [
    {
      url: 'https://next.xjxq.club/sitemap/static.xml',
      lastModified: new Date(),
    },
    {
      url: 'https://next.xjxq.club/sitemap/basic-problems.xml',
      lastModified: new Date(),
    },
    {
      url: 'https://next.xjxq.club/sitemap/coding-problems.xml',
      lastModified: new Date(),
    },
  ]
}
```

## 验证和测试

### 本地测试
```bash
# 查看sitemap内容
curl http://localhost:3001/sitemap.xml

# 统计URL数量
curl -s http://localhost:3001/sitemap.xml | grep -c "<url>"

# 查看题目详情页
curl -s http://localhost:3001/sitemap.xml | grep "basic-problems\|coding-problems"
```

### 生产环境验证
1. **Google Search Console**: 提交 sitemap.xml
2. **在线验证工具**: 使用 XML sitemap 验证器
3. **SEO工具**: 使用 Screaming Frog 等工具检查

## 最佳实践

### 1. 优先级设置
- 首页: 1.0
- 题目列表页: 0.9
- 题目详情页: 0.6
- 帮助页面: 0.5

### 2. 更新频率
- 首页: daily
- 题目相关: weekly/monthly
- 静态页面: monthly/yearly

### 3. 排除私有页面
以下页面已自动排除：
- 用户个人页面 (`/profile`, `/settings`)
- 需要登录的页面 (`/wrong-problems`, `/progress`)
- API接口 (`/api/*`)

### 4. robots.txt 配置
确保 `public/robots.txt` 正确配置：

```
User-agent: *
Allow: /

Disallow: /api/
Disallow: /_next/
Disallow: /admin/
Disallow: /.well-known/
Disallow: /profile

Sitemap: https://next.xjxq.club/sitemap.xml
```

## 监控和维护

### 1. 日志监控
sitemap 生成过程会输出日志：
```
生成了 20 个基础题详情页和 15 个编程题详情页的sitemap
```

### 2. 错误处理
- API失败时自动降级为静态页面
- 避免空白sitemap的生成

### 3. 定期更新
- 建议每天重新生成sitemap
- 可配置自动构建流程

## 常见问题

### Q: sitemap太大怎么办？
A: 
1. 启用分页sitemap
2. 限制题目数量
3. 只包含重要题目

### Q: 如何处理动态内容？
A:
1. 使用ISR（增量静态再生）
2. 配置合适的revalidate时间
3. 手动触发重新生成

### Q: 搜索引擎收录效果如何？
A:
1. 提交到Google Search Console
2. 监控收录状态
3. 定期检查sitemap错误

## 相关链接
- [Next.js Sitemap 文档](https://nextjs.org/docs/app/api-reference/file-conventions/metadata/sitemap)
- [Google Sitemap 指南](https://developers.google.com/search/docs/crawling-indexing/sitemaps/overview) 