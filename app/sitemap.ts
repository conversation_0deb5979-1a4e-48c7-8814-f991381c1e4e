import { MetadataRoute } from 'next'
import { getCachedProblemIds } from './lib/sitemap-cache'
// 注意：不能在服务端使用客户端的axios实例
// import BaseProblemService from './service/base-problem-service'
// import CodingProblemService from './service/coding-problem-service'

// 配置 ISR：每24小时重新生成一次sitemap
export const revalidate = 86400 // 24小时（秒）

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = 'https://next.xjxq.club'
  const currentDate = new Date()
  
  // 静态页面路由 - 按优先级排序
  const staticRoutes: MetadataRoute.Sitemap = [
    // 首页 - 最高优先级
    {
      url: baseUrl,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 1.0,
    },
    
    // 核心功能页面 - 高优先级
    {
      url: `${baseUrl}/basic-problems`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/coding-problems`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/problem-lists`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/exams`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    
    // 会员和服务页面
    {
      url: `${baseUrl}/membership`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    
    // 新闻和内容页面
    {
      url: `${baseUrl}/news`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.6,
    },
    {
      url: `${baseUrl}/news-list`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.6,
    },
    
    // 帮助和支持页面
    {
      url: `${baseUrl}/help-center`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/faq`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.4,
    },
    
    // 认证页面
    {
      url: `${baseUrl}/login`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.4,
    },
    
    // 法律页面 - 低优先级但重要
    {
      url: `${baseUrl}/agreement`,
      lastModified: currentDate,
      changeFrequency: 'yearly',
      priority: 0.3,
    },
  ]
  
  // 动态题目页面
  const dynamicRoutes: MetadataRoute.Sitemap = []
  
  try {
    // 使用缓存系统获取题目ID列表
    const cachedData = await getCachedProblemIds()
    
    // 基础题详情页
    cachedData.basicIds.forEach(id => {
      dynamicRoutes.push({
        url: `${baseUrl}/basic-problems/${id}`,
        lastModified: currentDate,
        changeFrequency: 'monthly',
        priority: 0.6,
      })
    })
    
    // 编程题详情页
    cachedData.codingIds.forEach(id => {
      dynamicRoutes.push({
        url: `${baseUrl}/coding-problems/${id}`,
        lastModified: currentDate,
        changeFrequency: 'monthly',
        priority: 0.6,
      })
    })
    
    console.log(`[Sitemap] 生成了 ${cachedData.basicIds.length} 个基础题详情页和 ${cachedData.codingIds.length} 个编程题详情页`)
    
  } catch (error) {
    console.error('[Sitemap] 获取题目列表失败，使用静态页面:', error)
  }
  
  /* 
   * 排除的页面（不包含在sitemap中）：
   * - /profile - 用户个人资料页面（需要登录）
   * - /settings - 用户设置页面（需要登录）
   * - /payment - 支付页面（需要登录）
   * - /purchase - 购买页面（需要登录）
   * - /notifications - 通知页面（需要登录）
   * - /wrong-problems - 错题集页面（需要登录）
   * - /progress - 学习进度页面（需要登录）
   * - /api/* - API接口（在robots.txt中已禁止）
   * - /_next/* - Next.js内部文件（在robots.txt中已禁止）
   */
  
  return [...staticRoutes, ...dynamicRoutes]
}

 