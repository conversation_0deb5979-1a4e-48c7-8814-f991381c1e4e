"use client";

import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faShareSquare } from '@fortawesome/free-solid-svg-icons';

interface ShareButtonProps {
  title: string;
}

export default function ShareButton({ title }: ShareButtonProps) {
  const handleShare = () => {
    // 分享功能
    if (navigator.share) {
      navigator.share({
        title: title || '编程资讯',
        text: title || '编程资讯分享',
        url: window.location.href,
      }).catch(err => {
        console.error('分享失败:', err);
      });
    } else {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(window.location.href).then(() => {
        alert('链接已复制到剪贴板');
      });
    }
  };

  return (
    <button
      onClick={handleShare}
      className="flex items-center text-gray-500 hover:text-indigo-600"
    >
      <FontAwesomeIcon icon={faShareSquare} className="mr-1" />
      <span>分享</span>
    </button>
  );
}
