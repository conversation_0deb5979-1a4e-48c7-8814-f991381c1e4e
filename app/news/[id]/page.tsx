"use client";

import { useState, useEffect, cache } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faArrowLeft,
  faCalendarAlt,
  faUser,
  faEye,
  faTag,
  faThumbsUp,
  faBookmark,
  faShareSquare,
  faComment,
  faChevronRight
} from '@fortawesome/free-solid-svg-icons';
import newsService, { NewsDetailModel } from '../../service/news-service';
import getUrl from '../../utils/url-utils';
import MarkdownRenderer from '../../components/MarkdownRenderer';

// 定义接口
interface Comment {
  id: number;
  user: {
    name: string;
    avatar: string;
    isOfficial?: boolean;
  };
  content: string;
  time: string;
  likes: number;
  replies?: Comment[];
}

interface RelatedNews {
  id: number;
  title: string;
  imageUrl: string;
  publishDate: string;
  viewCount: number;
}

// 定义API响应结构
interface ApiResponse<T> {
  code: number;
  data: T;
  msg?: string;
}

// 创建一个缓存解包函数
const getParams = cache((params: { id: string }) => {
  return { id: params.id };
});

export default function NewsDetail({ params }: { params: { id: string } }) {
  // 使用缓存函数获取解包后的参数
  const { id } = getParams(params);

  const [news, setNews] = useState<NewsDetailModel | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchNewsDetail();
  }, [id]);

  const fetchNewsDetail = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await newsService.getNewsDetail(Number(id)) as unknown as ApiResponse<NewsDetailModel>;

      if (response.data) {
        setNews(response.data);
      } else {
        throw new Error(response.msg || '获取资讯详情失败');
      }
    } catch (err: any) {
      console.error('获取资讯详情错误:', err);
      setError(err.message || '获取资讯详情失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };
  const handleShare = () => {
    // 分享功能
    if (navigator.share) {
      navigator.share({
        title: news?.title || '编程资讯',
        text: news?.title || '编程资讯分享',
        url: window.location.href,
      }).catch(err => {
        console.error('分享失败:', err);
      });
    } else {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(window.location.href).then(() => {
        alert('链接已复制到剪贴板');
      });
    }
  };

  // 日期格式化
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 主要内容 */}
      <main className="max-w-5xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* 返回按钮 */}
        <div className="mb-6">
          <Link href="/news-list" className="flex items-center text-indigo-600 hover:text-indigo-800">
            <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
            <span>返回资讯列表</span>
          </Link>
        </div>

        {/* 错误信息 */}
        {error && (
          <div className="bg-red-50 p-4 rounded-lg text-red-600 mb-8">
            {error}
          </div>
        )}

        {/* 加载中 */}
        {loading && (
          <div className="bg-white rounded-lg shadow-md p-16 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600 mb-2"></div>
            <p className="text-gray-600">加载中...</p>
          </div>
        )}

        {/* 资讯详情 */}
        {!loading && !error && news && (
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            {/* 资讯头部信息 */}
            <div className="p-6 border-b border-gray-200">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">{news.title}</h1>
              <div className="flex flex-wrap items-center text-sm text-gray-500 mb-4">
                <span className="flex items-center mr-4">
                  <FontAwesomeIcon icon={faCalendarAlt} className="mr-1" />
                  发布时间：{formatDate(news.createdTime)}
                </span>
                <span className="flex items-center mr-4">
                  <FontAwesomeIcon icon={faUser} className="mr-1" />
                  作者：{news.author}
                </span>
                <span className="flex items-center mr-4">
                  <FontAwesomeIcon icon={faEye} className="mr-1" />
                  浏览：{news.viewAmt.toLocaleString()}次
                </span>
                <span className="flex items-center">
                  <FontAwesomeIcon icon={faTag} className="mr-1" />
                  分类：{news.categoryName}
                </span>
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={handleShare}
                  className="flex items-center text-gray-500 hover:text-indigo-600"
                >
                  <FontAwesomeIcon icon={faShareSquare} className="mr-1" />
                  <span>分享</span>
                </button>
              </div>
            </div>

            {/* 资讯内容 */}
            <div className="p-6 leading-relaxed text-gray-700">
              {/* 使用MarkdownRenderer渲染内容 */}
              <MarkdownRenderer
                content={news.content}
                className="prose max-w-none"
                codeTheme="light"
              />

              {/* 标签列表 */}
              {news.tags && news.tags.length > 0 && (
                <div className="flex items-center mt-8 pt-4 border-t border-gray-100">
                  <span className="text-gray-700 mr-2">相关标签：</span>
                  <div className="flex flex-wrap gap-2">
                    {news.tags.map(tag => (
                      <span
                        key={tag.id}
                        className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm font-bold"
                      >
                        #{tag.name}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* 相关文章 */}
              {/*<div className="mt-12">*/}
              {/*  <h2 className="text-xl font-bold text-gray-900 mb-4">相关推荐</h2>*/}
              {/*  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">*/}
              {/*    {relatedNews.map(related => (*/}
              {/*      <div key={related.id} className="bg-white rounded-lg shadow-md overflow-hidden">*/}
              {/*        <div className="h-48 w-full relative">*/}
              {/*          <Image */}
              {/*            src={related.imageUrl} */}
              {/*            alt={related.title} */}
              {/*            fill*/}
              {/*            className="object-cover"*/}
              {/*            sizes="(max-width: 768px) 100vw, 30vw"*/}
              {/*          />*/}
              {/*        </div>*/}
              {/*        <div className="p-4">*/}
              {/*          <h3 className="font-semibold text-gray-900 mb-2 hover:text-indigo-600">*/}
              {/*            <Link href={`/news/${related.id}`}>{related.title}</Link>*/}
              {/*          </h3>*/}
              {/*          <div className="flex justify-between items-center text-xs text-gray-500">*/}
              {/*            <span>{formatDate(related.publishDate)}</span>*/}
              {/*            <span>阅读 {related.viewCount.toLocaleString()}次</span>*/}
              {/*          </div>*/}
              {/*        </div>*/}
              {/*      </div>*/}
              {/*    ))}*/}
              {/*  </div>*/}
              {/*</div>*/}
            </div>

            {/* 评论区 */}
            {/*<div className="mt-8 border-t border-gray-200">*/}
            {/*  <div className="p-6">*/}
            {/*    <h2 className="text-xl font-bold mb-4">评论 ({comments.length})</h2>*/}
            {/*    */}
            {/*    /!* 发表评论 *!/*/}
            {/*    <div className="mb-6">*/}
            {/*      <form onSubmit={handleCommentSubmit}>*/}
            {/*        <textarea */}
            {/*          className="w-full border border-gray-300 rounded-lg p-3 text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500" */}
            {/*          rows={3} */}
            {/*          placeholder="发表您的评论..."*/}
            {/*          value={commentText}*/}
            {/*          onChange={(e) => setCommentText(e.target.value)}*/}
            {/*        />*/}
            {/*        <div className="flex justify-end mt-2">*/}
            {/*          <button */}
            {/*            type="submit"*/}
            {/*            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"*/}
            {/*          >*/}
            {/*            发表评论*/}
            {/*          </button>*/}
            {/*        </div>*/}
            {/*      </form>*/}
            {/*    </div>*/}
            {/*    */}
            {/*    /!* 评论列表 *!/*/}
            {/*    {comments.length > 0 ? (*/}
            {/*      <div className="space-y-6">*/}
            {/*        {comments.map(comment => (*/}
            {/*          <div key={comment.id} className="flex space-x-4">*/}
            {/*            <div className="flex-shrink-0">*/}
            {/*              <div className="h-10 w-10 relative rounded-full overflow-hidden">*/}
            {/*                <Image */}
            {/*                  src={comment.user.avatar} */}
            {/*                  alt={comment.user.name} */}
            {/*                  fill*/}
            {/*                  className="object-cover"*/}
            {/*                  sizes="40px"*/}
            {/*                />*/}
            {/*              </div>*/}
            {/*            </div>*/}
            {/*            <div className="flex-grow">*/}
            {/*              <div className="flex items-center justify-between">*/}
            {/*                <h3 className="text-sm font-medium text-gray-900">*/}
            {/*                  {comment.user.name}*/}
            {/*                  {comment.user.isOfficial && (*/}
            {/*                    <span className="bg-indigo-100 text-indigo-800 text-xs px-2 py-0.5 rounded ml-2">官方</span>*/}
            {/*                  )}*/}
            {/*                </h3>*/}
            {/*                <p className="text-sm text-gray-500">{comment.time}</p>*/}
            {/*              </div>*/}
            {/*              <p className="text-gray-700 mt-1">{comment.content}</p>*/}
            {/*              <div className="flex items-center mt-2 space-x-4 text-sm">*/}
            {/*                <button className="flex items-center text-gray-500 hover:text-indigo-600">*/}
            {/*                  <FontAwesomeIcon icon={faThumbsUp} className="mr-1" />*/}
            {/*                  <span>赞 ({comment.likes})</span>*/}
            {/*                </button>*/}
            {/*                <button className="flex items-center text-gray-500 hover:text-indigo-600">*/}
            {/*                  <FontAwesomeIcon icon={faComment} className="mr-1" />*/}
            {/*                  <span>回复</span>*/}
            {/*                </button>*/}
            {/*              </div>*/}
            {/*              */}
            {/*              /!* 回复评论 *!/*/}
            {/*              {comment.replies && comment.replies.length > 0 && (*/}
            {/*                <div className="mt-4 pl-4 border-l-2 border-gray-200">*/}
            {/*                  {comment.replies.map(reply => (*/}
            {/*                    <div key={reply.id} className="flex space-x-3 mb-3">*/}
            {/*                      <div className="flex-shrink-0">*/}
            {/*                        <div className="h-8 w-8 relative rounded-full overflow-hidden">*/}
            {/*                          <Image */}
            {/*                            src={reply.user.avatar} */}
            {/*                            alt={reply.user.name} */}
            {/*                            fill*/}
            {/*                            className="object-cover"*/}
            {/*                            sizes="32px"*/}
            {/*                          />*/}
            {/*                        </div>*/}
            {/*                      </div>*/}
            {/*                      <div className="flex-grow">*/}
            {/*                        <div className="flex items-center justify-between">*/}
            {/*                          <h3 className="text-sm font-medium text-gray-900">*/}
            {/*                            {reply.user.name}*/}
            {/*                            {reply.user.isOfficial && (*/}
            {/*                              <span className="bg-indigo-100 text-indigo-800 text-xs px-2 py-0.5 rounded ml-2">官方</span>*/}
            {/*                            )}*/}
            {/*                          </h3>*/}
            {/*                          <p className="text-sm text-gray-500">{reply.time}</p>*/}
            {/*                        </div>*/}
            {/*                        <p className="text-gray-700 mt-1 text-sm">{reply.content}</p>*/}
            {/*                        <div className="flex items-center mt-2 space-x-4 text-xs">*/}
            {/*                          <button className="flex items-center text-gray-500 hover:text-indigo-600">*/}
            {/*                            <FontAwesomeIcon icon={faThumbsUp} className="mr-1" />*/}
            {/*                            <span>赞 ({reply.likes})</span>*/}
            {/*                          </button>*/}
            {/*                        </div>*/}
            {/*                      </div>*/}
            {/*                    </div>*/}
            {/*                  ))}*/}
            {/*                </div>*/}
            {/*              )}*/}
            {/*            </div>*/}
            {/*          </div>*/}
            {/*        ))}*/}
            {/*      </div>*/}
            {/*    ) : (*/}
            {/*      <div className="text-center py-6 text-gray-500">*/}
            {/*        暂无评论，成为第一个评论的人吧！*/}
            {/*      </div>*/}
            {/*    )}*/}
            {/*  </div>*/}
            {/*</div>*/}
          </div>
        )}
      </main>
    </div>
  );
}
