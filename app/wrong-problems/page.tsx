'use client'

import Link from 'next/link'
import { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faSearch, faCheck, faExternalLinkAlt, faTimes,
  faCalendarCheck, faHashtag, faRocket,
  faCode, faBookOpen, faFilter,
  faSortAmountDown, faClock
} from '@fortawesome/free-solid-svg-icons'
import Pagination from '../components/Pagination'
import WrongProblemService, { WrongProblemView, WrongProblemListResponse } from '../service/wrong-problem-service'
import CommonDropdown, { DropdownOption } from '../components/CommonDropdown'

export default function WrongProblemsPage() {
  const [activeFilter, setActiveFilter] = useState('all')
  const [sortOption, setSortOption] = useState('error_count_desc')
  const [problemTypeFilter, setProblemTypeFilter] = useState<number | null>(null) // 题目类型筛选
  const [currentPage, setCurrentPage] = useState(1)
  const [searchKeyword, setSearchKeyword] = useState('')
  const pageSize = 5

  // 错题单数据状态
  const [wrongProblems, setWrongProblems] = useState<WrongProblemView[]>([])
  const [totalItems, setTotalItems] = useState(0)
  const [loading, setLoading] = useState(false)
  const [statusCounts, setStatusCounts] = useState({
    total: 0,
    pending: 0, // 待巩固
    mastered: 0, // 已掌握
    thisMonth: 0 // 本月新增
  })

  // 定义排序选项
  const sortOptions: DropdownOption<string>[] = [
    {
      label: '错误次数 (多到少)',
      value: 'error_count_desc',
      prefix: <FontAwesomeIcon icon={faSortAmountDown} className="text-indigo-500" />
    },
    {
      label: '最近做错',
      value: 'last_wrong_time_desc',
      prefix: <FontAwesomeIcon icon={faClock} className="text-indigo-500" />
    }
  ]

  // 根据筛选条件获取错题单列表
  const fetchWrongProblems = async () => {
    setLoading(true)
    try {
      // 根据当前筛选条件构建请求参数
      const params: any = {
        pageNum: currentPage,
        pageSize: pageSize,
        title: searchKeyword || undefined,
        problemType: problemTypeFilter, // 添加题目类型筛选
      }

      // 设置排序参数
      if (sortOption === 'error_count_desc') {
        params.sortField = 'wrongCount'
        params.sortOrder = 'DESC'
      } else if (sortOption === 'last_wrong_time_desc') {
        params.sortField = 'lastWrongTime'
        params.sortOrder = 'DESC'
      }

      // 根据active filter设置状态参数
      if (activeFilter === 'pending') {
        params.status = 1 // 待巩固
      } else if (activeFilter === 'mastered') {
        params.status = 2 // 已掌握
      } else if (activeFilter === 'thisMonth') {
        // 获取本月第一天
        const now = new Date()
        const firstDay = new Date(now.getFullYear(), now.getMonth(), 1)
        firstDay.setHours(0, 0, 0, 0) // 设置为当天的00:00:00
        
        // 格式化为 yyyy-MM-dd HH:mm:ss
        const formatDate = (date: Date) => {
          const pad = (num: number) => String(num).padStart(2, '0')
          
          const year = date.getFullYear()
          const month = pad(date.getMonth() + 1)
          const day = pad(date.getDate())
          const hours = pad(date.getHours())
          const minutes = pad(date.getMinutes())
          const seconds = pad(date.getSeconds())
          
          return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
        }
        
        params.startTime = formatDate(firstDay)
        params.endTime = formatDate(now)
      }

      const response = await WrongProblemService.fetchList(params)

      if (response && response.data) {
        const responseData = response.data
        setWrongProblems(responseData.items)
        setTotalItems(responseData.total || 0)

        // 如果按错误次数排序但后端未处理，在前端手动排序
        if (sortOption === 'error_count_desc') {
          setWrongProblems(prev =>
            [...prev].sort((a, b) => (b.wrongCount || 0) - (a.wrongCount || 0))
          )
        }
      }
    } catch (error) {
      console.error('获取错题单列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 获取统计数据
  const fetchStatisticData = async () => {
    try {
      // 当problemType为null或0时不传该参数
      const params = problemTypeFilter ? { problemType: problemTypeFilter } : {}
      const response = await WrongProblemService.getStatisticData(params)
      if (response && response.data) {
        setStatusCounts({
          total: response.data.total || 0,
          pending: response.data.pending || 0,
          mastered: response.data.mastered || 0,
          thisMonth: response.data.thisMonth || 0
        })

        // 处理标签分布数据 - 将数量转换为百分比
        if (response.data.tagCounts && response.data.tagCounts.length > 0) {
          // 计算总数
          const totalCount = response.data.tagCounts.reduce((sum, tag) => sum + tag.count, 0)

          // 按数量排序
          const sortedTags = [...response.data.tagCounts].sort((a, b) => b.count - a.count)

          // 取前4个标签
          const topTags = sortedTags.slice(0, 4)

          // 计算其他标签的总数
          const otherCount = sortedTags.slice(4).reduce((sum, tag) => sum + tag.count, 0)

          // 处理数据为百分比
          let processedTags = topTags.map(tag => ({
            name: tag.name,
            count: Math.round((tag.count / totalCount) * 100) || 1 // 确保至少为1%
          }))

          // 如果有其他标签，添加"其他"类别
          if (otherCount > 0) {
            processedTags.push({
              name: '其他',
              count: Math.round((otherCount / totalCount) * 100) || 1
            })
          }

          // 调整百分比确保总和为100%
          let percentageSum = processedTags.reduce((sum, tag) => sum + tag.count, 0)
          if (percentageSum !== 100 && processedTags.length > 0) {
            // 将差值加到最大的标签上
            const diff = 100 - percentageSum
            processedTags[0].count += diff
          }

          setTagDistribution(processedTags)
        }
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
    }
  }

  // 添加标签分布状态
  const [tagDistribution, setTagDistribution] = useState<{name: string, count: number}[]>([])

  // 当页码、筛选条件、搜索关键词、题目类型筛选或排序选项变化时重新获取数据
  useEffect(() => {
    fetchWrongProblems()
  }, [currentPage, activeFilter, searchKeyword, problemTypeFilter, sortOption])

  // 首次加载时获取统计数据
  useEffect(() => {
    fetchStatisticData()
  }, [problemTypeFilter])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleFilterChange = (filter: string) => {
    setActiveFilter(filter)
    setCurrentPage(1) // 切换筛选条件时，重置为第一页
  }

  const handleSortChange = (value: string) => {
    setSortOption(value)
  }

  const handleProblemTypeChange = (type: number | null) => {
    setProblemTypeFilter(type)
    setCurrentPage(1) // 切换题目类型筛选时，重置为第一页
  }

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setCurrentPage(1) // 搜索时重置为第一页
    // 搜索关键词已通过input的onChange事件设置，这里不需要额外处理
  }

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchKeyword(e.target.value)
  }

  // 标记已掌握或取消标记
  const handleToggleMastered = async (problem: WrongProblemView) => {
    try {
      // 切换状态：1->2 或 2->1
      const newStatus = problem.status === 1 ? 2 : 1
      await WrongProblemService.updateStatus(problem.id, newStatus as 1 | 2)
      // 成功后重新获取数据
      fetchWrongProblems()
    } catch (error) {
      console.error('更新状态失败:', error)
      // 在实际应用中，应该显示错误提示
    }
  }

  // 获取难度文本和样式类
  const getDifficultyInfo = (difficulty: number) => {
    switch (difficulty) {
      case 1:
        return { text: '简单', className: 'bg-green-100 text-green-800' }
      case 2:
        return { text: '中等', className: 'bg-yellow-100 text-yellow-800' }
      case 3:
        return { text: '困难', className: 'bg-red-100 text-red-800' }
      default:
        return { text: '未知', className: 'bg-gray-100 text-gray-800' }
    }
  }

  // 获取标签样式类
  const getTagClass = (tagName: string) => {
    switch (tagName) {
      case '动态规划':
        return 'bg-blue-100 text-blue-800'
      case '图论':
        return 'bg-purple-100 text-purple-800'
      case '数据结构':
        return 'bg-yellow-100 text-yellow-800'
      case '搜索':
        return 'bg-green-100 text-green-800'
      case '贪心':
        return 'bg-pink-100 text-pink-800'
      case '广度优先搜索':
      case '深度优先搜索':
        return 'bg-green-100 text-green-800'
      case '树':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-blue-100 text-blue-800'
    }
  }

  // 获取问题类型信息
  const getProblemTypeInfo = (type: number) => {
    switch (type) {
      case 1:
        return { text: '基础题', className: 'bg-indigo-100 text-indigo-800', icon: faBookOpen }
      case 2:
        return { text: '编程题', className: 'bg-purple-100 text-purple-800', icon: faCode }
      default:
        return { text: '未知类型', className: 'bg-gray-100 text-gray-800', icon: faBookOpen }
    }
  }

  // 获取题目详情页链接
  const getProblemDetailUrl = (problem: WrongProblemView) => {
    // 基础题和编程题的详情页可能不同
    return problem.problemType === 1
      ? `/basic-problems/${problem.displayId}`
      : `/coding-problems/${problem.displayId}`
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 面包屑导航 */}
      <div className="text-sm text-gray-600 mb-6">
        <Link href="/" className="hover:text-indigo-600">首页</Link>
        <span className="mx-2">/</span>
        <span className="text-gray-800">我的错题单</span>
      </div>

      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-2xl md:text-3xl font-bold text-gray-800">我的错题单</h1>
        <p className="text-gray-600 mt-2">记录你曾经做错的题目，帮助你查漏补缺、巩固知识点</p>
      </div>

      {/* 题目类型筛选（错题统计上方） */}
      <div className="bg-white rounded-xl shadow-md mb-4 p-4">
        <div className="flex items-center justify-between">
          <h3 className="text-md font-medium text-gray-800">按题目类型筛选</h3>
          <div className="flex items-center gap-2">
            <button
              className={`${problemTypeFilter === null ? 'bg-indigo-100 text-indigo-800' : 'text-gray-600 hover:bg-gray-100'} px-3 py-1.5 rounded-md text-sm font-medium`}
              onClick={() => handleProblemTypeChange(null)}
            >
              <FontAwesomeIcon icon={faFilter} className="mr-1" />
              全部题型
            </button>
            <button
              className={`${problemTypeFilter === 1 ? 'bg-indigo-100 text-indigo-800' : 'text-gray-600 hover:bg-gray-100'} px-3 py-1.5 rounded-md text-sm font-medium`}
              onClick={() => handleProblemTypeChange(1)}
            >
              <FontAwesomeIcon icon={faBookOpen} className="mr-1" />
              基础题
            </button>
            <button
              className={`${problemTypeFilter === 2 ? 'bg-indigo-100 text-indigo-800' : 'text-gray-600 hover:bg-gray-100'} px-3 py-1.5 rounded-md text-sm font-medium`}
              onClick={() => handleProblemTypeChange(2)}
            >
              <FontAwesomeIcon icon={faCode} className="mr-1" />
              编程题
            </button>
          </div>
        </div>
      </div>

      {/* 错题统计卡片 */}
      <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
        <div className="px-6 py-5 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-800">错题统计</h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="bg-red-50 rounded-lg p-4 text-center">
              <div className="text-3xl font-bold text-red-600">{statusCounts.total}</div>
              <div className="text-sm text-gray-700 mt-1">总错题数</div>
            </div>
            <div className="bg-yellow-50 rounded-lg p-4 text-center">
              <div className="text-3xl font-bold text-yellow-600">{statusCounts.thisMonth}</div>
              <div className="text-sm text-gray-700 mt-1">本月新增</div>
            </div>
            <div className="bg-green-50 rounded-lg p-4 text-center">
              <div className="text-3xl font-bold text-green-600">{statusCounts.mastered}</div>
              <div className="text-sm text-gray-700 mt-1">已掌握</div>
            </div>
            <div className="bg-blue-50 rounded-lg p-4 text-center">
              <div className="text-3xl font-bold text-blue-600">{statusCounts.pending}</div>
              <div className="text-sm text-gray-700 mt-1">待巩固</div>
            </div>
          </div>

          {/* 错题分布图表 */}
          <div className="mt-6 border-t border-gray-100 pt-6">
            <h3 className="text-md font-medium text-gray-800 mb-4">错题知识点分布</h3>
            <div className="h-10 bg-gray-200 rounded-full overflow-hidden">
              <div className="flex h-full">
                {tagDistribution.map((tag, index) => {
                  // 常用颜色列表
                  const colors = ['bg-blue-500', 'bg-purple-500', 'bg-yellow-500', 'bg-green-500', 'bg-red-500', 'bg-indigo-500', 'bg-pink-500'];
                  const bgColor = colors[index % colors.length];

                  return (
                    <div
                      key={tag.name}
                      className={`${bgColor} h-full`}
                      style={{ width: `${tag.count}%` }}
                      title={`${tag.name}: ${tag.count}%`}
                    ></div>
                  );
                })}
              </div>
            </div>
            <div className="flex flex-wrap mt-2 text-xs text-gray-600 gap-x-4 gap-y-2">
              {tagDistribution.map((tag, index) => {
                const colors = ['bg-blue-500', 'bg-purple-500', 'bg-yellow-500', 'bg-green-500', 'bg-red-500', 'bg-indigo-500', 'bg-pink-500'];
                const bgColor = colors[index % colors.length];

                return (
                  <div key={tag.name} className="flex items-center">
                    <div className={`w-3 h-3 ${bgColor} rounded-full mr-1`}></div>
                    <span>{tag.name} ({tag.count}%)</span>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* 过滤和排序工具栏 */}
      <div className="bg-white rounded-xl shadow-md mb-6 p-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          {/* 状态筛选 */}
          <div className="flex flex-wrap gap-2 mb-2 md:mb-0">
            <button
              className={`${activeFilter === 'all' ? 'bg-indigo-100 text-indigo-800' : 'text-gray-600 hover:bg-gray-100'} px-3 py-1 rounded-md text-sm font-medium`}
              onClick={() => handleFilterChange('all')}
            >
              全部（{statusCounts.total}）
            </button>
            <button
              className={`${activeFilter === 'pending' ? 'bg-indigo-100 text-indigo-800' : 'text-gray-600 hover:bg-gray-100'} px-3 py-1 rounded-md text-sm font-medium`}
              onClick={() => handleFilterChange('pending')}
            >
              待巩固（{statusCounts.pending}）
            </button>
            <button
              className={`${activeFilter === 'mastered' ? 'bg-indigo-100 text-indigo-800' : 'text-gray-600 hover:bg-gray-100'} px-3 py-1 rounded-md text-sm font-medium`}
              onClick={() => handleFilterChange('mastered')}
            >
              已掌握（{statusCounts.mastered}）
            </button>
            <button
              className={`${activeFilter === 'thisMonth' ? 'bg-indigo-100 text-indigo-800' : 'text-gray-600 hover:bg-gray-100'} px-3 py-1 rounded-md text-sm font-medium`}
              onClick={() => handleFilterChange('thisMonth')}
            >
              本月新增（{statusCounts.thisMonth}）
            </button>
          </div>

          {/* 排序选项 */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">排序:</span>
            <CommonDropdown
              label="排序方式"
              options={sortOptions}
              value={sortOption}
              onChange={handleSortChange}
              className="w-48 h-9 text-sm"
              placeholder="请选择排序方式"
              canClear={false}
            />
          </div>
        </div>
      </div>

      {/* 错题列表 */}
      <div className="bg-white rounded-xl shadow-md overflow-hidden">
        <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-800">错题列表</h3>
            <form onSubmit={handleSearch} className="relative">
              <input
                type="text"
                placeholder="搜索题目..."
                value={searchKeyword}
                onChange={handleSearchInputChange}
                className="pl-8 pr-4 py-1.5 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
              <FontAwesomeIcon icon={faSearch} className="text-gray-400 absolute left-3 top-2.5" />
            </form>
          </div>
        </div>

        {loading ? (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600"></div>
            <p className="text-gray-600 mt-2">加载错题数据中...</p>
          </div>
        ) : wrongProblems.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-600">暂无符合条件的错题记录</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {wrongProblems.map((problem) => {
              const difficulty = getDifficultyInfo(problem.difficulty)
              const isMastered = problem.status === 2
              const problemType = getProblemTypeInfo(problem.problemType)
              // 获取错误次数，如果没有则显示默认值
              const errorCount = problem.wrongCount

              return (
                <div key={problem.id} className={`p-5 hover:bg-gray-50 ${isMastered ? 'bg-green-50' : ''}`}>
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div className="flex-grow w-2/3">
                      <div className="flex flex-wrap gap-2 mb-2">
                        <span className="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                          做错{problem.wrongCount || 1}次
                        </span>
                        {isMastered && (
                          <span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            已掌握
                          </span>
                        )}
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${difficulty.className}`}>
                          {difficulty.text}
                        </span>
                        {/* 题目类型标识 */}
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${problemType.className} flex items-center`}>
                          <FontAwesomeIcon icon={problemType.icon} className="mr-1" />
                          {problemType.text}
                        </span>
                        {problem.tags.map((tag) => (
                          <span
                            key={tag.id}
                            className={`px-2 py-1 text-xs font-semibold rounded-full ${getTagClass(tag.name)}`}
                          >
                            {tag.name}
                          </span>
                        ))}
                      </div>
                      <h4 className="text-lg font-medium text-gray-800 line-clamp-1">{problem.title}</h4>
                      <div className="mt-2 text-xs text-gray-500">
                        <span className="mr-4">
                          <FontAwesomeIcon icon={faCalendarCheck} className="mr-1" />
                          上次尝试: {problem.lastWrongTime.split(' ')[0]}
                        </span>
                        <span>
                          <FontAwesomeIcon icon={faHashtag} className="mr-1" />
                          题号: #{problem.displayId}
                        </span>
                      </div>
                    </div>
                    <div className="flex mt-4 md:mt-0 md:ml-4">
                      <button
                        className="bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 px-3 py-1.5 rounded-md text-sm mr-2"
                        onClick={() => handleToggleMastered(problem)}
                      >
                        {isMastered ? (
                          <>
                            <FontAwesomeIcon icon={faTimes} className="mr-1" />
                            取消标记
                          </>
                        ) : (
                          <>
                            <FontAwesomeIcon icon={faCheck} className="mr-1" />
                            标记已掌握
                          </>
                        )}
                      </button>
                      <Link
                        href={getProblemDetailUrl(problem)}
                        className="bg-indigo-600 hover:bg-indigo-700 text-white px-3 py-1.5 rounded-md text-sm"
                      >
                        <FontAwesomeIcon icon={faExternalLinkAlt} className="mr-1" />
                        再次挑战
                      </Link>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )}

        {/* 使用公共分页组件 */}
        <div className="px-6 py-4 border-t border-gray-200">
          <Pagination
            currentPage={currentPage}
            totalItems={totalItems}
            pageSize={pageSize}
            onPageChange={handlePageChange}
            showInfo={true}
            maxPageButtons={5}
          />
        </div>
      </div>
    </div>
  )
}
