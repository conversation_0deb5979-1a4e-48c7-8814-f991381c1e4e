'use client'

import { useState, useEffect, useMemo } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
	faSearch,
	faRocket,
	faUserCircle,
	faCode,
	faClipboardList,
	faCrown,
	faTrophy,
	faCogs,
	faHeadset,
	faBook,
	faArrowRight,
	faInfoCircle,
	faExclamationTriangle,
	faTimes,
} from '@fortawesome/free-solid-svg-icons'
import Link from 'next/link'
import Footer from '../components/Footer'
import MarkdownRenderer from '../components/MarkdownRenderer'
import helpCenterService, { HelpCenter } from '../service/help-center-service'

interface Category {
	id: string
	label: string
	icon: typeof faRocket
}

export default function HelpCenterPage() {
	const [helpData, setHelpData] = useState<HelpCenter[]>([])
	const [loading, setLoading] = useState(true)
	const [error, setError] = useState<string | null>(null)
	const [searchTerm, setSearchTerm] = useState('')
	const [activeSection, setActiveSection] = useState('')

	// 图标映射，根据分类名称匹配图标
	const iconMapping: Record<string, typeof faRocket> = {
		'新手入门': faRocket,
		'账号管理': faUserCircle,
		'题目练习': faCode,
		'模拟考试': faClipboardList,
		'会员服务': faCrown,
		'竞赛指南': faTrophy,
		'技术支持': faCogs,
		'平台介绍': faInfoCircle,
		'功能导览': faBook,
		'学习方法': faBook,
		'常见问题': faExclamationTriangle,
		'使用指南': faBook,
		// 默认图标
		'default': faBook
	}

	// 从接口数据中提取分类
	const categories: Category[] = useMemo(() => {
		const uniqueCategories = Array.from(new Set(helpData.map(item => item.question)))
		return uniqueCategories.map(categoryName => ({
			id: categoryName.toLowerCase().replace(/\s+/g, '-'),
			label: categoryName,
			icon: iconMapping[categoryName] || iconMapping['default']
		}))
	}, [helpData])

	// 获取帮助中心数据
	useEffect(() => {
		const fetchHelpData = async () => {
			try {
				setLoading(true)
				const response = await helpCenterService.getHelpCenterList()
				setHelpData(response.data)
			} catch (err) {
				console.error('获取帮助中心数据失败:', err)
				setError('无法加载帮助中心数据，请稍后重试')
			} finally {
				setLoading(false)
			}
		}

		fetchHelpData()
	}, [])

	// 设置初始的活跃分类
	useEffect(() => {
		if (categories.length > 0 && !activeSection) {
			setActiveSection(categories[0].id)
		}
	}, [categories, activeSection])

	// 按分类组织数据
	const groupedData = helpData.reduce((acc, item) => {
		const category = item.question
		if (!acc[category]) {
			acc[category] = []
		}
		acc[category].push(item)
		return acc
	}, {} as Record<string, HelpCenter[]>)

	// 过滤数据（根据搜索词）
	const filteredData = Object.entries(groupedData).reduce((acc, [category, items]) => {
		const filteredItems = items.filter(item =>
			item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
			item.answer.toLowerCase().includes(searchTerm.toLowerCase())
		)
		if (filteredItems.length > 0) {
			acc[category] = filteredItems
		}
		return acc
	}, {} as Record<string, HelpCenter[]>)

	// 获取当前显示的数据
	const currentData = useMemo(() => {
		if (searchTerm) {
			// 搜索时显示所有匹配的内容
			return filteredData
		} else {
			// 正常浏览时只显示当前选中分类的内容
			const activeCategory = categories.find(cat => cat.id === activeSection)
			if (activeCategory && groupedData[activeCategory.label]) {
				return { [activeCategory.label]: groupedData[activeCategory.label] }
			}
			return {}
		}
	}, [searchTerm, filteredData, groupedData, activeSection, categories])

	// 滚动到指定section
	const scrollToSection = (sectionId: string) => {
		setActiveSection(sectionId)
		const element = document.getElementById(sectionId)
		if (element) {
			element.scrollIntoView({ behavior: 'smooth', block: 'start' })
		}
	}

	if (loading) {
		return null
	}

	if (error) {
		return null
	}

	return (
		<div className="min-h-screen flex flex-col bg-gray-50">
			
			{/* 占位符，防止固定导航栏遮挡内容 */}
			<div id="navbar-placeholder"></div>

			{/* 主要内容 */}
			<div className="flex-1 container mx-auto px-4 py-8">
				{/* 页面标题 */}
				<div className="mb-8 text-center">
					<h1 className="text-3xl font-bold text-gray-800 mb-2">帮助中心</h1>
					<p className="text-gray-600">全面的使用指南，帮助您更好地使用信竞星球平台</p>
				</div>

				<div className="flex flex-col lg:flex-row gap-8">
					{/* 左侧导航 */}
					<div className="lg:w-1/4">
						<div className="bg-white rounded-lg shadow-md p-4 sticky top-4">
							<h3 className="text-lg font-semibold text-gray-800 mb-4">帮助分类</h3>
							<div className="space-y-1">
								{categories.length === 0 ? (
									<div className="space-y-2">
										{[1, 2, 3].map(i => (
											<div key={i} className="animate-pulse">
												<div className="h-8 bg-gray-200 rounded"></div>
											</div>
										))}
									</div>
								) : (
									categories.map((category: Category) => (
									<button
										key={category.id}
										onClick={() => scrollToSection(category.id)}
										className={`w-full text-left py-2 px-3 rounded transition ${
											activeSection === category.id
												? 'bg-indigo-50 text-indigo-600 font-medium'
												: 'text-gray-700 hover:bg-gray-50'
										}`}
									>
										<FontAwesomeIcon icon={category.icon} className="mr-2" />
										{category.label}
									</button>
									))
								)}
							</div>
							
							<div className="mt-8 bg-indigo-50 p-4 rounded-lg">
								<h4 className="font-medium text-indigo-800 mb-2">需要更多帮助？</h4>
								<p className="text-sm text-gray-700 mb-3">如果您在帮助中心找不到答案，可以联系我们的客服团队</p>
								<Link
									href="/contact"
									className="bg-indigo-600 text-white text-sm py-2 px-4 rounded w-full block text-center hover:bg-indigo-700 transition"
								>
									<FontAwesomeIcon icon={faHeadset} className="mr-2" />
									联系客服
								</Link>
							</div>
						</div>
					</div>
					
					{/* 右侧内容 */}
					<div className="lg:w-3/4">
						{/* 搜索框 */}
						<div className="bg-white rounded-lg shadow-md p-4 mb-6">
							<div className="relative">
								<input
									type="text"
									placeholder="搜索帮助文档..."
									value={searchTerm}
									onChange={(e) => setSearchTerm(e.target.value)}
									className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
								/>
								{searchTerm ? (
									<button 
										onClick={() => setSearchTerm('')}
										className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
									>
										<FontAwesomeIcon icon={faTimes} />
									</button>
								) : (
									<button className="absolute right-3 top-1/2 transform -translate-y-1/2 text-indigo-600">
										<FontAwesomeIcon icon={faSearch} />
									</button>
								)}
							</div>
							{searchTerm && (
								<div className="mt-2 text-sm text-gray-600">
									搜索结果会显示所有匹配的分类内容。
									<button 
										onClick={() => setSearchTerm('')}
										className="text-indigo-600 hover:text-indigo-800 ml-1"
									>
										清除搜索
									</button>
								</div>
							)}
						</div>
						
						{/* 内容区域 */}
						{Object.keys(currentData).length === 0 ? (
							<div className="bg-white rounded-lg shadow-md p-12 text-center">
								<div className="text-gray-400 text-6xl mb-4">🔍</div>
								<h3 className="text-xl font-semibold text-gray-700 mb-2">没有找到相关内容</h3>
								<p className="text-gray-500">请尝试使用不同的关键词搜索</p>
							</div>
						) : (
							Object.entries(currentData).map(([categoryName, items]) => {
								// 根据分类名查找对应的ID和图标
								const categoryInfo = categories.find((cat: Category) => 
									cat.label === categoryName || 
									cat.id === categoryName.toLowerCase().replace(/\s+/g, '-')
								)
								const categoryId = categoryInfo?.id || categoryName.toLowerCase().replace(/\s+/g, '-')
								const categoryIcon = categoryInfo?.icon || faBook
								
								return (
									<div key={categoryName} id={categoryId} className="bg-white rounded-lg shadow-md p-6 mb-6">
										<div className="border-l-4 border-indigo-600 pl-4 py-1 mb-4">
											<h2 className="text-xl font-semibold text-gray-800 flex items-center">
												<FontAwesomeIcon icon={categoryIcon} className="mr-2 text-indigo-600" />
												{categoryName}
											</h2>
										</div>
										
										<div className="space-y-6">
											{items.map((item, index) => (
												<div key={index}>
													<MarkdownRenderer
														content={item.answer}
														className="text-gray-700"
														codeTheme="light"
														format="auto"
													/>
												</div>
											))}
										</div>
									</div>
								)
							})
						)}

						{/* 快速链接卡片 */}
						{!searchTerm && (
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
								<Link
									href="/faq"
									className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow group"
								>
									<div className="flex items-center justify-between">
										<div>
											<h3 className="text-lg font-semibold text-gray-800 mb-2">常见问题</h3>
											<p className="text-gray-600 text-sm">查看最常见的问题和解答</p>
										</div>
										<FontAwesomeIcon 
											icon={faArrowRight} 
											className="text-indigo-600 group-hover:translate-x-1 transition-transform" 
										/>
									</div>
								</Link>
								
								<Link
									href="/contact"
									className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow group"
								>
									<div className="flex items-center justify-between">
										<div>
											<h3 className="text-lg font-semibold text-gray-800 mb-2">联系我们</h3>
											<p className="text-gray-600 text-sm">获得一对一的专业支持</p>
										</div>
										<FontAwesomeIcon 
											icon={faArrowRight} 
											className="text-indigo-600 group-hover:translate-x-1 transition-transform" 
										/>
									</div>
								</Link>
							</div>
						)}
					</div>
				</div>
			</div>
		</div>
	)
} 