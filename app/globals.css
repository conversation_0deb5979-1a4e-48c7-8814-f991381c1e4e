@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* 导航栏吸顶动画 */
@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.fixed.top-0.left-0 {
  animation: slideDown 0.3s ease-in-out;
}
.cm-editor.cm-focused {
  outline: none !important;
}
.ͼ2 .cm-gutters {
  background: #ffffff;
  border-right: none;
}

/* Markdown样式 */
.markdown-body code {
  display: inline;
  white-space: break-spaces;
  word-wrap: break-word;
  vertical-align: middle;
  font-family: monospace;
}

.markdown-body p code,
.markdown-body li code,
.markdown-body td code,
.markdown-body th code,
.markdown-body blockquote code {
  display: inline;
  vertical-align: baseline;
}

.markdown-body pre {
  margin: 1em 0;
}

.markdown-body pre code {
  display: block;
  overflow-x: auto;
  white-space: pre;
  padding: 1em;
  width: 100%;
}

/* 防止行内代码被截断 */
.markdown-body p, 
.markdown-body li,
.markdown-body td,
.markdown-body th,
.markdown-body blockquote {
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
}

/* 代码高亮行号样式 */
.markdown-body .linenumber {
  border-right: 1px solid #4d4d4d;
  margin-right: 0.6em !important;
}

.markdown-body pre {
  border-radius: 0.375rem;
  margin: 0;
}

/* 提高代码块的可读性和紧凑性 */
.markdown-body pre > div {
  padding: 0!important;
}

/* 代码块容器样式 */
.code-block-wrapper {
  margin-top: 0rem !important;
  margin-bottom: 0rem !important;
  position: relative;
  overflow: hidden; /* 确保圆角效果不被子元素破坏 */
  border-radius: 0.375rem;
  border: none !important;
}

/* 确保代码块本身没有额外的margin */
.code-highlight-block {
  margin: 0 !important;
  border-radius: 0 !important;
  border: none !important; 
}

/* 语言标签样式 */
.code-language-tag {
  border-bottom-right-radius: 0.375rem;
  font-family: monospace;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* 复制按钮样式 */
.code-copy-btn {
  border-bottom-left-radius: 0.375rem;
}

/* 确保代码块和语言标签无缝融合 */
.code-block-wrapper > div {
  box-sizing: border-box !important;
}

/* React Syntax Highlighter覆盖样式 */
.code-block-wrapper pre {
  margin: 0 !important;
}

/* 代码块内部样式 */
.code-block-wrapper > div {
  border-radius: 0.375rem !important;
  overflow: hidden !important;
}

/* 确保代码高亮容器不被添加额外边距 */
.code-block-wrapper > div:first-child {
  margin: 0 !important;
}

/* 深色主题代码块 */
.markdown-body .dark-code,
.code-highlight-block {
  background-color: #1e1e1e !important; /* VSCode dark theme background */
  color: #d4d4d4 !important; /* VSCode dark theme text color */
}

/* 强制确保语法高亮正确显示 */
.markdown-body pre[class*="language-"],
.markdown-body .prism-code {
  background-color: #1e1e1e !important;
  margin: 0 !important;
  padding-top: 2.2rem !important;
}

/* 手动覆盖内部pre的样式 */
.code-highlight-block > pre {
  margin: 0 !important;
  background-color: transparent !important;
}

/* 重置内部元素样式，避免干扰 */
.code-block-wrapper * {
  box-sizing: border-box;
}

/* 手动覆盖任何可能干扰主题的样式 */
.react-syntax-highlighter-line-number {
  color: #606060 !important;
}

/* 代码块复制按钮和语言标签样式 */
@media (max-width: 640px) {
  .markdown-body .group:hover .opacity-0 {
    opacity: 1 !important;
  }
  
  .markdown-body .group button {
    font-size: 0.7rem !important;
    padding: 0.1rem 0.5rem !important;
  }
}

.markdown-body .group:hover button {
  opacity: 1 !important;
}

/* 确保复制按钮的过渡效果 */
.markdown-body .group button {
  transition: all 0.2s ease-in-out;
}

.markdown-body .group button:active {
  transform: scale(0.95);
}

/* 代码高亮主题强制样式 */
.markdown-body .dark-code {
  background-color: #1e1e1e !important; /* VSCode dark theme background */
  color: #d4d4d4 !important; /* VSCode dark theme text color */
}

.markdown-body .light-code {
  background-color: #f8f8f8 !important;
  color: #333 !important;
}
