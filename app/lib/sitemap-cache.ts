// sitemap 缓存系统
interface ProblemIdsCache {
  basicIds: string[]
  codingIds: string[]
  lastUpdate: number
}

let cache: ProblemIdsCache | null = null
// const CACHE_DURATION = 1000 * 60 * 10 // 10分钟缓存（开发环境）
const CACHE_DURATION = 1000 * 60 * 60 * 24 // 24小时缓存（生产环境）

/**
 * 获取缓存的题目ID列表
 */
export async function getCachedProblemIds(): Promise<ProblemIdsCache> {
  const now = Date.now()
  
  // 如果缓存存在且未过期，直接返回
  if (cache && (now - cache.lastUpdate) < CACHE_DURATION) {
    console.log('[Sitemap Cache] 使用缓存数据')
    return cache
  }
  
  console.log('[Sitemap Cache] 缓存过期或不存在，重新获取数据')
  
  // 重新获取数据
  const basicIds = await fetchBasicProblemIds()
  const codingIds = await fetchCodingProblemIds()
  
  // 更新缓存
  cache = {
    basicIds,
    codingIds,
    lastUpdate: now
  }
  
  return cache
}

/**
 * 获取基础题目ID列表（内部函数）
 */
async function fetchBasicProblemIds(): Promise<string[]> {
  try {
    const apiUrl = 'https://next.xjxq.club/api/next'
    const response = await fetch(`${apiUrl}/subject/list`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        pageNum: 1,
        pageSize: 100 // 减少数量，避免太多页面
      }),
      signal: AbortSignal.timeout(10000)
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    if (data.code === 1 && data.data?.items) {
      const problemIds = data.data.items.map((problem: any) => problem.id.toString())
      console.log(`[API] 获取到 ${problemIds.length} 个基础题目ID`)
      return problemIds
    } else {
      throw new Error(`API返回错误: ${data.msg || '未知错误'}`)
    }
  } catch (error) {
    console.warn('[API] 获取基础题目ID失败，使用模拟数据:', error)
    
    // 降级到模拟数据
    return [
      '1', '2', '3', '4', '5', '6', '7', '8', '9', '10',
      '11', '12', '13', '14', '15', '16', '17', '18', '19', '20'
    ]
  }
}

/**
 * 获取编程题目ID列表（内部函数）
 */
async function fetchCodingProblemIds(): Promise<string[]> {
  try {
    const apiUrl = 'https://next.xjxq.club/api/next'
    const response = await fetch(`${apiUrl}/problem/list`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        pageNum: 1,
        pageSize: 100 // 减少数量，避免太多页面
      }),
      signal: AbortSignal.timeout(10000)
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    if (data.code === 1 && data.data?.items) {
      const problemIds = data.data.items.map((problem: any) => problem.pid || problem.problemId)
      console.log(`[API] 获取到 ${problemIds.length} 个编程题目ID`)
      return problemIds
    } else {
      throw new Error(`API返回错误: ${data.msg || '未知错误'}`)
    }
  } catch (error) {
    console.warn('[API] 获取编程题目ID失败，使用模拟数据:', error)
    
    // 降级到模拟数据
    return [
      '1', '2', '3', '4', '5', '6', '7', '8', '9', '10',
      '11', '12', '13', '14', '15', '16', '17', '18', '19', '20'
    ]
  }
}

/**
 * 清除缓存（用于测试或强制刷新）
 */
export function clearSitemapCache(): void {
  cache = null
  console.log('[Sitemap Cache] 缓存已清除')
} 