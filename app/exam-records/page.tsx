'use client';

import React, {useEffect, useState} from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, ArcElement, Title, Tooltip, Legend, Filler } from 'chart.js';
import { Line, Doughnut } from 'react-chartjs-2';
import Link from 'next/link';
import ExamService, {ExamRecord, ExamStat} from "@/app/service/exam-service";
import {EXAM_DIFFICULTY, EXAM_STATUS} from "@/app/utils/constant";
import Pagination from "@/app/components/Pagination";

// 注册 ChartJS 组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

export default function ExamRecords() {
  const [filterStatus, setFilterStatus] = useState('all');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(5)
  const [total, setTotal] = useState<number>(0)
  const [records, setRecords] = useState<ExamRecord[]>([])
  const [loading, setLoading] = useState<boolean>(false)
  const [examStat, setExamStat] = useState<ExamStat | null>(null)
  const [statLoading, setStatLoading] = useState<boolean>(false)

  const fetchRecords = () => {
    setLoading(true)
    let status = null
    switch (filterStatus) {
      case 'inProgress':
        status = 0;
        break;
      case 'inScoring':
        status = 1;
        break;
      case 'completed':
        status = 2;
        break;
    }
    const params = {
      pageNum: currentPage,
      pageSize: pageSize,
      status
    }
    ExamService.getRecords(params).then(resp => {
      setRecords(resp.data.items || [])
      setTotal(resp.data.total || 0)
    }).finally(() => {
      setLoading(false)
    })
  }

  const fetchExamStat = () => {
    setStatLoading(true)
    ExamService.getExamStat().then(resp => {
      setExamStat(resp.data)
    }).finally(() => {
      setStatLoading(false)
    })
  }

  useEffect(() => {
    fetchRecords()
    fetchExamStat()
  }, [currentPage, pageSize, filterStatus])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  // 考试成绩走势图数据
  const scoreChartData = {
    labels: examStat?.recentlyExam && examStat.recentlyExam.length > 0
      ? examStat.recentlyExam.map(exam => {
          const date = new Date(exam.date)
          return `${date.getMonth() + 1}月${date.getDate()}日`
        })
      : [],
    datasets: [{
      label: '考试得分率',
      data: examStat?.recentlyExam && examStat.recentlyExam.length > 0
        ? examStat.recentlyExam.map(exam => exam.scoringRate)
        : [],
      backgroundColor: 'rgba(99, 102, 241, 0.1)',
      borderColor: 'rgba(99, 102, 241, 1)',
      borderWidth: 2,
      pointBackgroundColor: 'rgba(99, 102, 241, 1)',
      tension: 0.3,
      fill: true
    }]
  };

  // 考试成绩走势图配置
  const scoreChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: false,
        min: 0,
        max: 100,
        ticks: {
          callback: function(value: any) {
            return value + '%';
          }
        }
      }
    },
    plugins: {
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return '得分率: ' + context.parsed.y + '%';
          }
        }
      }
    }
  };

  // 考试类型分析图数据
  const typeChartData = {
    labels: ['入门测试', '基础测试', '期中测试', '期末测试', '竞赛模拟'],
    datasets: [{
      data: [5, 4, 3, 2, 4],
      backgroundColor: [
        'rgba(16, 185, 129, 0.7)',
        'rgba(59, 130, 246, 0.7)',
        'rgba(251, 191, 36, 0.7)',
        'rgba(139, 92, 246, 0.7)',
        'rgba(79, 70, 229, 0.7)'
      ],
      borderColor: [
        'rgba(16, 185, 129, 1)',
        'rgba(59, 130, 246, 1)',
        'rgba(251, 191, 36, 1)',
        'rgba(139, 92, 246, 1)',
        'rgba(79, 70, 229, 1)'
      ],
      borderWidth: 1
    }]
  };

  // 考试类型分析图配置
  const typeChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          padding: 20,
          boxWidth: 12
        }
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return context.label + ': ' + context.parsed + '次测试';
          }
        }
      }
    }
  };

  const getStatusStyle = (status: number) => {
    return EXAM_STATUS[status as keyof typeof EXAM_STATUS]?.style || ''
  }

  const getDifficultyColor = (difficulty: number) => {
    switch (difficulty) {
      case 1:
        return 'bg-green-100 text-green-800';
      case 2:
        return 'bg-blue-100 text-blue-800';
      case 3:
        return 'bg-red-100 text-red-800';
      case 4:
        return 'bg-indigo-100 text-indigo-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // ExamRecord接口中可能没有id字段，需要使用一个临时字段存储记录ID
  const getRecordId = (record: ExamRecord, index: number) => {
    // 使用一个假的ID，实际应用中应该从后端API获取
    return `record-${index}`;
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 顶部标题和筛选器 */}
      <div className="mb-8 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">考试记录</h1>
          <p className="text-gray-600 mt-1">查看历史考试记录与成绩分析</p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-2">
          <button
            className={`px-4 py-2 ${filterStatus === 'all' ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700 border border-gray-300'} rounded-lg text-sm`}
            onClick={() => setFilterStatus('all')}
          >
            全部记录
          </button>
          <button
            className={`px-4 py-2 ${filterStatus === 'inProgress' ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700 border border-gray-300'} rounded-lg text-sm`}
            onClick={() => setFilterStatus('inProgress')}
          >
            进行中
          </button>
          <button
            className={`px-4 py-2 ${filterStatus === 'inScoring' ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700 border border-gray-300'} rounded-lg text-sm`}
            onClick={() => setFilterStatus('inScoring')}
          >
            评分中
          </button>
          <button
            className={`px-4 py-2 ${filterStatus === 'completed' ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700 border border-gray-300'} rounded-lg text-sm`}
            onClick={() => setFilterStatus('completed')}
          >
            已完成
          </button>
        </div>
      </div>

      {/* 考试记录统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-3xl font-bold text-indigo-600">{examStat?.examCount || '-'}</div>
              <div className="text-sm text-gray-500 mt-1">考试总数</div>
            </div>
            <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-3xl font-bold text-green-600">{examStat?.scoringRate ? `${examStat.scoringRate}%` : '-'}</div>
              <div className="text-sm text-gray-500 mt-1">平均得分率</div>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
              </svg>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-3xl font-bold text-blue-600">{examStat?.maxScore || '-'}</div>
              <div className="text-sm text-gray-500 mt-1">最高分数</div>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
              </svg>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-3xl font-bold text-yellow-600">{examStat?.useHours.toFixed(1) || '-'}</div>
              <div className="text-sm text-gray-500 mt-1">累计学时</div>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* 考试成绩走势图 */}
      <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
        <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100">
          <h3 className="text-lg font-semibold text-gray-800">考试成绩走势</h3>
        </div>
        <div className="p-6">
          <div className="h-64">
            {examStat?.recentlyExam && examStat.recentlyExam.length > 0 ? (
              <Line data={scoreChartData} options={scoreChartOptions} />
            ) : (
              <div className="flex justify-center items-center h-full">
                <p className="text-gray-500">暂无考试记录</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 考试记录列表 */}
      <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
        <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-800">考试记录列表</h3>
            <div className="relative">
              <input
                type="text"
                placeholder="搜索考试记录..."
                className="px-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              />
              <button className="absolute right-0 top-0 mt-2 mr-3 text-gray-400 hover:text-indigo-600">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>
            </div>
          </div>
        </div>
        <div className="overflow-x-auto relative">
          {loading ? (
            <div className="flex justify-center items-center py-20">
              <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
              <span className="ml-3 text-gray-600">加载中...</span>
            </div>
          ) : (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">考试名称</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">难度</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">得分/总分</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">完成时间</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用时</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {records.length > 0 ? records.map((record, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {/* <div className="flex-shrink-0 h-10 w-10">
                          <img className="h-10 w-15" src={'https:' + record.banner} alt="考试封面" />
                        </div> */}
                        <div>
                          <div className="text-sm font-medium text-gray-900">{record.title}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-indigo-100 text-indigo-800">{record.categoryName}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getDifficultyColor(record.difficulty)}`}>
                        {EXAM_DIFFICULTY[(record.difficulty || 1) as keyof typeof EXAM_DIFFICULTY]?.text || '未知难度'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{record.status == 2 ? record.score : '-'}/{record.totalScore}</div>
                      <div className="text-xs text-gray-500">{record.status == 2 ? (record.score / record.totalScore * 100).toFixed(0) + '%' : '-'}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {record.submitTime || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {record.useTime}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusStyle(record.status)}`}>
                        {EXAM_STATUS[record.status as keyof typeof EXAM_STATUS]?.text || '未知状态'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      {
                        record.status === 0 && <Link href={`/exams/${record.testPaperId}`} className="text-indigo-600 hover:text-indigo-900 mr-3">查看详情</Link>
                      }
                      {
                        record.status === 2 && <Link href={`/exam-records/${record.id}`} className="text-indigo-600 hover:text-indigo-900 mr-3">查看详情</Link>
                      }
                      {/*{record.status === 2 && <Link href={`/exam-records/${getRecordId(record, index)}/wrong-problems`} className="text-gray-600 hover:text-gray-900">查看错题</Link>}*/}
                    </td>
                  </tr>
                )) : (
                  <tr>
                    <td colSpan={8} className="px-6 py-10 text-center text-gray-500">
                      暂无考试记录
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          )}
        </div>
        <div className="px-6 py-2 bg-gray-50">
          <Pagination
            currentPage={currentPage}
            totalItems={total}
            pageSize={pageSize}
            onPageChange={handlePageChange}
          />
        </div>
      </div>
    </div>
  );
}
