'use client';

import React, {useEffect, useState} from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, RadialLinearScale, PointElement, LineElement, Title, Tooltip, Legend } from 'chart.js';
import { Bar, Radar } from 'react-chartjs-2';
import ExamService, {ExamProblem, Option, RecordDetail} from "@/app/service/exam-service";
import {useParams} from "next/navigation";
import {EXAM_DIFFICULTY, JUDGE_STATUS} from "@/app/utils/constant";
import {formatKb} from "@/app/utils/number-utils";
import MarkdownRenderer from "@/app/components/MarkdownRenderer";
import { checkAuth } from '@/app/utils/authCheck';
import { useRouter } from 'next/navigation';
import { useAppSelector } from '@/app/redux/hooks';
import { selectUser } from '@/app/redux/features/authSlice';

// 注册 ChartJS 组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  RadialLinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface Section {
  title: string;
  score: number;
  totalScore: number;
  // 题目类型 1: 单选题 2: 多选题 3: 判断题 4: 编程题
  type: number;
  questions: ExamProblem[];
  correct: number;
}

// 新增Modal组件
function ProblemModal({ isOpen, onClose, problem }: { isOpen: boolean, onClose: () => void, problem: ExamProblem | null }) {
  if (!isOpen || !problem) return null;

  // 提取用户答案和正确答案
  const userAnswer = problem.value || '';
  const rightAnswer = problem.rightAnswer || '';

  // 判断题目类型
  const isProgramming = problem.problemType === 4;
  const isMultipleChoice = problem.problemType === 2;

  // 判断选择题的用户选择和正确选项
  const userSelections = isMultipleChoice ?
    userAnswer.split(',') :
    [userAnswer];

  const correctOptions = isMultipleChoice ?
    rightAnswer.split(',') :
    [rightAnswer];


  interface ProblemExample {
    input: string;
    output: string;
  }
  const getExamples = (value: string) => {
    const reg = '<input>([\\s\\S]*?)</input><output>([\\s\\S]*?)</output>'
    const re = RegExp(reg, 'g')
    const objList: ProblemExample[] = []
    let tmp = re.exec(value)
    while (tmp) {
      objList.push({input: tmp[1], output: tmp[2]})
      tmp = re.exec(value)
    }
    return objList
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-3xl max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 bg-indigo-50 px-6 py-4 border-b border-indigo-200 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-800">
            {problem.problemType === 1 ? '单选题' :
             problem.problemType === 2 ? '多选题' :
             problem.problemType === 3 ? '判断题' : '编程题'} - 第{problem.no}题
          </h3>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="p-6">
          {/* 题目内容 */}
          <div className="mb-6">
            <h4 className="text-lg font-medium text-gray-900 mb-3">题目内容</h4>
            <div className="bg-gray-50 p-4 rounded-lg">
              {problem.problemType === 1 && problem.title}
              {
                problem.description && problem.problemType === 1 && <div className="mt-4" dangerouslySetInnerHTML={{__html: problem.description || ''}}></div>
              }

              {
                problem.description && problem.problemType === 4 &&
                <>
                  <h2 className="text-lg font-semibold text-gray-800 mb-2">题目描述</h2>
                  <MarkdownRenderer content={problem.description} />
                  <h2 className="text-lg font-semibold text-gray-800 mb-2">输入格式</h2>
                  <MarkdownRenderer content={problem?.input || ''}/>
                  <h2 className="text-lg font-semibold text-gray-800 mb-2">输出格式</h2>
                  <MarkdownRenderer content={problem?.output || ''}/>
                  {getExamples(problem.examples).map((example, index) => (
                    <div key={index} className="mb-4">
                      <h2 className="text-lg font-semibold text-gray-800 mb-2">示例 {index + 1}</h2>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                          <h3 className="font-medium text-gray-700 mb-1">输入:</h3>
                          <div className="bg-gray-100 p-3 rounded-md">
																	<pre
                                    className="text-gray-800 whitespace-pre-wrap text-sm font-mono">{example.input}</pre>
                          </div>
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-700 mb-1">输出:</h3>
                          <div className="bg-gray-100 p-3 rounded-md">
																	<pre
                                    className="text-gray-800 whitespace-pre-wrap text-sm font-mono">{example.output}</pre>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </>
              }
            </div>

          </div>

          {/* 选项 */}
          {!isProgramming && problem.optionArray && (
            <div className="mb-6">
              <h4 className="text-lg font-medium text-gray-900 mb-3">选项</h4>
              <div className="space-y-3">
                {problem.optionArray.map((option) => (
                  <div
                    key={option.id}
                    className={`p-3 rounded-lg flex items-start ${
                      userSelections.includes(option.id) && correctOptions.includes(option.id)
                        ? 'bg-green-50 border border-green-200'
                        : userSelections.includes(option.id) && !correctOptions.includes(option.id)
                        ? 'bg-red-50 border border-red-200'
                        : !userSelections.includes(option.id) && correctOptions.includes(option.id)
                        ? 'bg-blue-50 border border-blue-200'
                        : 'bg-gray-50 border border-gray-200'
                    }`}
                  >
                    <div className="mr-3 text-gray-700 font-medium">{option.id}.</div>
                    <div className="flex-1" dangerouslySetInnerHTML={{__html: option.content || ''}}></div>
                    <div className="ml-2">
                      {userSelections.includes(option.id) && correctOptions.includes(option.id) && (
                        <span className="text-green-600 text-xl">✓</span>
                      )}
                      {userSelections.includes(option.id) && !correctOptions.includes(option.id) && (
                        <span className="text-red-600 text-xl">✗</span>
                      )}
                      {!userSelections.includes(option.id) && correctOptions.includes(option.id) && (
                        <span className="text-blue-600 text-sm">正确答案</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* todo 编程题的输入输出样例 */}

          {/* 答案 */}
          {
            !isProgramming && <div className="mb-6">
              <h4 className="text-lg font-medium text-gray-900 mb-3">答案</h4>
              <div className="flex space-x-6">
                <div className="bg-gray-50 p-4 rounded-lg flex-1">
                  <p className="text-sm text-gray-500 mb-1">你的答案</p>
                  <p className="font-medium text-gray-900">
                    {userAnswer}
                  </p>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg flex-1">
                  <p className="text-sm text-gray-500 mb-1">正确答案</p>
                  <p className="font-medium text-gray-900">{rightAnswer}</p>
                </div>
              </div>
            </div>
          }

          {/* 解析 */}
          {problem.explanations && (
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-3">解析</h4>
              <div className="bg-gray-50 p-4 rounded-lg">
                {problem.explanations}
              </div>
            </div>
          )}
        </div>

        <div className="bg-gray-50 px-6 py-4 border-t border-gray-200 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
}

// 新增ResultModal组件用于显示结果
function ResultModal({ isOpen, onClose, problem }: { isOpen: boolean, onClose: () => void, problem: ExamProblem | null }) {
  if (!isOpen || !problem) return null;

  // 获取代码和编程语言
  const code = problem.value || '';
  const language = problem.language || '-';

  // 执行状态
  const status = problem.value ? (JUDGE_STATUS as any)[String(problem.judge.status)]?.name : '未作答';

  // 这里可以根据实际情况添加执行时间和内存占用，这里使用模拟数据
  const executionTime = problem.judge?.time;  // 实际应用中应该从problem中获取
  const memoryUsage = problem.judge?.memory;  // 实际应用中应该从problem中获取

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-3xl max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 bg-indigo-50 px-6 py-4 border-b border-indigo-200 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-800">
            执行结果 - {problem.title}
          </h3>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="p-6">
          {/* 结果信息表格 */}
          <div className="grid grid-cols-4 gap-4 mb-6">
            <div className="p-4 rounded-lg bg-gray-50">
              <h4 className="text-sm text-gray-500 mb-1">状态</h4>
              <div className={`inline-block px-3 py-1 rounded-md text-sm font-medium ${problem.isAc ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                {status}
              </div>
            </div>
            <div className="p-4 rounded-lg bg-gray-50">
              <h4 className="text-sm text-gray-500 mb-1">语言</h4>
              <div className="text-gray-900 font-medium">{language}</div>
            </div>
            <div className="p-4 rounded-lg bg-gray-50">
              <h4 className="text-sm text-gray-500 mb-1">执行时间</h4>
              <div className="text-gray-900 font-medium">{executionTime ? executionTime + 'ms' : '-'}</div>
            </div>
            <div className="p-4 rounded-lg bg-gray-50">
              <h4 className="text-sm text-gray-500 mb-1">内存占用</h4>
              <div className="text-gray-900 font-medium">{memoryUsage ? formatKb(memoryUsage) : '-'}</div>
            </div>
          </div>

          {/* 提交代码 */}
          { problem.value && (
            <div className="mb-6">
              <div className="flex justify-between items-center mb-3">
                <h4 className="text-lg font-medium text-gray-900">提交代码</h4>
                <button
                  onClick={() => navigator.clipboard.writeText(code)}
                  className="text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-md transition"
                >
                  复制
                </button>
              </div>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
              <pre className="whitespace-pre-wrap">
                <code>{code}</code>
              </pre>
              </div>
            </div>
          )}
        </div>

        <div className="bg-gray-50 px-6 py-4 border-t border-gray-200 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
}

export default function ExamRecordDetail() {
  const params = useParams<{id: string}>()
  const [record, setRecord] = useState<RecordDetail>()
  const [sections, setSections] = useState<Section[]>()
  const [totalCorrect, setTotalCorrect] = useState<number>()
  const [totalWrong, setTotalWrong] = useState<number>()
  const [answersChartData, setAnswersChartData] = useState<any>(null)
  const [statusFilter, setStatusFilter] = useState<string>('all')

  // 添加Modal相关状态
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedProblem, setSelectedProblem] = useState<ExamProblem | null>(null);


  // 添加结果Modal相关状态
  const [resultModalOpen, setResultModalOpen] = useState(false);
  const [selectedResultProblem, setSelectedResultProblem] = useState<ExamProblem | null>(null);

  useEffect(() => {
    ExamService.getRecordDetail(params.id).then(resp => {
      console.log(resp)
      setRecord(resp.data)
      initializeData(resp.data)
    })
  }, [])

  const initializeData = (record: RecordDetail) => {
    const detail = record.detail
    const sections: Section[] = []
    let totalCorrect = 0
    let totalWrong = 0
    let no = 1;
    if (detail.singleChoiceList && detail.singleChoiceList.length > 0) {
      const questions = detail.singleChoiceList
      let score = 0
      let correct = 0
      questions.forEach((question) => {
        const optionsJson = question.options
        const options: Option[] = JSON.parse(optionsJson)
        options.forEach((option: Option, index: number) => {
          option.id = String.fromCharCode(65 + index)
        })
        question.optionArray = options
        question.score = detail.singleChoiceScore
        score += question.userScore
        if (question.isAc) {
          correct++
          totalCorrect++
        } else {
          totalWrong++
        }
        question.no = no++
      })
      sections.push({
        title: '单选题',
        score: score,
        totalScore: detail.singleChoiceScore * detail.singleChoiceList.length,
        questions: questions,
        type: 1,
        correct: correct
      })
    }
    if (detail.multipleChoiceList && detail.multipleChoiceList.length > 0) {
      let score = 0
      const questions = detail.multipleChoiceList
      let correct = 0
      questions.forEach((question) => {
        const optionsJson = question.options
        const options: Option[] = JSON.parse(optionsJson)
        options.forEach((option: Option, index: number) => {
          option.id = String.fromCharCode(65 + index)
        })
        question.optionArray = options
        question.score = detail.multipleChoiceScore
        score += question.userScore
        if (question.isAc) {
          correct++
          totalCorrect++
        } else {
          totalWrong++
        }
        question.no = no++
      })
      sections.push({
        title: '多选题',
        score: score,
        totalScore: detail.multipleChoiceScore * detail.multipleChoiceList.length,
        questions: questions,
        type: 2,
        correct: correct
      })
    }
    if (detail.trueOrFalseList && detail.trueOrFalseList.length > 0) {
      let score = 0
      const questions = detail.trueOrFalseList
      let correct = 0
      questions.forEach((question) => {
        question.optionArray = [
          {id: 'A', content: '正确'},
          {id: 'B', content: '错误'}
        ]
        question.score = detail.trueOrFalseScore
        score += question.userScore
        if (question.isAc) {
          correct++
          totalCorrect++
        } else {
          totalWrong++
        }
        question.no = no++
      })
      sections.push({
        title: '判断题',
        score: score,
        totalScore: detail.trueOrFalseScore * detail.trueOrFalseList.length,
        questions: questions,
        type: 3,
        correct: correct
      })
    }
    if (detail.ojList && detail.ojList.length > 0) {
      let score = 0
      let correct = 0
      const questions = detail.ojList
      questions.forEach((question) => {
        question.score = detail.ojScore
        score += question.userScore
        if (question.isAc) {
          correct++
          totalCorrect++
        } else {
          totalWrong++
        }
        question.no = no++
      })
      sections.push({
        title: '编程题',
        score: score,
        totalScore: detail.ojScore * detail.ojList.length,
        questions: detail.ojList,
        type: 4,
        correct: correct
      })
    }

    setSections(sections)
    setTotalCorrect(totalCorrect)
    setTotalWrong(totalWrong)

    const bgColors = ['rgba(16, 185, 129, 0.7)', 'rgba(99, 102, 241, 0.7)', 'rgba(248,156,17, 0.7)', 'rgba(0,124,255, 0.7)']
    const borderColors = ['rgba(16, 185, 129, 1)', 'rgba(99, 102, 241, 1)', 'rgba(248,156,17, 1)', 'rgba(0,124,255, 1)']
    const labels = sections.map(section => section.title)
    const dataset = {
      label: '得分情况',
      data: sections.map(section => section.score),
      backgroundColor: bgColors.slice(0, sections.length),
      borderColor: borderColors.slice(0, sections.length),
      borderWidth: 1,
      barPercentage: 0.4
    }
    console.log({
      labels: labels,
      datasets: [dataset]
    })
    console.log({
      labels: labels,
      datasets: [dataset]
    })
    setAnswersChartData({
      labels: labels,
      datasets: [dataset]
    })
    setAnswerChartOptions({
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: '分数'
          }
        }
      },
      plugins: {
        tooltip: {
          callbacks: {
            label: function(context: any) {
              const maxScores = sections.map(section => section.totalScore);
              return context.dataset.label + ': ' + context.raw + '/' + maxScores[context.dataIndex] + '分';
            }
          }
        }
      }
    })
  }

  // 答题情况分析图配置
  const [answersChartOptions, setAnswerChartOptions] = useState<any>(null);

  const getDifficultyColor = (difficulty: number | null) => {
    switch (difficulty) {
      case 1:
        return 'bg-green-100 text-green-800';
      case 2:
        return 'bg-blue-100 text-blue-800';
      case 3:
        return 'bg-red-100 text-red-800';
      case 4:
        return 'bg-indigo-100 text-indigo-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  function getQuestions(questions: ExamProblem[]) {
    if (statusFilter === 'all') {
      return questions
    } else if (statusFilter === 'correct') {
      return questions.filter(question => question.isAc)
    } else {
      return questions.filter(question => !question.isAc)
    }
  }

  // 处理题目点击事件
  const handleProblemClick = (problem: ExamProblem) => {
    setSelectedProblem(problem);
    setModalOpen(true);
  };

  // 处理查看结果点击事件
  const handleViewResultClick = (problem: ExamProblem) => {
    setSelectedResultProblem(problem);
    setResultModalOpen(true);
  };
  const router = useRouter()

  const user = useAppSelector(selectUser);
  const isUltraPlan = user?.currentMembershipLevel === 3;

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 返回与页面标题 */}
      <div className="mb-6 flex items-center">
        <Link href="/exam-records" className="flex items-center text-indigo-600 hover:text-indigo-800">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          <span>返回考试记录</span>
        </Link>
        <div className="mx-4 text-gray-300">|</div>
        <h1 className="text-2xl font-bold text-gray-800">考试记录详情</h1>
      </div>

      {/* 考试信息概览卡片 */}
      <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
        <div className="md:flex">
          {/*<div className="md:flex-shrink-0">*/}
          {/*  {record && <Image*/}
          {/*    className="h-48 w-full object-cover md:w-48"*/}
          {/*    src={'https:' + record?.detail.banner}*/}
          {/*    alt="考试封面"*/}
          {/*    width={192}*/}
          {/*    height={192}*/}
          {/*  />}*/}
          {/*</div>*/}
          <div className="p-8 w-full">
            <div className="flex justify-between items-start">
              <div>
                <div className="flex items-center">
                  <h2 className="text-2xl font-bold text-gray-900">{record?.detail.title}</h2>
                  <span className="ml-3 px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded-full">{record?.detail.categoryName}</span>
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getDifficultyColor(record?.detail.difficulty || null)}`}>{record?.detail.difficulty ? (EXAM_DIFFICULTY as any)[record.detail.difficulty]?.text : ''}</span>
                </div>
                <p className="text-gray-600 text-sm mt-2">完成时间：{record?.submitTime} | 用时：{record?.useTime}</p>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-indigo-600">{record ? (record?.score / record?.detail.totalScore * 100).toFixed(0) : '-'}%</div>
                <div className="text-sm text-gray-500">{record?.score}/{record?.detail.totalScore}分</div>
              </div>
            </div>

            {sections && (
              <div className={`mt-6 grid grid-cols-2 gap-4 md:grid-cols-${sections.length + 2}`}>
                {
                  sections.map((section, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4 text-center">
                      <div className="text-lg font-semibold text-gray-900">{section.correct}/{section.questions.length}</div>
                      <div className="text-xs text-gray-500">{section.title}</div>
                    </div>
                  ))
                }
                <div className="bg-gray-50 rounded-lg p-4 text-center">
                  <div className="text-lg font-semibold text-green-600">{totalCorrect}</div>
                  <div className="text-xs text-gray-500">正确题数</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-4 text-center">
                  <div className="text-lg font-semibold text-red-600">{totalWrong}</div>
                  <div className="text-xs text-gray-500">错误题数</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 答题数据分析 */}
      <div className="grid md:grid-cols-1 gap-6 mb-8">
        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100">
            <h3 className="text-lg font-semibold text-gray-800">答题情况分析</h3>
          </div>
          <div className="p-6">
            <div className="h-64">
              {
                answersChartData && <Bar data={answersChartData} options={answersChartOptions} />
              }

            </div>
          </div>
        </div>

        {/*<div className="bg-white rounded-xl shadow-md overflow-hidden">*/}
        {/*  <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100">*/}
        {/*    <h3 className="text-lg font-semibold text-gray-800">知识点掌握情况</h3>*/}
        {/*  </div>*/}
        {/*  <div className="p-6">*/}
        {/*    <div className="h-64">*/}
        {/*      <Radar data={knowledgeChartData} options={knowledgeChartOptions} />*/}
        {/*    </div>*/}
        {/*  </div>*/}
        {/*</div>*/}
      </div>

      {/* 答题详情 */}
      <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
        <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-800">答题详情</h3>
            <div className="flex items-center">
              <button
                className={`px-4 py-2 rounded-lg text-sm mr-2 ${statusFilter === 'all' ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700'}`}
                onClick={() => setStatusFilter('all')}
              >全部</button>
              <button
                className={`px-4 py-2 rounded-lg text-sm mr-2 ${statusFilter === 'correct' ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700'}`}
                onClick={() => setStatusFilter('correct')}
              >正确</button>
              <button
                className={`px-4 py-2 rounded-lg text-sm ${statusFilter === 'wrong' ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700'}`}
                onClick={() => setStatusFilter('wrong')}
              >错误</button>
            </div>
          </div>
        </div>
        {
          sections && sections.map((section, index) => (
            section.type !== 4 ? (
              /* 选择题部分 */
              <div key={index} className="p-6 border-b border-gray-200">
                <h4 className="text-lg font-semibold text-gray-800 mb-4">第{['一', '二', '三', '四'][index]}部分：{section.title}（{section.questions.length}题）</h4>
                <div className="grid grid-cols-5 sm:grid-cols-10 gap-3">
                  {getQuestions(section.questions).map((question, qIndex) => (
                    <div
                      key={qIndex}
                      className={`${question.isAc ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'} font-medium rounded-full w-10 h-10 flex items-center justify-center cursor-pointer hover:opacity-80 transition-opacity`}
                      onClick={() => handleProblemClick(question)}
                    >
                      {question.no}
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              /* 编程题部分 */
              <div key={index} className="p-6">
                <h4 className="text-lg font-semibold text-gray-800 mb-4">第{['一', '二', '三', '四'][index]}部分：{section.title}（{section.questions.length}题）</h4>

                {getQuestions(section.questions).map((problem) => (
                  <div key={problem.id} className="mb-6 border border-gray-200 rounded-lg overflow-hidden">
                    <div className="flex items-center justify-between px-4 py-3 bg-gray-50">
                      <div className="flex items-center">
                        <div
                          className={`${problem.isAc ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'} font-medium rounded-full w-8 h-8 flex items-center justify-center mr-3 cursor-pointer hover:opacity-80 transition-opacity`}
                          onClick={() => handleProblemClick(problem)}
                        >
                          {problem.no}
                        </div>
                        <div>
                          <h5 className="font-medium text-gray-900">{problem.title}</h5>
                          <div className="flex items-center text-xs text-gray-500">
                            {problem.tags && problem.tags.map((tag: string, tagIndex: number) => (
                              <span key={tagIndex} className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full mr-2">
                          {tag}
                        </span>
                            ))}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <div className={`${problem.isAc? 'text-green-600' : 'text-red-600'} mr-4`}>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline mr-1" viewBox="0 0 20 20" fill="currentColor">
                            {problem.isAc ? (
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            ) : (
                              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                            )}
                          </svg>
                          {/*<span>{problem.isAc}</span>*/}
                        </div>
                        {/*<div className="text-gray-600 mr-4">*/}
                        {/*  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline mr-1" viewBox="0 0 20 20" fill="currentColor">*/}
                        {/*    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />*/}
                        {/*  </svg>*/}
                        {/*  <span>{problem.duration}</span>*/}
                        {/*</div>*/}
                        <div className="text-gray-600">
                          <span className="font-medium">{problem.userScore}/{problem.score}</span> 分
                        </div>
                      </div>
                    </div>
                    <div className="px-4 py-3 border-t border-gray-200">
                      <div className="flex items-center mb-2">
                        <button
                          className="text-xs text-indigo-600 font-medium mr-3"
                          onClick={() => handleProblemClick(problem)}
                        >
                          查看题目
                        </button>
                        <button
                          className="text-xs text-indigo-600 font-medium"
                          onClick={() => handleViewResultClick(problem)}
                        >
                          查看结果
                        </button>
                        {!problem.isAc && (problem.analysisStatus === 2 || !isUltraPlan) && (
                          <span className="ml-3 text-xs text-red-600 font-medium cursor-pointer" onClick={() => {
                            if (checkAuth(3)) {
                              router.push(`/exam-records/${params.id}/wrong-problems/${problem.recordId}`)
                            }
                          }}>
                            查看错题分析
                          </span>
                        )}
                        {!problem.isAc && problem.analysisStatus === 1 && (
                          <span className="ml-3 text-xs text-yellow-600 font-medium">
                            分析中...
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )
          ))
        }
      </div>

      {/* 题目详情Modal */}
      <ProblemModal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        problem={selectedProblem}
      />

      {/* 结果详情Modal */}
      <ResultModal
        isOpen={resultModalOpen}
        onClose={() => setResultModalOpen(false)}
        problem={selectedResultProblem}
      />
    </div>
  );
}
