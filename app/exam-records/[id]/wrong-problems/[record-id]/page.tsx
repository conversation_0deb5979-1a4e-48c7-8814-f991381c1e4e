'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import examService, { WrongAnalysisView } from '@/app/service/exam-service';
import MarkdownRenderer from '@/app/components/MarkdownRenderer';
import { getExamples } from '@/app/utils/problem-utils';

export default function WrongProblemAnalysis({
  params
}: {
  params: { id: string; 'record-id': string }
}) {
  const [loading, setLoading] = useState(true);
  const [analysisData, setAnalysisData] = useState<WrongAnalysisView | null>(null);
  const [error, setError] = useState<string | null>(null);

  // 获取错题分析数据
  useEffect(() => {
    const fetchAnalysisData = async () => {
      try {
        setLoading(true);
        const response = await examService.getWrongAnalysis(Number(params['record-id']));
        setAnalysisData(response.data);
        setError(null);
      } catch (err) {
        console.error('获取错题分析失败:', err);
        setError('获取数据失败，请稍后重试');
      } finally {
        setLoading(false);
      }
    };

    fetchAnalysisData();
  }, [params]);

  // 难度标签的样式
  const getDifficultyClass = (difficulty: string) => {
    if (difficulty === "简单难度") return "bg-green-100 text-green-800";
    if (difficulty === "中等难度") return "bg-yellow-100 text-yellow-800";
    return "bg-red-100 text-red-800"; // 困难难度
  };

  // 展示加载状态
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          <span className="ml-4 text-gray-600">加载中...</span>
        </div>
      </div>
    );
  }

  // 展示错误信息
  if (error || !analysisData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col items-center justify-center h-64">
          <div className="text-red-500 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <p className="text-gray-700">{error || '数据不存在'}</p>
          <Link href={`/exam-records/${params.id}`} className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
            返回考试详情
          </Link>
        </div>
      </div>
    );
  }

  const relatedProblems = [
    {
      id: 1,
      title: "最短路径问题",
      tags: ["图论", "最短路径"],
      difficulty: "中等难度",
      description: "给定一个带权图，求解从指定源点到其他所有顶点的最短路径。练习Dijkstra算法或Bellman-Ford算法。"
    },
    {
      id: 2,
      title: "网络连接问题",
      tags: ["图论", "最小生成树"],
      difficulty: "中等难度",
      description: "有n个城市需要通过网络连接起来，每两个城市之间的连接成本不同，求最小的连接所有城市的成本。"
    },
    {
      id: 3,
      title: "岛屿连通问题",
      tags: ["图论", "并查集"],
      difficulty: "简单难度",
      description: "给定一个二维网格地图，其中\"1\"表示陆地，\"0\"表示水域。计算网格中岛屿的数量（陆地连通块的数量）。"
    },
    {
      id: 4,
      title: "第K短路径",
      tags: ["图论", "优先队列"],
      difficulty: "困难难度",
      description: "给定一个带权有向图，找出从源点到目标点的第K短路径的长度。要求使用A*算法或Dijkstra变种算法。"
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 返回与页面标题 */}
      <div className="mb-6 flex items-center">
        <Link href={`/exam-records/${params.id}`} className="flex items-center text-indigo-600 hover:text-indigo-800">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          <span>返回考试详情</span>
        </Link>
        <div className="mx-4 text-gray-300">|</div>
        <h1 className="text-2xl font-bold text-gray-800">错题分析</h1>
      </div>

      {/* 错题详情 */}
      <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
        <div className="px-6 py-4 bg-red-50 border-b border-red-100">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <h3 className="text-lg font-semibold text-gray-800">{analysisData.title}</h3>
              <span className="ml-3 px-2 py-0.5 rounded-full bg-red-100 text-red-800 text-xs">未通过</span>
            </div>
          </div>
        </div>

        {/* 题目描述 */}
        <div className="p-6 border-b border-gray-200">
          <div className="mb-4">
            <h4 className="text-base font-semibold text-gray-900 mb-2">题目描述</h4>
            <div className="text-gray-700 text-sm space-y-3">
              <MarkdownRenderer content={analysisData.output} />
            </div>
          </div>

          <div className="mb-4">
            <h4 className="text-base font-semibold text-gray-900 mb-2">输入格式</h4>
            <div className="text-gray-700 text-sm">
              <MarkdownRenderer content={analysisData.input} />
            </div>
          </div>

          <div className="mb-4">
            <h4 className="text-base font-semibold text-gray-900 mb-2">输出格式</h4>
            <div className="text-gray-700 text-sm">
              <MarkdownRenderer content={analysisData.output} />
            </div>
          </div>

          {analysisData.examples && (
            <>
              <div className="mb-4">
                <h4 className="text-base font-semibold text-gray-900 mb-2">样例</h4>
                {getExamples(analysisData.examples).map((example, index) => (
                  <div key={index} className="mb-4">
                    <h2 className="text-lg font-semibold text-gray-800 mb-2">示例 {index + 1}</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <div>
                        <h3 className="font-medium text-gray-700 mb-1">输入:</h3>
                        <div className="bg-gray-100 p-3 rounded-md">
                          <pre
                            className="text-gray-800 whitespace-pre-wrap text-sm font-mono">{example.input}</pre>
                        </div>
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-700 mb-1">输出:</h3>
                        <div className="bg-gray-100 p-3 rounded-md">
                          <pre
                            className="text-gray-800 whitespace-pre-wrap text-sm font-mono">{example.output}</pre>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}

          {analysisData.hint && (
            <div>
              <h4 className="text-base font-semibold text-gray-900 mb-2">提示</h4>
              <MarkdownRenderer content={analysisData.hint} />
            </div>
          )}
        </div>

        {/* 你的解答 - 仅在用户提交代码时显示 */}
        {analysisData.code && (
          <div className="p-6 border-b border-gray-200">
            <h4 className="text-base font-semibold text-gray-900 mb-4">你的解答 ({analysisData.language})</h4>
            <MarkdownRenderer content={'```' + analysisData.language + '\n' + analysisData.code + '\n```'} />
          </div>
        )}

        {/* 错误分析 - 仅在用户提交代码且有错误分析时显示 */}
        {analysisData.code && analysisData.analysis && analysisData.analysis.error_analysis && (
          <div className="p-6 border-b border-gray-200">
            <h4 className="text-base font-semibold text-gray-900 mb-4">错误分析</h4>
            <div className="text-xs text-gray-500 mb-3">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <span>以下错误分析由AI生成，仅供参考</span>
              </div>
            </div>
            <div className="bg-red-50 p-4 rounded-lg mb-6">
              <h5 className="font-medium text-red-800 mb-2">错误原因</h5>
              <div className="text-sm text-red-700">
                <p className="mb-2">你的解答中存在以下几个问题：</p>
                <div className="pl-5 space-y-2">
                  {/* 错误分析可能是单个对象或数组，做兼容处理 */}
                  {Array.isArray(analysisData.analysis.error_analysis) ? (
                    // 如果是数组，遍历显示每个错误
                    analysisData.analysis.error_analysis.map((error, index) => (
                      <div key={index} className="mb-3 pb-3 border-b border-red-200 last:border-0">
                        <p className="font-medium">错误 {index + 1}：{error.error_type}</p>
                        <p>位置：{error.error_location}</p>
                        <p>根本原因分析：{error.root_cause}</p>
                      </div>
                    ))
                  ) : (
                    // 如果是单个对象，直接显示
                    <div>
                      <p className="font-medium">错误类型：{analysisData.analysis.error_analysis.error_type}</p>
                      <p>位置：{analysisData.analysis.error_analysis.error_location}</p>
                      <p>根本原因分析：{analysisData.analysis.error_analysis.root_cause}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {analysisData.analysis.modification_suggestions && analysisData.analysis.modification_suggestions.length > 0 && (
              <div className="bg-yellow-50 p-4 rounded-lg mb-6">
                <h5 className="font-medium text-yellow-800 mb-2">修改建议</h5>
                <div className="text-sm text-yellow-700 space-y-4">
                  {analysisData.analysis.modification_suggestions.map((suggestion, index) => (
                    <div key={index} className="border-l-2 border-yellow-300 pl-3">
                      <p className="font-medium">行号：{suggestion.line_number}</p>
                      <div className="mt-1 mb-2">
                        <p className="text-gray-700">原代码：</p>
                        <pre className="bg-gray-100 p-2 rounded mt-1 text-gray-800 overflow-x-auto">{suggestion.original_code}</pre>
                      </div>
                      <div className="mt-1 mb-2">
                        <p className="text-green-700">修改后：</p>
                        <pre className="bg-green-50 p-2 rounded mt-1 text-green-800 overflow-x-auto">{suggestion.modified_code}</pre>
                      </div>
                      <p className="text-gray-600 mt-2">修改说明：{suggestion.explanation}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {analysisData.analysis.prevention_tips && analysisData.analysis.prevention_tips.length > 0 && (
              <div className="bg-blue-50 p-4 rounded-lg mb-6">
                <h5 className="font-medium text-blue-800 mb-2">预防建议</h5>
                <ul className="list-disc pl-5 text-sm text-blue-700 space-y-1">
                  {analysisData.analysis.prevention_tips.map((tip, index) => (
                    <li key={index}>{tip}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        {/* 参考解法部分 */}
        <div className="p-6 border-b border-gray-200">
          <h4 className="text-base font-semibold text-gray-900 mb-4">参考解法</h4>
          
          {/* 根据用户提交的语言调整显示顺序 */}
          {analysisData.code && analysisData.language?.toLowerCase().includes('python') ? (
            // 如果用户使用了Python，则先显示Python解法
            <>
              {analysisData.analysis.pythonCode && (
                <div className="bg-green-50 p-4 rounded-lg">
                  <h5 className="font-medium text-green-800 mb-2">参考解法 (Python)</h5>
                  <MarkdownRenderer content={'```python\n' + analysisData.analysis.pythonCode + '\n```'} />
                </div>
              )}
              {analysisData.analysis.cppCode && (
                <div className="bg-green-50 p-4 rounded-lg mt-4">
                  <h5 className="font-medium text-green-800 mb-2">参考解法 (C++)</h5>
                  <MarkdownRenderer content={'```cpp\n' + analysisData.analysis.cppCode + '\n```'} />
                </div>
              )}
            </>
          ) : (
            // 如果用户未提交代码或使用了其他语言，则先显示C++解法
            <>
              {analysisData.analysis.cppCode && (
                <div className="bg-green-50 p-4 rounded-lg">
                  <h5 className="font-medium text-green-800 mb-2">参考解法 (C++)</h5>
                  <MarkdownRenderer content={'```cpp\n' + analysisData.analysis.cppCode + '\n```'} />
                </div>
              )}
              {analysisData.analysis.pythonCode && (
                <div className="bg-green-50 p-4 rounded-lg mt-4">
                  <h5 className="font-medium text-green-800 mb-2">参考解法 (Python)</h5>
                  <MarkdownRenderer content={'```python\n' + analysisData.analysis.pythonCode + '\n```'} />
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
