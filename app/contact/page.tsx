'use client'

import { useState } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
	faEnvelope,
	faPhone,
	faPaperPlane,
	faHeadset,
	faComments,
	faLightbulb,
} from '@fortawesome/free-solid-svg-icons'
import {
	faWeixin
} from '@fortawesome/free-brands-svg-icons'
import Link from 'next/link'
import Image from 'next/image'
import FeedbackService from '../service/feedback-service'

export default function ContactPage() {
	const [formData, setFormData] = useState({
		name: '',
		contact: '',
		subject: '',
		category: '一般咨询',
		message: ''
	})
	const [isSubmitting, setIsSubmitting] = useState(false)
	const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')
	const [hoveredIcon, setHoveredIcon] = useState<string | null>(null)

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
		const { name, value } = e.target
		setFormData(prev => ({
			...prev,
			[name]: value
		}))
	}

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault()
		setIsSubmitting(true)
		
		try {
			// 调用反馈服务发送消息
			await FeedbackService.sendMessage({
				name: formData.name,
				contact: formData.contact,
				type: formData.category,
				title: formData.subject,
				content: formData.message
			})
			
			setSubmitStatus('success')
			setFormData({
				name: '',
				contact: '',
				subject: '',
				category: 'general',
				message: ''
			})
		} catch (error) {
			console.error('发送消息失败:', error)
			setSubmitStatus('error')
		} finally {
			setIsSubmitting(false)
		}
	}

	return (
		<div className="min-h-screen flex flex-col bg-gray-50">
			
			{/* 占位符，防止固定导航栏遮挡内容 */}
			<div id="navbar-placeholder"></div>

			{/* 主要内容 */}
			<div className="flex-1 container mx-auto px-4 py-8">
				{/* 页面标题 */}
				<div className="mb-8 text-center">
					<h1 className="text-3xl font-bold text-gray-800 mb-2">联系我们</h1>
					<p className="text-gray-600">我们随时为您提供帮助和支持</p>
				</div>

				<div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
					{/* 左侧 - 联系信息 */}
					<div className="space-y-6">
						{/* 联系方式 */}
						<div className="bg-white rounded-lg shadow-md p-6">
							<h2 className="text-xl font-semibold text-gray-800 mb-4">联系方式</h2>
							<div className="space-y-4">
								<div className="flex items-center">
									<FontAwesomeIcon icon={faEnvelope} className="text-indigo-600 w-5 mr-3" />
									<div>
										<p className="font-medium text-gray-800">邮箱</p>
										<p className="text-gray-600"><EMAIL></p>
									</div>
								</div>
								<div className="flex items-center">
									<FontAwesomeIcon icon={faPhone} className="text-indigo-600 w-5 mr-3" />
									<div>
										<p className="font-medium text-gray-800">客服热线</p>
										<p className="text-gray-600">13423490835</p>
									</div>
								</div>
								<div
									className="relative inline-block"
									onMouseEnter={() => setHoveredIcon('customer-wechat')}
									onMouseLeave={() => setHoveredIcon(null)}
								>
									<div className="flex items-center cursor-pointer">
										<FontAwesomeIcon icon={faWeixin} className="text-indigo-600 w-5 mr-3" />
										<div>
											<p className="font-medium text-gray-800">客服微信</p>
											<p className="text-gray-600">zbpBettey</p>
										</div>
									</div>
									{hoveredIcon === 'customer-wechat' && (
										<div className="absolute bottom-full mb-2 left-1/2 -translate-x-1/2 p-2 bg-white rounded-md shadow-lg z-10 w-32">
											<img src="/qrcode_wechat.jpg" alt="微信二维码" className="w-32 h-32 rounded-md" />
											<p className="text-center text-sm text-gray-600 mt-1">扫码添加客服</p>
										</div>
									)}
								</div>
							</div>
						</div>

						{/* 社交媒体 */}
						<div className="bg-white rounded-lg shadow-md p-6">
							<h2 className="text-xl font-semibold text-gray-800 mb-4">关注我们</h2>
							<div className="flex space-x-4">
								<div
									className="relative"
									onMouseEnter={() => setHoveredIcon('wechat')}
									onMouseLeave={() => setHoveredIcon(null)}
								>
									<div className="flex items-center justify-center w-12 h-12 bg-green-500 text-white rounded-full cursor-pointer hover:bg-green-600 transition-colors">
										<FontAwesomeIcon icon={faWeixin} className="text-xl" />
									</div>
									{hoveredIcon === 'wechat' && (
										<div className="absolute bottom-full mb-2 left-1/2 -translate-x-1/2 p-2 bg-white rounded-md shadow-lg z-10 w-32">
											<img src="/qrcode_wechat.jpg" alt="微信二维码" className="w-32 h-32 rounded-md" />
											<p className="text-center text-sm text-gray-600 mt-1">微信扫码关注</p>
										</div>
									)}
								</div>
								<div
									className="relative"
									onMouseEnter={() => setHoveredIcon('xiaohongshu')}
									onMouseLeave={() => setHoveredIcon(null)}
								>
									<div className="flex items-center justify-center w-12 h-12 bg-red-600 text-white rounded-full cursor-pointer hover:bg-red-700 transition-colors">
										<Image src="/xiaohongshu.svg" alt="小红书" width={24} height={24} />
									</div>
									{hoveredIcon === 'xiaohongshu' && (
										<div className="absolute bottom-full mb-2 left-1/2 -translate-x-1/2 p-2 bg-white rounded-md shadow-lg z-10 w-32">
											<img src="/qrcode_xiaohongshu.jpg" alt="小红书二维码" className="w-32 h-32 rounded-md" />
											<p className="text-center text-sm text-gray-600 mt-1">小红书扫码关注</p>
										</div>
									)}
								</div>
							</div>
						</div>

						{/* 常见问题快速入口 */}
						<div className="bg-white rounded-lg shadow-md p-6">
							<h2 className="text-xl font-semibold text-gray-800 mb-4">常见解决方案</h2>
							<div className="space-y-3">
								<Link href="/faq" className="flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
									<FontAwesomeIcon icon={faComments} className="text-indigo-600 w-5 mr-3" />
									<span className="text-gray-700">查看常见问题</span>
								</Link>
								<Link href="/help-center" className="flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
									<FontAwesomeIcon icon={faHeadset} className="text-indigo-600 w-5 mr-3" />
									<span className="text-gray-700">访问帮助中心</span>
								</Link>
							</div>
						</div>
					</div>

					{/* 右侧 - 联系表单 */}
					<div className="bg-white rounded-lg shadow-md p-6">
						<h2 className="text-xl font-semibold text-gray-800 mb-4">发送消息</h2>
						
						{submitStatus === 'success' && (
							<div className="mb-4 p-4 bg-green-50 border-l-4 border-green-400 rounded">
								<p className="text-green-700">消息发送成功！我们会尽快回复您。</p>
							</div>
						)}
						
						{submitStatus === 'error' && (
							<div className="mb-4 p-4 bg-red-50 border-l-4 border-red-400 rounded">
								<p className="text-red-700">消息发送失败，请稍后重试或通过其他方式联系我们。</p>
							</div>
						)}

						<form onSubmit={handleSubmit} className="space-y-4">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div>
									<label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
										姓名 *
									</label>
									<input
										type="text"
										id="name"
										name="name"
										value={formData.name}
										onChange={handleInputChange}
										required
										className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
									/>
								</div>
								<div>
									<label htmlFor="contact" className="block text-sm font-medium text-gray-700 mb-1">
										联系方式 *
									</label>
									<input
										type="text"
										id="contact"
										name="contact"
										value={formData.contact}
										onChange={handleInputChange}
										required
										placeholder="请输入手机号或邮箱"
										className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
									/>
								</div>
							</div>

							<div>
								<label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
									问题类型
								</label>
								<select
									id="category"
									name="category"
									value={formData.category}
									onChange={handleInputChange}
									className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
								>
									<option value="一般咨询">一般咨询</option>
									<option value="技术支持">技术支持</option>
									<option value="问题反馈">问题反馈</option>
									<option value="功能建议">功能建议</option>
									<option value="账号问题">账号问题</option>
									<option value="支付问题">支付问题</option>
								</select>
							</div>

							<div>
								<label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
									主题 *
								</label>
								<input
									type="text"
									id="subject"
									name="subject"
									value={formData.subject}
									onChange={handleInputChange}
									required
									placeholder="简要描述您的问题"
									className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
								/>
							</div>

							<div>
								<label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
									详细描述 *
								</label>
								<textarea
									id="message"
									name="message"
									value={formData.message}
									onChange={handleInputChange}
									required
									rows={5}
									placeholder="请详细描述您遇到的问题或建议..."
									className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
								/>
							</div>

							<button
								type="submit"
								disabled={isSubmitting}
								className="w-full bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
							>
								{isSubmitting ? (
									<>
										<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
										发送中...
									</>
								) : (
									<>
										<FontAwesomeIcon icon={faPaperPlane} className="mr-2" />
										发送消息
									</>
								)}
							</button>
						</form>
					</div>
				</div>

				{/* 服务时间提示 */}
				<div className="mt-8 bg-blue-50 border-l-4 border-blue-400 p-4 rounded-lg">
					<div className="flex items-center">
						<FontAwesomeIcon icon={faLightbulb} className="text-blue-400 mr-3" />
						<div>
							<h3 className="text-blue-800 font-medium">客服服务时间</h3>
							<p className="text-blue-700 text-sm mt-1">
								工作日：9:00 - 18:00 | 周末：10:00 - 17:00
								<br />
								我们通常在24小时内回复您的消息
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	)
} 