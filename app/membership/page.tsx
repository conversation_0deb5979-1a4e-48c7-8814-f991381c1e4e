'use client';

import React, {useEffect, useState} from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCheckCircle, faTimesCircle, faChevronRight,
  faRocket, faCrown, faSyncAlt, faStar
} from '@fortawesome/free-solid-svg-icons';
import UserService from "@/app/service/user-service";
import ProfileService from "@/app/service/profile-service";
import {useAppSelector} from "@/app/redux/hooks";
import {selectIsAuthenticated} from "@/app/redux/features/authSlice";
import {checkAuth} from "@/app/utils/authCheck";

export default function Membership() {
  const router = useRouter();

  // 当前会员等级 1: 基础版 2: Pro版 3: Ultra版
  const [currentMembershipLevel, setCurrentMembershipLevel] = useState<number | null>(null)

  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  useEffect(() => {
    if (isAuthenticated) {
      ProfileService.getProfile().then(resp => {
        const user = resp.data
        setCurrentMembershipLevel(user.currentMembershipLevel)
        console.log(user)
      })
    }
  }, []);

  // 处理跳转到确认购买页面
  const handleGoToPurchase = (planId: string, plan: string, upgrade: number = 0) => {
    if (!checkAuth()) {
      return;
    }

    // 如果是已开通会员升级到更高等级，直接跳转到付款页面
    if (upgrade === 1) {
      // 确定计费周期和价格
      // 使用URLSearchParams构建查询参数
      const params = new URLSearchParams();
      params.append('planId', planId);
      params.append('plan', plan);
      params.append('upgrade', upgrade.toString());
      params.append('months', '12'); // 默认12个月
      params.append('isContinuousMonthlySubscription', '0');

      // 直接跳转到支付页面
      router.push(`/payment?${params.toString()}`);
    } else {
      // 未开通会员或购买相同等级会员，跳转到确认页面
      const params = new URLSearchParams();
      params.append('planId', planId);
      params.append('plan', plan);
      params.append('upgrade', upgrade.toString());

      // 跳转到确认购买页面
      router.push(`/purchase?${params.toString()}`);
    }
  };

  // 显示促销文案
  const handlePromotion = () => {
    // 这里可以实现促销信息的展示逻辑
    if (!checkAuth()) {
      return;
    }

    // 未开通会员，跳转至确认购买页面
    const params = new URLSearchParams();
    params.append('planId', '2'); // 默认Pro版
    params.append('plan', 'pro');
    params.append('upgrade', '0');

    router.push(`/purchase?${params.toString()}`);
  };

  // 处理跳转到支付页面
  const handleGoToPayment = (planId: string, plan: string, billingType: string, price: number, upgrade: number = 0) => {
    if (!checkAuth()) {
      return;
    }
    // 使用URLSearchParams构建查询参数
    const params = new URLSearchParams();
    params.append('planId', planId); // 基础版或Pro版
    params.append('plan', plan); // 基础版或Pro版
    params.append('billingType', billingType); // 月付或年付
    params.append('price', price.toString());
    params.append('upgrade', upgrade.toString())

    // 使用router.push跳转到支付页面
    router.push(`/payment?${params.toString()}`);
  };

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* 主要内容 */}
      <div className="container mx-auto px-4 py-12">
        {/* 页面标题和简介 */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">升级到会员，开启更多学习可能</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">选择适合您的会员方案，让孩子的编程学习之旅更加顺畅、高效</p>
        </div>

        {/* 会员套餐卡片 */}
        <div className="grid lg:grid-cols-3 gap-8 max-w-8xl mx-auto mb-16">
          {/* 基础版 */}
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100 transform transition-transform hover:scale-105">
            <div className="p-8">
              <div className="flex justify-between items-start">
                <div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-2">基础版</h2>
                  <p className="text-gray-600 mb-4">适合初学者入门</p>
                </div>
              </div>

              <div className="mt-6 mb-8">
                <div className="flex items-baseline">
                  <div className="flex items-center">
                    <span className="text-2xl font-bold text-gray-800">¥9.9</span>
                    <span className="text-gray-600 ml-2">/ 月</span>
                  </div>
                  <span className="mx-4 text-gray-400">或</span>
                  <div className="flex items-center">
                    <span className="text-2xl font-bold text-gray-800">¥99</span>
                    <span className="text-gray-600 ml-2">/ 年</span>
                    <span className="ml-2 text-sm text-red-600 font-medium">(省¥19.8)</span>
                  </div>
                </div>
              </div>

              <ul className="space-y-3 mb-8">
                <li className="flex items-start">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" />
                  <span className="text-gray-700">完整访问所有基础题库</span>
                </li>
                <li className="flex items-start">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" />
                  <span className="text-gray-700">题目详解</span>
                </li>
                <li className="flex items-start">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" />
                  <span className="text-gray-700">个人进度跟踪</span>
                </li>
                <li className="flex items-start opacity-50">
                  <FontAwesomeIcon icon={faTimesCircle} className="text-red-500 mt-1 mr-2" />
                  <span className="text-gray-700">官方题单（不支持）</span>
                </li>
                <li className="flex items-start opacity-50">
                  <FontAwesomeIcon icon={faTimesCircle} className="text-red-500 mt-1 mr-2" />
                  <span className="text-gray-700">编程题库（不支持）</span>
                </li>
                <li className="flex items-start opacity-50">
                  <FontAwesomeIcon icon={faTimesCircle} className="text-red-500 mt-1 mr-2" />
                  <span className="text-gray-700">AI智能题单（不支持）</span>
                </li>
              </ul>

              {
                currentMembershipLevel && currentMembershipLevel > 1 ?
                  (<button className="w-full py-3 bg-gray-100 text-gray-400 rounded-lg font-medium">{currentMembershipLevel == 2 ? 'Pro版无法降级到基础版~' : 'Ultra版无法降级到基础版~'}</button>) :
                  (<div className="flex space-x-4">
                    <button
                      onClick={() => handleGoToPurchase('1', 'basic')}
                      className="w-full py-3 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition"
                    >
                      立即购买
                    </button>
                  </div>)
              }

            </div>
          </div>

          {/* Pro版 */}
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-indigo-200 transform transition-transform hover:scale-105 relative">
            <div className="absolute top-0 right-0 bg-indigo-600 text-white px-6 py-1 rounded-bl-lg font-medium">推荐</div>
            <div className="p-8">
              <div className="flex justify-between items-start">
                <div>
                  <h2 className="text-2xl font-bold text-indigo-600 mb-2">Pro版</h2>
                  <p className="text-gray-600 mb-4">适合进阶学习者</p>
                </div>
                <span className="px-4 py-1 bg-indigo-100 text-indigo-800 rounded-full text-sm font-medium">增强版</span>
              </div>

              <div className="mt-6 mb-8">
                <div className="flex items-baseline">
                  <div className="flex items-center">
                    <span className="text-2xl font-bold text-indigo-600">¥19.8</span>
                    <span className="text-gray-600 ml-2">/ 月</span>
                  </div>
                  <span className="mx-4 text-gray-400">或</span>
                  <div className="flex items-center">
                    <span className="text-2xl font-bold text-indigo-600">¥198</span>
                    <span className="text-gray-600 ml-2">/ 年</span>
                    <span className="ml-2 text-sm text-red-600 font-medium">(省¥39.6)</span>
                  </div>
                </div>
              </div>

              <ul className="space-y-3 mb-8">
                <li className="flex items-start">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" />
                  <span className="text-gray-700">包含基础版的所有功能</span>
                </li>
                <li className="flex items-start">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" />
                  <span className="text-gray-700 font-medium">完整访问编程题库</span>
                </li>
                <li className="flex items-start">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" />
                  <span className="text-gray-700">官方题单</span>
                </li>
                <li className="flex items-start opacity-50">
                  <FontAwesomeIcon icon={faTimesCircle} className="text-red-500 mt-1 mr-2" />
                  <span className="text-gray-700">考试模拟训练（不支持）</span>
                </li>
                <li className="flex items-start opacity-50">
                  <FontAwesomeIcon icon={faTimesCircle} className="text-red-500 mt-1 mr-2" />
                  <span className="text-gray-700">AI智能题单（不支持）</span>
                </li>
                <li className="flex items-start opacity-50">
                  <FontAwesomeIcon icon={faTimesCircle} className="text-red-500 mt-1 mr-2" />
                  <span className="text-gray-700">代码样例分析（不支持）</span>
                </li>
              </ul>

              {
                currentMembershipLevel && currentMembershipLevel > 2 ?
                  (<button className="w-full py-3 bg-gray-100 text-gray-400 rounded-lg font-medium">Ultra版无法降级到Pro版~</button>) :
                  (currentMembershipLevel !== 1 ? (
                    <div className="flex space-x-4">
                      <button
                        onClick={() => handleGoToPurchase('2', 'pro')}
                        className="w-full py-3 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition"
                      >
                        立即购买
                      </button>
                    </div>
                  ) : (
                    <div className="flex space-x-4">
                      <button
                        onClick={() => handleGoToPurchase('2', 'pro', 1)}
                        className="w-full cursor-pointer py-3 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition"
                      >
                        升级Pro版会员
                      </button>
                    </div>
                  ))
              }
            </div>
          </div>

          {/* Ultra版 - 即将上线 */}
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-purple-200 transform transition-transform hover:scale-105 relative">
            <div className="absolute top-0 right-0 bg-purple-600 text-white px-6 py-1 rounded-bl-lg font-medium">AI增强</div>
            <div className="p-8 relative">
              <div className="flex justify-between items-start">
                <div>
                  <h2 className="text-2xl font-bold text-purple-600 mb-2">Ultra版</h2>
                  <p className="text-gray-600 mb-4">适合竞赛选手</p>
                </div>
                <span className="px-4 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-medium">旗舰版</span>
              </div>

              <div className="mt-6 mb-8">
                <div className="flex items-baseline">
                  <div className="flex items-center">
                    <span className="text-2xl font-bold text-purple-600">¥39.8</span>
                    <span className="text-gray-600 ml-2">/ 月</span>
                  </div>
                  <span className="mx-4 text-gray-400">或</span>
                  <div className="flex items-center">
                    <span className="text-2xl font-bold text-purple-600">¥398</span>
                    <span className="text-gray-600 ml-2">/ 年</span>
                    <span className="ml-2 text-sm text-red-600 font-medium">(省¥79.6)</span>
                  </div>
                </div>
              </div>

              <ul className="space-y-3 mb-8">
                <li className="flex items-start">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" />
                  <span className="text-gray-700">包含Pro版的所有功能</span>
                </li>
                <li className="flex items-start">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" />
                  <span className="text-gray-700 font-medium">AI智能题单</span>
                </li>
                <li className="flex items-start">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" />
                  <span className="text-gray-700 font-medium">考试模拟训练</span>
                </li>
                <li className="flex items-start">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" />
                  <span className="text-gray-700 font-medium">错题分析</span>
                </li>
                <li className="flex items-start">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" />
                  <span className="text-gray-700 font-medium">学习进度分析</span>
                </li>
                <li className="flex items-start">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" />
                  <span className="text-gray-700 font-medium">AI学习建议</span>
                </li>
              </ul>

              {
                currentMembershipLevel !== 1 && currentMembershipLevel !== 2 ? (
                  <div className="flex space-x-4">
                    <button
                      onClick={() => handleGoToPurchase('3', 'ultra')}
                      className="w-full py-3 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 transition"
                    >
                      立即购买
                    </button>
                  </div>
                ) : (
                  <div className="flex space-x-4">
                    <button
                      onClick={() => handleGoToPurchase('3', 'ultra', 1)}
                      className="w-full cursor-pointer py-3 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 transition"
                    >
                      升级Ultra版会员
                    </button>
                  </div>
                )
              }
            </div>
          </div>
        </div>

        {/* 会员权益详细对比 */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden mb-16">
          <div className="p-6 bg-indigo-50 border-b border-indigo-100">
            <h2 className="text-2xl font-bold text-gray-800">会员权益详细对比</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead>
                <tr className="bg-gray-50">
                  <th scope="col" className="px-6 py-4 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">功能</th>
                  <th scope="col" className="px-6 py-4 text-center text-sm font-medium text-blue-600 uppercase tracking-wider">基础版</th>
                  <th scope="col" className="px-6 py-4 text-center text-sm font-medium text-indigo-600 uppercase tracking-wider">Pro版</th>
                  <th scope="col" className="px-6 py-4 text-center text-sm font-medium text-purple-600 uppercase tracking-wider">Ultra版</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {/* 基础题库 */}
                <tr>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">基础题库</td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900"><FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" /></td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900"><FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" /></td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900"><FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" /></td>
                </tr>
                {/* 编程题库 */}
                <tr>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">编程题库</td>
                  <td className="px-6 py-4 text-center text-sm text-gray-500"><FontAwesomeIcon icon={faTimesCircle} className="text-red-500 mt-1 mr-2" /></td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900 font-medium"><FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" /></td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900 font-medium"><FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" /></td>
                </tr>
                {/* 题目详解 */}
                <tr>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">题目详解</td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900"><FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" /></td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900"><FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" /></td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900"><FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" /></td>
                </tr>
                {/* 个人进度跟踪 */}
                <tr>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">个人进度跟踪</td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900">基础分析</td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900">基础分析</td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900">专业分析</td>
                </tr>
                {/* 官方题单 */}
                <tr>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">官方题单</td>
                  <td className="px-6 py-4 text-center text-sm text-gray-500"><FontAwesomeIcon icon={faTimesCircle} className="text-red-500 mt-1 mr-2" /></td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900 font-medium"><FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" /></td>
                  <td className="px-6 py-4 text-center text-sm text-purple-800 font-medium"><FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" /></td>
                </tr>
                {/* AI智能题单 */}
                <tr>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">AI智能题单</td>
                  <td className="px-6 py-4 text-center text-sm text-gray-500"><FontAwesomeIcon icon={faTimesCircle} className="text-red-500 mt-1 mr-2" /></td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900 font-medium"><FontAwesomeIcon icon={faTimesCircle} className="text-red-500 mt-1 mr-2" /></td>
                  <td className="px-6 py-4 text-center text-sm text-purple-800 font-medium"><FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" /></td>
                </tr>
                {/* 代码样例分析 */}
                <tr>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">代码样例分析</td>
                  <td className="px-6 py-4 text-center text-sm text-gray-500"><FontAwesomeIcon icon={faTimesCircle} className="text-red-500 mt-1 mr-2" /></td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900"><FontAwesomeIcon icon={faTimesCircle} className="text-red-500 mt-1 mr-2" /></td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900"><FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" /></td>
                </tr>
                {/* 考试模拟训练 */}
                <tr>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">考试模拟训练</td>
                  <td className="px-6 py-4 text-center text-sm text-gray-500"><FontAwesomeIcon icon={faTimesCircle} className="text-red-500 mt-1 mr-2" /></td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900"><FontAwesomeIcon icon={faTimesCircle} className="text-red-500 mt-1 mr-2" /></td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900"><FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" /></td>
                </tr>
                {/* 错题分析 */}
                <tr>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">错题分析</td>
                  <td className="px-6 py-4 text-center text-sm text-gray-500"><FontAwesomeIcon icon={faTimesCircle} className="text-red-500 mt-1 mr-2" /></td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900"><FontAwesomeIcon icon={faTimesCircle} className="text-red-500 mt-1 mr-2" /></td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900"><FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" /></td>
                </tr>
                {/* AI学习建议 */}
                <tr>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">AI学习建议</td>
                  <td className="px-6 py-4 text-center text-sm text-gray-500"><FontAwesomeIcon icon={faTimesCircle} className="text-red-500 mt-1 mr-2" /></td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900"><FontAwesomeIcon icon={faTimesCircle} className="text-red-500 mt-1 mr-2" /></td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900"><FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mt-1 mr-2" /></td>
                </tr>
                {/* 客服支持 */}
                <tr>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">客服支持</td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900">普通支持</td>
                  <td className="px-6 py-4 text-center text-sm text-gray-900 font-medium">优先支持</td>
                  <td className="px-6 py-4 text-center text-sm text-purple-800 font-medium">VIP专属支持</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* 常见问题 */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden mb-16">
          <div className="p-6 bg-indigo-50 border-b border-indigo-100">
            <h2 className="text-2xl font-bold text-gray-800">常见问题</h2>
          </div>
          <div className="p-6 space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">如何开通会员？</h3>
              <p className="text-gray-600">选择您喜欢的会员套餐，点击相应的"月付"或"年付"按钮，然后按照页面提示完成支付流程即可立即开通会员权益。</p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">会员是否自动续费？</h3>
              <p className="text-gray-600">会员默认为非自动续费，到期后需要手动续费。您可以在个人中心查看会员到期时间，并提前进行续费操作。</p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">可以更换会员套餐吗？</h3>
              <p className="text-gray-600">可以。基础版会员可以随时升级到Pro版，升级费用按剩余天数比例计算。Pro版降级需等当前套餐到期后再选择新套餐。</p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">有家庭套餐或团体优惠吗？</h3>
              <p className="text-gray-600">有。我们提供家庭套餐（3个账号）和班级团购（10个账号起）优惠，详情请联系客服了解更多信息。</p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">有教师版本吗？</h3>
              <p className="text-gray-600">有。我们提供教师版本，可管理学生学习进度，详情请联系客服了解更多信息。</p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">有机构版本吗？</h3>
              <p className="text-gray-600">有。我们提供机构版本，数据隔离，支持私有化部署，详情请联系客服了解更多信息。</p>
            </div>
          </div>
        </div>

        {/* 用户评价 */}
        <div className="max-w-5xl mx-auto mb-16">
          <h2 className="text-2xl font-bold text-gray-800 mb-8 text-center">用户评价</h2>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-white p-6 rounded-xl shadow-md">
              <div className="flex items-center mb-4">
                <div className="text-yellow-400 flex">
                  <FontAwesomeIcon icon={faStar} />
                  <FontAwesomeIcon icon={faStar} />
                  <FontAwesomeIcon icon={faStar} />
                  <FontAwesomeIcon icon={faStar} />
                  <FontAwesomeIcon icon={faStar} />
                </div>
                <span className="ml-2 text-gray-600 text-sm">5.0</span>
              </div>
              <p className="text-gray-700 mb-4">"Pro会员的AI题单功能非常实用，根据我孩子的弱点定制练习，编程能力提升很快。"</p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center text-indigo-800 font-semibold mr-3">LW</div>
                <div>
                  <div className="text-gray-800 font-medium">李女士</div>
                  <div className="text-gray-500 text-sm">六年级学生家长</div>
                </div>
              </div>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-md">
              <div className="flex items-center mb-4">
                <div className="text-yellow-400 flex">
                  <FontAwesomeIcon icon={faStar} />
                  <FontAwesomeIcon icon={faStar} />
                  <FontAwesomeIcon icon={faStar} />
                  <FontAwesomeIcon icon={faStar} />
                  <FontAwesomeIcon icon={faStar} />
                </div>
                <span className="ml-2 text-gray-600 text-sm">5.0</span>
              </div>
              <p className="text-gray-700 mb-4">"以前对编程没什么兴趣，但平台的题目设计很有趣，讲解也很清晰，现在已经爱上编程了！"</p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-800 font-semibold mr-3">ZH</div>
                <div>
                  <div className="text-gray-800 font-medium">张同学</div>
                  <div className="text-gray-500 text-sm">初中二年级学生</div>
                </div>
              </div>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-md">
              <div className="flex items-center mb-4">
                <div className="text-yellow-400 flex">
                  <FontAwesomeIcon icon={faStar} />
                  <FontAwesomeIcon icon={faStar} />
                  <FontAwesomeIcon icon={faStar} />
                  <FontAwesomeIcon icon={faStar} />
                  <FontAwesomeIcon icon={faStar} />
                </div>
                <span className="ml-2 text-gray-600 text-sm">4.8</span>
              </div>
              <p className="text-gray-700 mb-4">"作为编程老师，我推荐学生使用这个平台。内容全面且符合青少年认知特点，非常适合自主学习。"</p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center text-green-800 font-semibold mr-3">WL</div>
                <div>
                  <div className="text-gray-800 font-medium">王老师</div>
                  <div className="text-gray-500 text-sm">信息学教师</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 底部行动号召 */}
        <div className="bg-indigo-600 rounded-xl p-8 text-center text-white max-w-5xl mx-auto">
          <h2 className="text-3xl font-bold mb-4">立即开启编程学习之旅</h2>
          <p className="text-indigo-100 mb-6 max-w-3xl mx-auto">选择适合您的会员方案，享受全面的学习资源和服务，帮助孩子在编程世界中探索更多可能</p>
          <p className="text-indigo-100 mb-6 max-w-3xl mx-auto text-lg">
            <span className="bg-indigo-500 px-3 py-1 rounded-xl mr-2">限时优惠</span>
            年付可享<span className="font-bold text-yellow-300">高至17%优惠</span>
          </p>
          <div className="flex justify-center space-x-4">
            <button
              onClick={() => {
                window.scrollTo({ top: 0, behavior: 'smooth' });
              }}
              className="px-8 py-3 bg-white text-indigo-700 rounded-lg font-semibold hover:bg-indigo-50 transition"
            >
              立即开通
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
