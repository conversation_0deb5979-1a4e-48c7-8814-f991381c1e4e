"use client";

import {useState, useEffect} from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faRocket, faUser, faLock, faEye, faEyeSlash, faMobile, faEnvelope } from '@fortawesome/free-solid-svg-icons';
import { faPython, faJava, faJs, faWeixin } from '@fortawesome/free-brands-svg-icons';
import {useRouter, useSearchParams} from 'next/navigation';
import UserService from "@/app/service/user-service";
import MessageService from "@/app/service/message-service";
import { useAppDispatch, useAppSelector } from '@/app/redux/hooks';
import { login, selectAuthLoading, selectAuthError, setUser, UserInfo } from '@/app/redux/features/authSlice';
import UserInfoComponent from "@/app/components/UserInfo";
import { WechatLoginTicket, WechatLoginStatus } from "@/app/service/user-service";
import QRCode from 'react-qr-code';

type LoginMethod = 'password' | 'sms' | 'wechat';

interface LoginData {
  username: string;
  password: string;
  loginType: number;
}

interface SmsLoginData {
  username: string;
  verifyCode: string;
  loginType: number;
}

export default function Login() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const isLoading = useAppSelector(selectAuthLoading);
  const authError = useAppSelector(selectAuthError);
  const [loginMethod, setLoginMethod] = useState<LoginMethod>('wechat');
  // 微信登录相关状态
  const [wechatTicket, setWechatTicket] = useState<WechatLoginTicket | null>(null);
  const [wechatLoginPolling, setWechatLoginPolling] = useState<NodeJS.Timeout | null>(null);
  const [qrCodeExpired, setQrCodeExpired] = useState(false);

  const [formData, setFormData] = useState({
    username: '',
    password: '',
    phone: '',
    email: '',
    smsCode: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [validationErrors, setValidationErrors] = useState({
    username: '',
    password: '',
    phone: '',
    email: '',
    smsCode: '',
  });
  const [rememberMe, setRememberMe] = useState(false);
  const [countdown, setCountdown] = useState(0);

  const searchParams = useSearchParams();
  const redirect = searchParams.get('redirect') || '/'


  // 检查是否有保存的用户名或手机号
  useEffect(() => {
    try {
      if (loginMethod === 'password') {
        const savedUsername = localStorage.getItem('rememberedUsername')
        if (savedUsername) {
          setFormData(prev => ({ ...prev, username: savedUsername }))
          setRememberMe(true)
        } else {
          setRememberMe(false)
        }
      } else if (loginMethod === 'sms') {
        const savedPhone = localStorage.getItem('rememberedPhone')
        if (savedPhone) {
          setFormData(prev => ({ ...prev, phone: savedPhone }))
          setRememberMe(true)
        } else {
          setRememberMe(false)
        }
      }
    } catch (e) {
      console.warn('无法访问 localStorage', e)
    }
  }, [loginMethod])

  // 监听Redux中的认证错误
  useEffect(() => {
    if (authError) {
      setError(authError);
    }
  }, [authError]);

  // 获取微信登录二维码
  useEffect(() => {
    if (loginMethod === 'wechat' && !wechatTicket) {
      fetchWechatLoginTicket();
    }

    return () => {
      // 清除轮询
      if (wechatLoginPolling) {
        clearInterval(wechatLoginPolling);
        setWechatLoginPolling(null);
      }
    };
  }, [loginMethod]);

  // 倒计时效果
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const validateForm = () => {
    let isValid = true;
    const errors = {
      username: '',
      password: '',
      phone: '',
      email: '',
      smsCode: '',
    };

    if (loginMethod === 'password') {
      if (!formData.username.trim()) {
        errors.username = '用户名不能为空';
        isValid = false;
      } else if (formData.username.length < 3) {
        errors.username = '用户名长度至少为3个字符';
        isValid = false;
      }

      if (!formData.password) {
        errors.password = '密码不能为空';
        isValid = false;
      } else if (formData.password.length < 6) {
        errors.password = '密码长度至少为6个字符';
        isValid = false;
      }
    } else if (loginMethod === 'sms') {
      if (!formData.phone) {
        errors.phone = '手机号不能为空';
        isValid = false;
      } else if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
        errors.phone = '请输入正确的手机号';
        isValid = false;
      }

      if (!formData.smsCode) {
        errors.smsCode = '验证码不能为空';
        isValid = false;
      } else if (formData.smsCode.length !== 4) {
        errors.smsCode = '验证码长度必须为4位';
        isValid = false;
      }
    }

    setValidationErrors(errors);
    return isValid;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    if (validationErrors[name as keyof typeof validationErrors]) {
      setValidationErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const handleSendSmsCode = async () => {
    if (!formData.phone || !/^1[3-9]\d{9}$/.test(formData.phone)) {
      setValidationErrors(prev => ({ ...prev, phone: '请输入正确的手机号' }));
      return;
    }

    try {
      await MessageService.sendVerifyCode(formData.phone);
      setCountdown(60);
      setError('');
    } catch (error: any) {
      setError(error.msg || '发送验证码失败，请稍后重试');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!validateForm()) {
      return;
    }

    try {
      if (loginMethod === 'password') {
        if (rememberMe) {
          try {
            localStorage.setItem('rememberedUsername', formData.username);
          } catch (e) {
            console.warn('无法写入 localStorage', e);
          }
        } else {
          try {
            localStorage.removeItem('rememberedUsername');
          } catch (e) {
            console.warn('无法移除 localStorage', e);
          }
        }
      } else if (loginMethod === 'sms') {
        if (rememberMe) {
          try {
            localStorage.setItem('rememberedPhone', formData.phone);
          } catch (e) {
            console.warn('无法写入 localStorage', e);
          }
        } else {
          try {
            localStorage.removeItem('rememberedPhone');
          } catch (e) {
            console.warn('无法移除 localStorage', e);
          }
        }
      }

      if (loginMethod === 'password') {
        const loginData: LoginData = {
          username: formData.username,
          password: formData.password,
          loginType: 2
        };

        try {
          const res = await dispatch(login(loginData));
          if ('error' in res) {
            setError(res.payload || '登录失败，请检查输入信息');
          } else {
            console.log(redirect)
            router.push(redirect);
          }
        } catch (unwrapError: any) {
          setError(unwrapError.message || '登录失败，请检查输入信息');
        }
      } else if (loginMethod === 'sms') {
        const loginData: SmsLoginData = {
          username: formData.phone,
          verifyCode: formData.smsCode,
          loginType: 1
        };

        try {
          // TODO: 实现短信登录的 API 调用
          console.log('SMS login:', loginData);
          try {
            // @ts-ignore - TS错误在这里是因为login接口定义可能与实际使用不完全匹配
            const res = await dispatch(login(loginData));
            if (!('error' in res)) {
              router.push(redirect);
            }
          } catch (unwrapError: any) {
            setError(unwrapError.message || '登录失败，请检查输入信息');
          }
        } catch (error) {
          setError('短信登录失败，请稍后重试');
        }
      }
    } catch (err) {
      setError('登录过程中发生错误，请稍后重试');
      console.error('登录错误:', err);
    }
  };

  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const handleRememberMeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRememberMe(e.target.checked);
  };

  // 获取微信登录二维码
  const fetchWechatLoginTicket = async () => {
    try {
      setError('');
      setQrCodeExpired(false);
      const response = await UserService.getWechatLoginTicket();
      const ticketData = response.data;
      setWechatTicket(ticketData);

      // 开始轮询检查扫码状态
      startWechatLoginStatusPolling(ticketData.ticket);
    } catch (error: any) {
      setError(error.response?.data?.message || '获取微信登录二维码失败，请稍后重试');
    }
  };

  // 开始轮询检查微信扫码状态
  const startWechatLoginStatusPolling = (ticket: string) => {
    // 清除现有的轮询
    if (wechatLoginPolling) {
      clearInterval(wechatLoginPolling);
    }

    // 开始新的轮询
    const polling = setInterval(async () => {
      try {
        const response = await UserService.getWechatLoginStatus(ticket);
        const status: WechatLoginStatus = response.data;

        if (status.status === 1 && status.user) {
          // 登录成功，更新用户信息
          dispatch(setUser(status.user));
          clearInterval(polling);
          setWechatLoginPolling(null);

          // 如果用户未绑定手机号，跳转到设置页面
          if (!status.hasBindPhone) {
            router.push('/settings');
          } else {
            router.push(redirect);
          }
        } else if (status.status === -1) {
          setQrCodeExpired(true)
        }
      } catch (error) {
        console.error('获取微信登录状态失败:', error);
        clearInterval(polling);
        setWechatLoginPolling(null);
        setError('获取登录状态失败，请刷新页面重试');
      }
    }, 2000);

    setWechatLoginPolling(polling);
  };

  const renderLoginForm = () => {
    switch (loginMethod) {
      case 'password':
        return (
          <>
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">用户名/手机号/邮箱</label>
              <div className="relative">
                <span className="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-500">
                  <FontAwesomeIcon icon={faUser} />
                </span>
                <input
                  type="text"
                  id="username"
                  name="username"
                  value={formData.username}
                  onChange={handleChange}
                  required
                  className={`block w-full pl-10 pr-3 py-3 border ${validationErrors.username ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'} rounded-lg focus:outline-none focus:ring-2`}
                  placeholder="请输入用户名/手机号/邮箱"
                />
              </div>
              {validationErrors.username && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.username}</p>
              )}
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">密码</label>
              <div className="relative">
                <span className="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-500">
                  <FontAwesomeIcon icon={faLock} />
                </span>
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  required
                  className={`block w-full pl-10 pr-10 py-3 border ${validationErrors.password ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'} rounded-lg focus:outline-none focus:ring-2`}
                  placeholder="请输入密码"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500"
                  onClick={toggleShowPassword}
                >
                  <FontAwesomeIcon icon={showPassword ? faEyeSlash : faEye} />
                </button>
              </div>
              {validationErrors.password && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.password}</p>
              )}
            </div>
          </>
        );
      case 'sms':
        return (
          <>
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">手机号</label>
              <div className="relative">
                <span className="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-500">
                  <FontAwesomeIcon icon={faMobile} />
                </span>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  required
                  className={`block w-full pl-10 pr-3 py-3 border ${validationErrors.phone ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'} rounded-lg focus:outline-none focus:ring-2`}
                  placeholder="请输入手机号"
                />
              </div>
              {validationErrors.phone && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.phone}</p>
              )}
            </div>

            <div>
              <label htmlFor="smsCode" className="block text-sm font-medium text-gray-700 mb-1">验证码</label>
              <div className="relative">
                <span className="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-500">
                  <FontAwesomeIcon icon={faLock} />
                </span>
                <input
                  type="text"
                  id="smsCode"
                  name="smsCode"
                  value={formData.smsCode}
                  onChange={handleChange}
                  required
                  className={`block w-full pl-10 pr-32 py-3 border ${validationErrors.smsCode ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'} rounded-lg focus:outline-none focus:ring-2`}
                  placeholder="请输入验证码"
                />
                <button
                  type="button"
                  onClick={handleSendSmsCode}
                  disabled={countdown > 0}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-indigo-600 hover:text-indigo-500 disabled:text-gray-400"
                >
                  {countdown > 0 ? `${countdown}s后重试` : '获取验证码'}
                </button>
              </div>
              {validationErrors.smsCode && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.smsCode}</p>
              )}
            </div>
          </>
        );
      case 'wechat':
        return (
          <div className="flex flex-col items-center justify-center py-8">
            {wechatTicket ? (
              <>
                <div className="w-56 h-56 bg-white rounded-lg flex items-center justify-center mb-4 p-4 border-2 border-gray-200 relative">
                  {/* 使用QRCode组件生成二维码 */}
                  <QRCode
                    value={wechatTicket.url}
                    size={200}
                    level="H"
                    className={`w-full h-full ${qrCodeExpired ? 'opacity-30' : ''}`}
                  />
                  {qrCodeExpired && (
                    <div className="absolute inset-0 flex flex-col items-center justify-center bg-black/10 rounded-lg">
                      <span className="text-red-600 font-medium text-lg mb-2">二维码已过期</span>
                      <button
                        type="button"
                        onClick={fetchWechatLoginTicket}
                        className="px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700"
                      >
                        刷新二维码
                      </button>
                    </div>
                  )}
                </div>
                <p className="text-gray-600 text-sm">请使用微信扫描二维码登录</p>
                <p className="text-gray-400 text-xs mt-2">二维码有效期 {wechatTicket.expire_seconds} 秒</p>
                {!qrCodeExpired && (
                  <button
                    type="button"
                    onClick={fetchWechatLoginTicket}
                    className="mt-4 text-sm text-indigo-600 hover:text-indigo-500"
                  >
                    刷新二维码
                  </button>
                )}
              </>
            ) : (
              <>
                <div className="w-56 h-56 bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                  <FontAwesomeIcon icon={faWeixin} className="text-6xl text-green-500" />
                </div>
                <p className="text-gray-600 text-sm">正在加载微信二维码...</p>
              </>
            )}
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50">
      <div className="flex flex-col md:flex-row items-center md:items-stretch bg-white rounded-xl shadow-lg overflow-hidden max-w-6xl w-full">
        {/* 左侧图片区域 */}
        <div className="w-full md:w-1/2 hidden md:block">
          <div className="h-full relative bg-indigo-700">
            <div className="absolute inset-0 bg-gradient-to-b from-indigo-700/70 via-indigo-700/60 to-indigo-700/80 flex flex-col justify-between p-12">
              <div>
                <div className="flex items-center space-x-2">
                  <Image src="/logo2.png" alt="信竞星球" width={100} height={40} />
                </div>
                <p className="text-indigo-100 mt-4 text-lg">
                  少儿编程学习平台
                </p>
              </div>
              <div className="space-y-4">
                <h2 className="text-2xl font-bold text-white">开启编程学习之旅</h2>
                <p className="text-indigo-100">
                  在信竞星球，通过精心设计的编程题目，培养孩子的逻辑思维和问题解决能力，为未来的科技创新者打下坚实基础。
                </p>
                <div className="flex items-center space-x-4 pt-4">
                  <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center">
                    <FontAwesomeIcon icon={faPython} className="text-white" />
                  </div>
                  <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center">
                    <FontAwesomeIcon icon={faJs} className="text-white" />
                  </div>
                  <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center">
                    <FontAwesomeIcon icon={faJava} className="text-white" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧登录表单 */}
        <div className="w-full md:w-1/2 p-8 md:p-12">
          <div className="mb-8">
            <h2 className="text-3xl font-bold text-gray-800">欢迎回来</h2>
            <p className="text-gray-600 mt-2">请选择登录方式继续学习</p>
          </div>

          {error && (
            <div className="mb-6 p-4 bg-red-50 text-red-600 rounded-lg">
              {error}
            </div>
          )}

          {/* 登录方式切换 */}
          <div className="flex space-x-4 mb-6">
            <button
                onClick={() => setLoginMethod('wechat')}
                className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium ${
                  loginMethod === 'wechat'
                    ? 'bg-indigo-600 text-white'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
              微信登录
            </button>
            <button
              onClick={() => setLoginMethod('sms')}
              className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium ${
                loginMethod === 'sms'
                  ? 'bg-indigo-600 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              验证码登录
            </button>
            <button
              onClick={() => setLoginMethod('password')}
              className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium ${
                loginMethod === 'password'
                  ? 'bg-indigo-600 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              密码登录
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {renderLoginForm()}

            {loginMethod === 'wechat' ? (
              <div className="text-center text-sm text-gray-600">
                注册或登录即代表您同意
                <Link href="/agreement/user" className="text-indigo-600 hover:text-indigo-500">《用户协议》</Link>
                和
                <Link href="/agreement/privacy" className="text-indigo-600 hover:text-indigo-500">《隐私协议》</Link>。
                未注册的用户将在完成手机号验证或微信扫码后自动完成注册。
              </div>
            ) : (
              <>
                <div className="flex items-center justify-between">
                  {(loginMethod === 'password' || loginMethod === 'sms') && (
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="remember"
                        checked={rememberMe}
                        onChange={handleRememberMeChange}
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      />
                      <label htmlFor="remember" className="ml-2 block text-sm text-gray-700">
                        记住我
                      </label>
                    </div>
                  )}
                  <Link href="/forgot-password" className="text-sm text-indigo-600 hover:text-indigo-500">
                    忘记密码？
                  </Link>
                </div>

                <button
                  type="submit"
                  disabled={isLoading}
                  className={`w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${isLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
                >
                  {isLoading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      登录中...
                    </>
                  ) : loginMethod === 'password' ? '登录' : '登录/注册'}
                </button>

                <div className="text-center text-sm text-gray-600">
                  注册或登录即代表您同意
                  <Link href="/agreement/user" className="text-indigo-600 hover:text-indigo-500">《用户协议》</Link>
                  和
                  <Link href="/agreement/privacy" className="text-indigo-600 hover:text-indigo-500">《隐私协议》</Link>。
                  未注册的用户将在完成手机号验证或微信扫码后自动完成注册。
                </div>
              </>
            )}
          </form>
        </div>
      </div>
    </div>
  );
}
