"use client";

import Link from 'next/link';
import Image from 'next/image';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faRocket, faLaptopCode, faBrain, faTrophy, faChalkboardTeacher, faUserGraduate, faCheckCircle } from '@fortawesome/free-solid-svg-icons';
import { faUser, faSignOutAlt } from '@fortawesome/free-solid-svg-icons';
import { useState, useEffect } from 'react';
import { useAppSelector } from './redux/hooks';
import { selectIsAuthenticated } from './redux/features/authSlice';
import LoggedInHome from './components/LoggedInHome';

export default function Home() {
  const isAuthenticated = useAppSelector(selectIsAuthenticated);

  // 如果用户已登录，显示已登录状态的首页
  if (isAuthenticated) {
    return <LoggedInHome />;
  }

  // 未登录状态显示原有首页
  return (
    <main>
      {/* 轮播图区域 */}
      <section className="relative bg-gradient-to-r from-indigo-500 to-purple-600 py-16 md:py-24">
        <div className="container mx-auto px-4 flex flex-col md:flex-row items-center">
          <div className="md:w-1/2 mb-10 md:mb-0 md:pr-10">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              启发小天才的编程思维
            </h1>
            <p className="text-xl text-indigo-100 mb-8">
              通过有趣的编程挑战和题目，培养孩子的逻辑思维和问题解决能力
            </p>
            <div className="flex flex-wrap gap-4">
              <Link
                href="/basic-problems"
                className="bg-white text-indigo-600 hover:bg-indigo-50 px-6 py-3 rounded-lg font-medium shadow-lg"
              >
                开始学习
              </Link>
              <Link
                href="/membership"
                className="bg-transparent text-white border-2 border-white hover:bg-white hover:text-indigo-600 px-6 py-3 rounded-lg font-medium transition-colors"
              >
                会员介绍
              </Link>
            </div>
          </div>
          <div className="md:w-1/2 relative">
            <Image
              src="https://images.unsplash.com/photo-1599666520394-50d845fe09c6?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
              alt="儿童编程"
              width={600}
              height={400}
              className="rounded-lg shadow-xl"
            />
          </div>
        </div>
      </section>

      {/* 核心特色区域 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center text-gray-800 mb-12">信竞星球的核心特色</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-md transform transition duration-300 hover:scale-105">
              <div className="w-14 h-14 bg-indigo-100 rounded-full flex items-center justify-center text-indigo-600 mb-4">
                <FontAwesomeIcon icon={faLaptopCode} className="text-2xl" />
              </div>
              <h3 className="text-xl font-semibold mb-3">丰富的题库资源</h3>
              <p className="text-gray-600">
                提供上千道精选编程题目，涵盖基础知识点到高级算法，满足不同学习阶段的需求
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md transform transition duration-300 hover:scale-105">
              <div className="w-14 h-14 bg-indigo-100 rounded-full flex items-center justify-center text-indigo-600 mb-4">
                <FontAwesomeIcon icon={faBrain} className="text-2xl" />
              </div>
              <h3 className="text-xl font-semibold mb-3">思维能力培养</h3>
              <p className="text-gray-600">
                通过编程训练培养逻辑思维、算法思维和问题解决能力，提高孩子的综合素质
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md transform transition duration-300 hover:scale-105">
              <div className="w-14 h-14 bg-indigo-100 rounded-full flex items-center justify-center text-indigo-600 mb-4">
                <FontAwesomeIcon icon={faTrophy} className="text-2xl" />
              </div>
              <h3 className="text-xl font-semibold mb-3">竞赛能力提升</h3>
              <p className="text-gray-600">
                针对信息学奥赛等青少年编程竞赛，提供专项训练和模拟考试，助力竞赛佳绩
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 学习路径区域 */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center text-gray-800 mb-4">清晰的学习路径</h2>
          <p className="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
            从入门到精通，我们为不同阶段的学习者规划了详细的学习路径
          </p>

          <div className="flex flex-col md:flex-row gap-6">
            <div className="flex-1 border border-gray-200 rounded-lg p-6 bg-white shadow-sm">
              <h3 className="text-xl font-semibold text-indigo-600 mb-4">入门阶段</h3>
              <ul className="space-y-3">
                <li className="flex items-center">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mr-2" />
                  <span>基础编程概念与语法</span>
                </li>
                <li className="flex items-center">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mr-2" />
                  <span>简单的逻辑判断题</span>
                </li>
                <li className="flex items-center">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mr-2" />
                  <span>基础算法入门题</span>
                </li>
                <li className="flex items-center">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mr-2" />
                  <span>小项目实践</span>
                </li>
              </ul>
            </div>

            <div className="flex-1 border border-gray-200 rounded-lg p-6 bg-white shadow-sm">
              <h3 className="text-xl font-semibold text-indigo-600 mb-4">提高阶段</h3>
              <ul className="space-y-3">
                <li className="flex items-center">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mr-2" />
                  <span>数据结构基础</span>
                </li>
                <li className="flex items-center">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mr-2" />
                  <span>基本算法思想</span>
                </li>
                <li className="flex items-center">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mr-2" />
                  <span>编程技巧提升</span>
                </li>
                <li className="flex items-center">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mr-2" />
                  <span>中等难度编程题</span>
                </li>
              </ul>
            </div>

            <div className="flex-1 border border-gray-200 rounded-lg p-6 bg-white shadow-sm">
              <h3 className="text-xl font-semibold text-indigo-600 mb-4">竞赛阶段</h3>
              <ul className="space-y-3">
                <li className="flex items-center">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mr-2" />
                  <span>高级数据结构</span>
                </li>
                <li className="flex items-center">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mr-2" />
                  <span>复杂算法与技巧</span>
                </li>
                <li className="flex items-center">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mr-2" />
                  <span>竞赛专项训练</span>
                </li>
                <li className="flex items-center">
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mr-2" />
                  <span>模拟竞赛实战</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* 师资介绍区域 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center text-gray-800 mb-4">专业师资团队</h2>
          <p className="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
            我们的老师都具有丰富的教学经验和竞赛指导能力
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg overflow-hidden shadow-md">
              <Image
                src="https://images.unsplash.com/photo-1557862921-37829c790f19?w=300&h=200&fit=crop"
                alt="教师照片"
                width={300}
                height={200}
                className="w-full h-48 object-cover"
              />
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-2">张教授</h3>
                <p className="text-indigo-600 mb-3">算法与竞赛教练</p>
                <p className="text-gray-600 mb-4">
                  前ACM金牌得主，多次带队参与国际信息学奥赛，具有10年青少年编程教学经验。
                </p>
                <div className="flex items-center">
                  <FontAwesomeIcon icon={faChalkboardTeacher} className="text-indigo-500 mr-2" />
                  <span className="text-gray-700">已指导 200+ 名学生</span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg overflow-hidden shadow-md">
              <Image
                src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=300&h=200&fit=crop"
                alt="教师照片"
                width={300}
                height={200}
                className="w-full h-48 object-cover"
              />
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-2">李老师</h3>
                <p className="text-indigo-600 mb-3">编程基础导师</p>
                <p className="text-gray-600 mb-4">
                  计算机科学硕士，专注少儿编程思维启蒙，善于激发孩子学习兴趣，教学风格生动活泼。
                </p>
                <div className="flex items-center">
                  <FontAwesomeIcon icon={faChalkboardTeacher} className="text-indigo-500 mr-2" />
                  <span className="text-gray-700">已指导 300+ 名学生</span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg overflow-hidden shadow-md">
              <Image
                src="https://images.unsplash.com/photo-1544717305-2782549b5136?w=300&h=200&fit=crop"
                alt="教师照片"
                width={300}
                height={200}
                className="w-full h-48 object-cover"
              />
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-2">王教练</h3>
                <p className="text-indigo-600 mb-3">数据结构专家</p>
                <p className="text-gray-600 mb-4">
                  知名IT企业前高级工程师，深厚的技术功底和实战经验，注重培养学生的实际编程能力。
                </p>
                <div className="flex items-center">
                  <FontAwesomeIcon icon={faChalkboardTeacher} className="text-indigo-500 mr-2" />
                  <span className="text-gray-700">已指导 150+ 名学生</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 学员成果展示 */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center text-gray-800 mb-4">学员成果展示</h2>
          <p className="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
            看看我们的学员在各类竞赛中取得的优异成绩
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center text-white mr-4">
                  <FontAwesomeIcon icon={faUserGraduate} />
                </div>
                <div>
                  <h3 className="font-semibold">张同学</h3>
                  <p className="text-sm text-gray-600">学习时间: 1.5年</p>
                </div>
              </div>
              <p className="text-gray-700 mb-4">
                "在信竞星球学习后，我成功在全国青少年信息学奥林匹克竞赛中获得省级一等奖，并晋级全国赛。"
              </p>
              <div className="flex items-center text-indigo-600 font-medium">
                <span>全国信息学奥赛 省一等奖</span>
              </div>
            </div>

            <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center text-white mr-4">
                  <FontAwesomeIcon icon={faUserGraduate} />
                </div>
                <div>
                  <h3 className="font-semibold">李同学</h3>
                  <p className="text-sm text-gray-600">学习时间: 2年</p>
                </div>
              </div>
              <p className="text-gray-700 mb-4">
                "通过系统学习，我在编程思维方面有了质的飞跃，在蓝桥杯青少年组比赛中获得了全国二等奖。"
              </p>
              <div className="flex items-center text-indigo-600 font-medium">
                <span>蓝桥杯青少年组 全国二等奖</span>
              </div>
            </div>

            <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center text-white mr-4">
                  <FontAwesomeIcon icon={faUserGraduate} />
                </div>
                <div>
                  <h3 className="font-semibold">王同学</h3>
                  <p className="text-sm text-gray-600">学习时间: 1年</p>
                </div>
              </div>
              <p className="text-gray-700 mb-4">
                "从零基础开始学习，现在已经能够独立完成复杂的编程项目，并在学校的科技创新大赛中获得一等奖。"
              </p>
              <div className="flex items-center text-indigo-600 font-medium">
                <span>校园科技创新大赛 一等奖</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 号召行动区 */}
      <section className="py-16 bg-indigo-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">开启孩子的编程之旅</h2>
          <p className="mb-8 max-w-2xl mx-auto">
            今天注册信竞星球，让孩子在有趣的编程世界中探索无限可能
          </p>
          <div className="flex justify-center space-x-4">
            <Link
              href="/register"
              className="bg-white text-indigo-600 hover:bg-indigo-50 px-6 py-3 rounded-lg font-medium"
            >
              免费注册
            </Link>
            <Link
              href="/basic-problems"
              className="bg-transparent border-2 border-white hover:bg-white hover:text-indigo-600 px-6 py-3 rounded-lg font-medium transition-colors"
            >
              开始学习
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}
