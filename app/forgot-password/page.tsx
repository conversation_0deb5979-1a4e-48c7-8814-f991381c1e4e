'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faMobile, faEnvelope, faLock, faEye, faEyeSlash } from '@fortawesome/free-solid-svg-icons'
import MessageService from '@/app/service/message-service'
import UserService from '@/app/service/user-service'
import ProfileService from '@/app/service/profile-service'

// 找回方式类型
type FindMethod = 'phone' | 'email'

export default function ForgotPassword () {
	const router = useRouter()
	const [findMethod, setFindMethod] = useState<FindMethod>('phone')
	const [formData, setFormData] = useState({
		phone: '',
		email: '',
		verifyCode: '',
		newPassword: '',
		confirmPassword: '',
	})
	const [showPassword, setShowPassword] = useState(false)
	const [countdown, setCountdown] = useState(0)
	const [error, setError] = useState('')
	const [success, setSuccess] = useState('')
	const [validationErrors, setValidationErrors] = useState({
		phone: '',
		email: '',
		verifyCode: '',
		newPassword: '',
		confirmPassword: '',
	})
	const [isSubmitting, setIsSubmitting] = useState(false)

	// 倒计时效果
	useEffect(() => {
		if (countdown > 0) {
			const timer = setTimeout(() => setCountdown(countdown - 1), 1000)
			return () => clearTimeout(timer)
		}
	}, [countdown])

	const validateForm = () => {
		let isValid = true
		const errors = { phone: '', email: '', verifyCode: '', newPassword: '', confirmPassword: '' }
		if (findMethod === 'phone') {
			if (!formData.phone) {
				errors.phone = '手机号不能为空'
				isValid = false
			} else if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
				errors.phone = '请输入正确的手机号'
				isValid = false
			}
		} else {
			if (!formData.email) {
				errors.email = '邮箱不能为空'
				isValid = false
			} else if (!/^\S+@\S+\.\S+$/.test(formData.email)) {
				errors.email = '请输入正确的邮箱'
				isValid = false
			}
		}
		if (!formData.verifyCode) {
			errors.verifyCode = '验证码不能为空'
			isValid = false
		} else if (formData.verifyCode.length !== 4) {
			errors.verifyCode = '验证码长度必须为4位'
			isValid = false
		}
		if (!formData.newPassword) {
			errors.newPassword = '新密码不能为空'
			isValid = false
		} else if (formData.newPassword.length < 6) {
			errors.newPassword = '密码长度至少为6位'
			isValid = false
		}
		if (!formData.confirmPassword) {
			errors.confirmPassword = '请再次输入新密码'
			isValid = false
		} else if (formData.confirmPassword !== formData.newPassword) {
			errors.confirmPassword = '两次输入的密码不一致'
			isValid = false
		}
		setValidationErrors(errors)
		return isValid
	}

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value } = e.target
		setFormData(prev => ({ ...prev, [name]: value }))
		if (validationErrors[name as keyof typeof validationErrors]) {
			setValidationErrors(prev => ({ ...prev, [name]: '' }))
		}
	}

	const handleSendCode = async () => {
		setError('')
		if (findMethod === 'phone') {
			if (!formData.phone || !/^1[3-9]\d{9}$/.test(formData.phone)) {
				setValidationErrors(prev => ({ ...prev, phone: '请输入正确的手机号' }))
				return
			}
			try {
				await MessageService.sendVerifyCode(formData.phone, 1)
				setCountdown(60)
			} catch (err: any) {
				setError(err?.msg || '验证码发送失败')
			}
		} else {
			if (!formData.email || !/^\S+@\S+\.\S+$/.test(formData.email)) {
				setValidationErrors(prev => ({ ...prev, email: '请输入正确的邮箱' }))
				return
			}
			try {
				await MessageService.sendEmailVerifyCodeNotLogin(formData.email)
				setCountdown(60)
			} catch (err: any) {
				setError(err?.msg || '验证码发送失败')
			}
		}
	}

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault()
		setError('')
		setSuccess('')
		if (!validateForm()) return
		setIsSubmitting(true)
		try {
			if (findMethod === 'phone') {
				await ProfileService.resetPassword({
					password: formData.newPassword,
					verifyCode: formData.verifyCode,
					phone: formData.phone
				})
			} else {
				await ProfileService.resetPassword({
					password: formData.newPassword,
					verifyCode: formData.verifyCode,
					email: formData.email
				})
			}
			setSuccess('密码重置成功，请返回登录')
		} catch (err: any) {
			setError(err?.msg || err.message || '重置密码失败')
		} finally {
			setIsSubmitting(false)
		}
	}

	return (
		<div className='min-h-screen flex flex-col items-center justify-center bg-gray-50'>
			<div className='w-full max-w-md bg-white rounded-xl shadow-lg p-8'>
				<h2 className='text-2xl font-bold text-gray-800 mb-6 text-center'>找回密码</h2>
				{error && <div className='mb-4 p-3 bg-red-50 text-red-600 rounded'>{error}</div>}
				{success && <div className='mb-4 p-3 bg-green-50 text-green-600 rounded'>{success}</div>}
				<div className='flex space-x-4 mb-6'>
					<button
						onClick={() => setFindMethod('phone')}
						className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium ${findMethod === 'phone' ? 'bg-indigo-600 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
					>
						手机号找回
					</button>
					<button
						onClick={() => setFindMethod('email')}
						className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium ${findMethod === 'email' ? 'bg-indigo-600 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
					>
						邮箱找回
					</button>
				</div>
				<form onSubmit={handleSubmit} className='space-y-5'>
					{findMethod === 'phone' ? (
						<div>
							<label htmlFor='phone' className='block text-sm font-medium text-gray-700 mb-1'>手机号</label>
							<div className='relative'>
								<span className='absolute inset-y-0 left-0 pl-3 flex items-center text-gray-500'>
									<FontAwesomeIcon icon={faMobile} />
								</span>
								<input
									type='tel'
									id='phone'
									name='phone'
									value={formData.phone}
									onChange={handleChange}
									required
									className={`block w-full pl-10 pr-3 py-3 border ${validationErrors.phone ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'} rounded-lg focus:outline-none focus:ring-2`}
									placeholder='请输入手机号'
								/>
							</div>
							{validationErrors.phone && <p className='mt-1 text-sm text-red-600'>{validationErrors.phone}</p>}
						</div>
					) : (
						<div>
							<label htmlFor='email' className='block text-sm font-medium text-gray-700 mb-1'>邮箱</label>
							<div className='relative'>
								<span className='absolute inset-y-0 left-0 pl-3 flex items-center text-gray-500'>
									<FontAwesomeIcon icon={faEnvelope} />
								</span>
								<input
									type='email'
									id='email'
									name='email'
									value={formData.email}
									onChange={handleChange}
									required
									className={`block w-full pl-10 pr-3 py-3 border ${validationErrors.email ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'} rounded-lg focus:outline-none focus:ring-2`}
									placeholder='请输入邮箱'
								/>
							</div>
							{validationErrors.email && <p className='mt-1 text-sm text-red-600'>{validationErrors.email}</p>}
						</div>
					)}
					<div>
						<label htmlFor='verifyCode' className='block text-sm font-medium text-gray-700 mb-1'>验证码</label>
						<div className='relative'>
							<span className='absolute inset-y-0 left-0 pl-3 flex items-center text-gray-500'>
								<FontAwesomeIcon icon={faLock} />
							</span>
							<input
								type='text'
								id='verifyCode'
								name='verifyCode'
								value={formData.verifyCode}
								onChange={handleChange}
								required
								className={`block w-full pl-10 pr-32 py-3 border ${validationErrors.verifyCode ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'} rounded-lg focus:outline-none focus:ring-2`}
								placeholder='请输入验证码'
							/>
							<button
								type='button'
								onClick={handleSendCode}
								disabled={countdown > 0}
								className='absolute inset-y-0 right-0 pr-3 flex items-center text-indigo-600 hover:text-indigo-500 disabled:text-gray-400'
							>
								{countdown > 0 ? `${countdown}s后重试` : '获取验证码'}
							</button>
						</div>
						{validationErrors.verifyCode && <p className='mt-1 text-sm text-red-600'>{validationErrors.verifyCode}</p>}
					</div>
					<div>
						<label htmlFor='newPassword' className='block text-sm font-medium text-gray-700 mb-1'>新密码</label>
						<div className='relative'>
							<span className='absolute inset-y-0 left-0 pl-3 flex items-center text-gray-500'>
								<FontAwesomeIcon icon={faLock} />
							</span>
							<input
								type={showPassword ? 'text' : 'password'}
								id='newPassword'
								name='newPassword'
								value={formData.newPassword}
								onChange={handleChange}
								required
								className={`block w-full pl-10 pr-10 py-3 border ${validationErrors.newPassword ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'} rounded-lg focus:outline-none focus:ring-2`}
								placeholder='请输入新密码'
							/>
							<button
								type='button'
								onClick={() => setShowPassword(v => !v)}
								className='absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500'
							>
								<FontAwesomeIcon icon={showPassword ? faEyeSlash : faEye} />
							</button>
						</div>
						{validationErrors.newPassword && <p className='mt-1 text-sm text-red-600'>{validationErrors.newPassword}</p>}
					</div>
					<div>
						<label htmlFor='confirmPassword' className='block text-sm font-medium text-gray-700 mb-1'>确认新密码</label>
						<div className='relative'>
							<span className='absolute inset-y-0 left-0 pl-3 flex items-center text-gray-500'>
								<FontAwesomeIcon icon={faLock} />
							</span>
							<input
								type={showPassword ? 'text' : 'password'}
								id='confirmPassword'
								name='confirmPassword'
								value={formData.confirmPassword}
								onChange={handleChange}
								required
								className={`block w-full pl-10 pr-10 py-3 border ${validationErrors.confirmPassword ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'} rounded-lg focus:outline-none focus:ring-2`}
								placeholder='请再次输入新密码'
							/>
						</div>
						{validationErrors.confirmPassword && <p className='mt-1 text-sm text-red-600'>{validationErrors.confirmPassword}</p>}
					</div>
					<button
						type='submit'
						disabled={isSubmitting}
						className={`w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${isSubmitting ? 'opacity-70 cursor-not-allowed' : ''}`}
					>
						{isSubmitting ? '提交中...' : '重置密码'}
					</button>
					<div className='text-center text-sm text-gray-600 mt-4'>
						返回<Link href='/login' className='text-indigo-600 hover:text-indigo-500 ml-1'>登录</Link>
					</div>
				</form>
			</div>
		</div>
	)
} 