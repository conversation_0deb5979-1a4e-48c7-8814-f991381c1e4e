'use client'

import { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
	faChevronDown,
	faSearch,
	faBook,
	faHeadset,
} from '@fortawesome/free-solid-svg-icons'
import Link from 'next/link'
import Navbar from '../components/Navbar'
import Footer from '../components/Footer'
import MarkdownRenderer from '../components/MarkdownRenderer'
import faqService, { Faq } from '../service/faq-service'

interface FaqCategory {
	category: string
	faqs: Faq[]
}

export default function FaqPage() {
	const [faqData, setFaqData] = useState<FaqCategory[]>([])
	const [loading, setLoading] = useState(true)
	const [error, setError] = useState<string | null>(null)
	const [searchTerm, setSearchTerm] = useState('')
	const [selectedCategory, setSelectedCategory] = useState('全部问题')
	const [openAccordions, setOpenAccordions] = useState<Set<string>>(new Set())

	// 获取FAQ数据
	useEffect(() => {
		const fetchFaqData = async () => {
			try {
				setLoading(true)
				const response = await faqService.getFaqList()
				
				// 将返回的数据转换为分类格式
				// 假设返回格式为 Record<string, Faq[]> 或 Record<string, Faq>[]
				let categories: FaqCategory[] = []
				
				if (Array.isArray(response.data)) {
					// 如果是数组格式：[{"账号相关": [...faqs]}, {"题目练习": [...faqs]}]
					categories = response.data.map((categoryData) => {
						const [category, faqs] = Object.entries(categoryData)[0]
						return {
							category,
							faqs: Array.isArray(faqs) ? faqs : []
						}
					})
				} else if (typeof response.data === 'object') {
					// 如果是对象格式：{"账号相关": [...faqs], "题目练习": [...faqs]}
					categories = Object.entries(response.data).map(([category, faqs]) => ({
						category,
						faqs: Array.isArray(faqs) ? faqs : []
					}))
				}
				
				setFaqData(categories)
			} catch (err) {
				console.error('获取FAQ数据失败:', err)
				setError('无法加载常见问题数据，请稍后重试')
			} finally {
				setLoading(false)
			}
		}

		fetchFaqData()
	}, [])

	// 获取所有分类
	const categories = ['全部问题', ...faqData.map(item => item.category)]

	// 过滤FAQ数据
	const filteredFaqData = faqData.filter(categoryData => {
		if (selectedCategory !== '全部问题' && categoryData.category !== selectedCategory) {
			return false
		}
		
		if (searchTerm) {
			return categoryData.faqs.some(faq =>
				faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
				faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
			)
		}
		
		return true
	}).map(categoryData => ({
		...categoryData,
		faqs: categoryData.faqs.filter(faq =>
			!searchTerm ||
			faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
			faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
		)
	}))

	// 切换手风琴状态
	const toggleAccordion = (id: string) => {
		const newOpenAccordions = new Set(openAccordions)
		if (newOpenAccordions.has(id)) {
			newOpenAccordions.delete(id)
		} else {
			newOpenAccordions.add(id)
		}
		setOpenAccordions(newOpenAccordions)
	}



	if (loading) {
		return null
	}

	if (error) {
		return null;
	}

	return (
		<div className="min-h-screen flex flex-col bg-gray-50">

			{/* 主要内容 */}
			<div className="flex-1 container mx-auto px-4 py-8">
				{/* 页面标题 */}
				<div className="mb-8 text-center">
					<h1 className="text-3xl font-bold text-gray-800 mb-2">常见问题</h1>
					<p className="text-gray-600">解答您在使用信竞星球时可能遇到的常见问题</p>
				</div>

				{/* 分类标签 */}
				<div className="flex flex-wrap justify-center gap-2 mb-8">
					{categories.map((category) => (
						<button
							key={category}
							onClick={() => setSelectedCategory(category)}
							className={`px-4 py-2 rounded-full text-sm font-medium transition ${
								selectedCategory === category
									? 'bg-indigo-600 text-white'
									: 'bg-white text-gray-700 hover:bg-indigo-50'
							}`}
						>
							{category}
						</button>
					))}
				</div>

				{/* 搜索框 */}
				<div className="max-w-2xl mx-auto mb-8">
					<div className="relative">
						<input
							type="text"
							placeholder="搜索常见问题..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
						/>
						<button className="absolute right-2 top-1/2 transform -translate-y-1/2 text-indigo-600">
							<FontAwesomeIcon icon={faSearch} />
						</button>
					</div>
				</div>

				{/* FAQ 手风琴列表 */}
				<div className="max-w-3xl mx-auto space-y-4">
					{filteredFaqData.length === 0 ? (
						<div className="text-center py-12">
							<div className="text-gray-400 text-6xl mb-4">🔍</div>
							<h3 className="text-xl font-semibold text-gray-700 mb-2">没有找到相关问题</h3>
							<p className="text-gray-500">请尝试使用不同的关键词搜索</p>
						</div>
					) : (
						filteredFaqData.map((categoryData) => (
							<div key={categoryData.category}>
								{/* 分类标题 */}
								<div className="border-l-4 border-indigo-600 pl-4 py-2 mb-6">
									<h2 className="text-xl font-semibold text-gray-800">{categoryData.category}</h2>
								</div>

								{/* FAQ项目 */}
								{categoryData.faqs.map((faq, index) => {
									const accordionId = `${categoryData.category}-${index}`
									const isOpen = openAccordions.has(accordionId)

									return (
										<div key={accordionId} className="bg-white rounded-lg shadow-md overflow-hidden mb-4">
											<button
												onClick={() => toggleAccordion(accordionId)}
												className="w-full px-6 py-4 text-left focus:outline-none flex justify-between items-center hover:bg-gray-50 transition-colors"
											>
												<h3 className="text-lg font-medium text-gray-800 pr-4">{faq.question}</h3>
												<FontAwesomeIcon
													icon={faChevronDown}
													className={`text-indigo-600 transition-transform ${
														isOpen ? 'rotate-180' : ''
													}`}
												/>
											</button>
											{isOpen && (
												<div className="px-6 py-4 border-t border-gray-100">
													<MarkdownRenderer
														content={faq.answer}
														className="text-gray-700"
														codeTheme="light"
														format="auto"
													/>
												</div>
											)}
										</div>
									)
								})}
							</div>
						))
					)}
				</div>

				{/* 未解决问题？ */}
				<div className="max-w-2xl mx-auto mt-12 bg-indigo-50 rounded-lg p-6 text-center">
					<h3 className="text-xl font-semibold text-gray-800 mb-3">没有找到您要的答案？</h3>
					<p className="text-gray-700 mb-4">您可以前往帮助中心获取更多详细指南，或直接联系我们的客服团队</p>
					<div className="flex flex-col sm:flex-row justify-center gap-3">
						<Link
							href="/help-center"
							className="bg-white text-indigo-600 hover:bg-indigo-50 font-medium py-2 px-6 rounded-md transition duration-300 inline-flex items-center justify-center"
						>
							<FontAwesomeIcon icon={faBook} className="mr-2" />
							查看帮助中心
						</Link>
						<Link
							href="/contact"
							className="bg-indigo-600 text-white hover:bg-indigo-700 font-medium py-2 px-6 rounded-md transition duration-300 inline-flex items-center justify-center"
						>
							<FontAwesomeIcon icon={faHeadset} className="mr-2" />
							联系客服
						</Link>
					</div>
				</div>
			</div>
		</div>
	)
} 