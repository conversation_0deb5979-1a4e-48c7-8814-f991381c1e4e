"use client";

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch, faChevronDown } from '@fortawesome/free-solid-svg-icons';
import Pagination from '../components/Pagination';
import TagSelector from '../components/TagSelector';
import CommonDropdown, { DropdownOption } from '../components/CommonDropdown';
import CodingProblemService from "@/app/service/coding-problem-service";
import type { Problem } from "@/app/service/coding-problem-service";
import { getProblemDifficultyColor, getProblemDifficultyText } from "@/app/utils/constant";
import TagService, {Tag, TagGroup} from "@/app/service/tag-service";
import CategoryService from "@/app/service/category-service";
import { useAppSelector } from '@/app/redux/hooks';
import { selectIsAuthenticated } from '@/app/redux/features/authSlice';


interface SearchParam {
  difficulty: number | null;
  categoryId: number | null;
  status: number | null;
  keyword: string | null;
  tagIds: number[] | null
}

export default function CodingProblems() {
  const [problems, setProblems] = useState<Problem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [filters, setFilters] = useState<SearchParam>({
    difficulty: null,
    categoryId: null,
    status: null,
    keyword: '',
    tagIds: [] as number[]
  });
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const [isTagSelectorOpen, setIsTagSelectorOpen] = useState(false);
  const tagSelectorRef = useRef<HTMLDivElement | null>(null);
  const [tagOptions, setTagOptions] = useState<TagGroup[]>([])
  const [selectedTags, setSelectedTags] = useState<Tag[]>([])
  const pageSize = 10;

  // 难度级别选项
  const difficultyOptions: DropdownOption<number | null>[] = [
    { label: '全部', value: null },
    { label: '简单', value: 1 },
    { label: '中等', value: 2 },
    { label: '困难', value: 3 }
  ];

  // 状态选项
  const statusOptions: DropdownOption<number | null>[] = [
    { label: '全部状态', value: null },
    { label: '未开始', value: 1 },
    { label: '已解答', value: 2 },
    { label: '尝试中', value: 3 }
  ];

  useEffect(() => {
    fetchProblems();
  }, [currentPage, filters.difficulty, filters.categoryId, filters.status, filters.tagIds]);

  // 处理点击外部区域关闭标签选择器
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (tagSelectorRef.current && !tagSelectorRef.current?.contains(event.target as Node)) {
        setIsTagSelectorOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const fetchProblems = async (retry = 0) => {
    setLoading(true);
    setError('');

    await CodingProblemService.getProblemList({
      pageNum: currentPage,
      pageSize: pageSize,
      tagIds: filters.tagIds && filters.tagIds.length > 0 ? filters.tagIds : undefined,
      categoryId: filters.categoryId,
      difficulty: filters.difficulty,
      keyword: filters.keyword,
      status: filters.status
    }).then(resp => {
      setProblems(resp.data.items)
      setTotalItems(resp.data.total)
    }).catch(err => {
      setError(err.msg || '获取题目列表失败，请稍后重试');
    }).finally(() => {
      setLoading(false);
    })
  };

  const handleDifficultyChange = (value: number | null) => {
    setFilters(prev => ({
      ...prev,
      difficulty: value
    }));
    setCurrentPage(1);
  };

  const handleCategoryChange = (value: number | null) => {
    setFilters(prev => ({
      ...prev,
      categoryId: value
    }));
    setCurrentPage(1);
  };

  const handleStatusChange = (value: number | null) => {
    setFilters(prev => ({
      ...prev,
      status: value
    }));
    setCurrentPage(1);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters(prev => ({
      ...prev,
      keyword: e.target.value
    }));
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchProblems();
  };
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const getDifficultyColor = (difficulty: number) => {
    switch (difficulty) {
      case 0:
        return 'bg-green-100 text-green-800';
      case 1:
        return 'bg-yellow-100 text-yellow-800';
      case 2:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getProblemStatusColor = (status: number) => {
    switch (status) {
      case -1:
        return 'bg-gray-100 text-gray-800';
      case 0:
        return 'bg-yellow-100 text-yellow-800';
      case 1:
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getProblemStatusText = (status: number) => {
    switch (status) {
      case -1:
        return '未开始';
      case 0:
        return '尝试中';
      case 1:
        return '已解答';
      default:
        return '未知';
    }
  };

  const handleTagsChange = (selectedTags: Tag[]) => {
    console.log(selectedTags)
    setFilters(prev => ({
      ...prev,
      tagIds: selectedTags.map(tag => tag.id)
    }));
    setCurrentPage(1);
    setSelectedTags(selectedTags)
  };

  const toggleTagSelector = () => {
    setIsTagSelectorOpen(!isTagSelectorOpen);
  };

  useEffect(() => {
    TagService.getTagList(2).then(resp => {
      setTagOptions(resp.data)
    })
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <main className="container mx-auto px-4 pb-12">
        {/* 页面标题 */}
        <div className="py-6">
          <h1 className="text-3xl font-bold text-gray-900">编程题库</h1>
          <p className="text-gray-600 mt-2">通过实战编程挑战提升您的算法和数据结构能力</p>
        </div>

        {/* 主要内容 */}
        <div className="container mx-auto px-4 py-8">
          {/* 筛选和搜索区域 */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            {/* 筛选条件行 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              {/* 难度选择器 */}
              <div>
                <label className="block text-gray-700 mb-2">难度</label>
                <CommonDropdown
                  label="难度"
                  options={difficultyOptions}
                  value={filters.difficulty}
                  onChange={handleDifficultyChange}
                  placeholder="全部难度"
                  className="w-full"
                />
              </div>

              {/* 状态选择器 */}
              <div>
                <label className="block text-gray-700 mb-2">状态</label>
                <CommonDropdown<number | null>
                  label="状态"
                  options={statusOptions}
                  value={filters.status}
                  onChange={handleStatusChange}
                  placeholder="全部状态"
                  className="w-full"
                  disabled={!isAuthenticated}
                />
                {!isAuthenticated && (
                  <p className="text-xs text-gray-500 mt-1">请登录后使用状态筛选</p>
                )}
              </div>

              {/* 标签选择器按钮 */}
              <div className="md:col-span-1">
                <label className="block text-gray-700 mb-2">标签</label>
                <div className="relative" ref={tagSelectorRef}>
                  <button
                    type="button"
                    onClick={toggleTagSelector}
                    className="flex items-center justify-between w-full px-4 py-2 bg-gray-100 border border-gray-200 rounded-lg focus:outline-none hover:bg-gray-200 transition-colors"
                  >
                    <div className="flex items-center">
                      <span className="text-gray-700">标签筛选</span>
                      {filters.tagIds && filters.tagIds.length > 0 && (
                        <span className="ml-2 flex items-center justify-center h-6 w-6 bg-gray-500 text-white text-xs font-medium rounded-full">
                          {filters.tagIds.length}
                        </span>
                      )}
                    </div>
                    <FontAwesomeIcon icon={faChevronDown} className={`text-gray-500 ml-2 transition-transform ${isTagSelectorOpen ? 'transform rotate-180' : ''}`} />
                  </button>

                  {isTagSelectorOpen && (
                    <div className="absolute z-50 left-0 mt-2 w-full md:w-[450px] bg-white rounded-lg shadow-lg border border-gray-200">
                      <TagSelector
                        onTagsChange={handleTagsChange}
                        className="w-full max-h-[400px] overflow-auto"
                        initialSelectedTags={selectedTags || []}
                        tags={tagOptions}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 搜索行 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              

              {/* 搜索框 */}
              <div className="md:col-span-3">
                <label className="block text-gray-700 mb-2">搜索</label>
                <form onSubmit={handleSearchSubmit} className="relative">
                  <input
                    type="text"
                    placeholder="搜索题目名称、关键词..."
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    value={filters.keyword || ''}
                    onChange={handleSearchChange}
                  />
                  <button
                    type="submit"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-indigo-600"
                  >
                    <FontAwesomeIcon icon={faSearch} />
                  </button>
                </form>
              </div>
            </div>
          </div>

          {/* 错误信息 */}
          {error && (
            <div className="bg-red-50 p-4 rounded-lg text-red-600 mb-8">
              {error}
            </div>
          )}

          {/* 加载中 */}
          {loading && (
            <div className="bg-white rounded-lg shadow-md p-8 text-center mb-8">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600 mb-2"></div>
              <p className="text-gray-600">加载中...</p>
            </div>
          )}

          {/* 题目列表 */}
          {!loading && !error && (
            <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
              <div className="relative sm:rounded-lg">
                <div className="md:hidden px-4 py-3 bg-gray-50 text-xs font-medium text-gray-500">
                  <div className="flex items-center justify-between">
                    <span>← 左右滑动查看更多 →</span>
                  </div>
                </div>
                <div className="overflow-x-auto" style={{ WebkitOverflowScrolling: 'touch' }}>
                  <table className="min-w-full divide-y divide-gray-200 border-collapse table-fixed">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap w-12">
                          ID
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap w-48 md:w-auto">
                          题目
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap w-16 md:w-auto">
                          难度
                        </th>
                        {isAuthenticated && (
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap w-16 md:w-auto">
                            状态
                          </th>
                        )}
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap w-16 md:w-auto">
                          通过率
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap w-16 md:w-auto">
                          提交次数
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap w-32 md:w-auto">
                          标签
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {problems.length > 0 ? (
                        problems.map(problem => (
                          <tr key={problem.pid} className="hover:bg-gray-50">
                            <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                              {problem.pid}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap">
                              <Link href={`/coding-problems/${problem.pid}`} className="text-indigo-600 hover:text-indigo-900 font-medium">
                                {problem.title}
                              </Link>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap">
                              <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getProblemDifficultyColor(problem.difficulty)}`}>
                                {getProblemDifficultyText(problem.difficulty)}
                              </span>
                            </td>
                            {isAuthenticated && (
                              <td className="px-4 py-3 whitespace-nowrap">
                                <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getProblemStatusColor(problem.status)}`}>
                                  {getProblemStatusText(problem.status)}
                                </span>
                              </td>
                            )}
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                              {problem.statistics?.commitAmt && problem.statistics?.acceptAmt ?
                                `${Math.round((problem.statistics.acceptAmt / problem.statistics.commitAmt) * 100)}%` :
                                '-'
                              }
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                              {problem.statistics?.commitAmt || 0}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                              <div className="flex flex-wrap gap-1">
                                {problem.tags?.map(tag => (
                                  <span key={tag.id} className="px-2 py-0.5 rounded text-xs bg-gray-100">
                                    {tag.name}
                                  </span>
                                ))}
                              </div>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={isAuthenticated ? 7 : 6} className="px-4 py-3 text-center text-gray-500">
                            没有找到符合条件的题目
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* 分页控制 */}
          {!loading && problems.length > 0 && (
            <Pagination
              currentPage={currentPage}
              totalItems={totalItems}
              pageSize={pageSize}
              onPageChange={handlePageChange}
            />
          )}
        </div>
      </main>
    </div>
  );
}
