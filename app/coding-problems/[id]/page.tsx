"use client";

import {useState, useEffect, useRef, JSX, useCallback, cache} from 'react';
import {useParams, useSearchParams} from 'next/navigation';
import Link from 'next/link';
import Pagination from "@/app/components/Pagination";
import {FontAwesomeIcon} from '@fortawesome/react-fontawesome';
import {
  faRocket,
  faChevronLeft,
  faChevronRight,
  faBookmark,
  faFlag,
  faPlay,
  faPaperPlane,
  faSyncAlt,
  faCheckCircle,
  faUser,
  faTag,
  faChevronDown,
  faSignOutAlt,
  faChevronUp,
  faFileAlt,
  faLightbulb,
  faHistory,
  faComments,
  faCode,
  faTimes
} from '@fortawesome/free-solid-svg-icons';
import Image from 'next/image';
import FavoriteButton from '../../components/FavoriteButton';
import CodeEditor from '../../components/CodeEditor';
import CodingProblemService from "@/app/service/coding-problem-service";
import type {ProblemDetail} from "@/app/service/coding-problem-service";
import MarkdownRenderer from "@/app/components/MarkdownRenderer";
import JudgeService from "@/app/service/judge-service";
import type {JudgeResult} from "@/app/service/judge-service";
import type {TestJudgeResult} from "@/app/service/judge-service";
import {CODING_PROBLEM_DIFFICULTY_MAP, JUDGE_STATUS} from "@/app/utils/constant";
import {formatKb} from "@/app/utils/number-utils";
import SubmissionDetailModal from '@/app/components/Modal/SubmissionDetailModal';
import ProblemSolutionService, {ProblemSolution} from '@/app/service/problem-solution-service';
import {UserInfo, PageResult} from '@/app/model/models';

import {useAppSelector} from '@/app/redux/hooks';
import {selectUser} from '@/app/redux/features/authSlice';
import LearningContextService, {LearningContext} from "@/app/service/learning-context-service";
import {checkAuth} from "@/app/utils/authCheck";

interface ProblemExample {
    input: string;
    output: string;
}

interface TabProps {
  id: string;
  label: string;
  isActive: boolean;
  onClick: () => void;
  isCollapsed: boolean;
}

// 获取状态文本
const getStatusText = (status: number) => {
	const statusKey = String(status);
	return (JUDGE_STATUS as any)[statusKey]?.name || '未知状态';
};
// 获取状态样式
const getStatusStyle = (status: number) => {
	switch (status) {
		case 0:
			return 'bg-green-100 text-green-800';
		default:
			return 'bg-red-100 text-red-800';
	}
};

// 修改Tab组件，支持显示图标
const Tab: React.FC<TabProps> = ({id, label, isActive, onClick, isCollapsed}) => {
  // 为常用选项卡定义图标
  const getTabIcon = () => {
    switch (id) {
      case 'description':
				return <FontAwesomeIcon icon={faFileAlt}/>;
      case 'solution':
				return <FontAwesomeIcon icon={faLightbulb}/>;
      case 'submissions':
				return <FontAwesomeIcon icon={faHistory}/>;
      case 'discussions':
				return <FontAwesomeIcon icon={faComments}/>;
      default:
				return <FontAwesomeIcon icon={faFileAlt}/>;
    }
  };

  return (
    <li className={`${isCollapsed ? 'w-full' : 'mr-1'}`}>
      <button
        className={`
          ${isCollapsed 
            ? 'w-full py-3 flex justify-center items-center' 
            : 'inline-block py-2 px-4'
          } 
          ${isActive 
            ? 'text-indigo-600 font-medium' 
            : 'text-gray-500 hover:text-gray-700'
          }
          ${isCollapsed 
            ? (isActive ? 'border-l-4 border-indigo-600 bg-indigo-50' : 'border-l-4 border-transparent') 
            : (isActive ? 'border-b-2 border-indigo-600' : 'border-b-2 border-transparent hover:border-gray-300')
          }
          transition-all duration-200
        `}
        onClick={onClick}
        role="tab"
        aria-selected={isActive}
        id={`${id}-tab`}
        aria-controls={id}
        title={label}
      >
        {isCollapsed ? (
          getTabIcon()
        ) : (
          <span>{label}</span>
        )}
      </button>
    </li>
  );
};

// 创建一个缓存解包函数
const getParams = cache((params: { id: string }) => {
	return {id: params.id};
});

export default function CodingProblemDetail() {
  // 使用useParams获取参数
  const params = useParams<{ id: string }>();
	const {id} = getParams(params);

  const [problemId, setProblemId] = useState<string>(id)
	const [problem, setProblem] = useState<ProblemDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('description');
  const [activeResultTab, setActiveResultTab] = useState('test-cases');
  const [code, setCode] = useState('');
	const [language, setLanguage] = useState('Python3');
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [isFlagged, setIsFlagged] = useState(false);
  const [isRunning, setIsRunning] = useState(false);
	const [runResult, setRunResult] = useState<TestJudgeResult | null>(null);

  // 添加移动设备状态
  const [isMobile, setIsMobile] = useState(false);
  // 添加代码编辑器模态框状态
  const [showCodeEditor, setShowCodeEditor] = useState(false);
  // 添加移动端提示关闭状态
  const [hideDesktopTip, setHideDesktopTip] = useState(false);

  // 检测设备尺寸
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

	// 添加测试用例状态
	const [testInput, setTestInput] = useState('');
	const [testOutput, setTestOutput] = useState('');
	// 添加缓存状态
	const [cachedExamples, setCachedExamples] = useState<{ input: string, output: string }[]>([]);
	// 添加反馈状态
	const [feedbackMessage, setFeedbackMessage] = useState('');

  const leftPanelRef = useRef<HTMLDivElement>(null);
  const resultsContainerRef = useRef<HTMLDivElement>(null);

  // 添加用户状态
  const [user, setUser] = useState<any>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // 添加一个状态来跟踪左侧面板是否折叠
  const [isLeftPanelCollapsed, setIsLeftPanelCollapsed] = useState(false);

	// 添加面板初始状态，默认折叠
	const [isResultsPanelCollapsed, setIsResultsPanelCollapsed] = useState(true);

  // 添加移动端测试用例输入区域的折叠状态，默认展开
  const [isTestInputCollapsed, setIsTestInputCollapsed] = useState(false);

	// 添加左右面板比例状态，默认1:1
	const [leftPanelWidth, setLeftPanelWidth] = useState(50); // 百分比值

	// 添加拖动状态
	const [isDragging, setIsDragging] = useState(false);

	// 添加当前选中的题解索引
	const [activeSolutionIndex, setActiveSolutionIndex] = useState(0);

	// 添加状态用于提交详情
	const [selectedSubmission, setSelectedSubmission] = useState<JudgeResult | null>(null);
	const [showSubmissionModal, setShowSubmissionModal] = useState(false);

	// 添加筛选状态
	const [statusFilter, setStatusFilter] = useState('all');
	const [languageFilter, setLanguageFilter] = useState('all');


	// 提交记录
	// 添加分页相关状态
	const [currentPage, setCurrentPage] = useState(1);
	const [pageSize, setPageSize] = useState(10); // 每页显示5条记录
  const [pageSizeOptions] = useState([2, 5, 10, 15, 20]);
	// 提交记录列表
	const [submissionList, setSubmissionList] = useState<JudgeResult[] | null>(null)
	const [submissionTotal, setSubmissionTotal] = useState<number>(0)
	const [submitListQueryParams, setSubmitListQueryParams] = useState<{pageNum: number, pageSize: number, pid: string}>({pageNum: 1, pageSize: 10, pid: id})

	// 添加加载状态变量
	const [isLoadingSubmissions, setIsLoadingSubmissions] = useState(false);

	const fetchSubmissionList = () => {
		setIsLoadingSubmissions(true); // 开始加载
		submitListQueryParams.pageNum = currentPage
		setSubmitListQueryParams({...submitListQueryParams, pageNum: currentPage})

		JudgeService.getSubmitList(submitListQueryParams).then(resp => {
			setSubmissionList(resp.data.items)
			setSubmissionTotal(resp.data.total)
		}).finally(() => {
			setIsLoadingSubmissions(false); // 完成加载
		})
	}

	useEffect(() => {
		if (activeTab === 'submissions' && submissionList === null) {
			fetchSubmissionList()
		}
	}, [activeTab])
	useEffect(() => {
		if (currentPage !== submitListQueryParams.pageNum) {
			fetchSubmissionList()
		}
	}, [currentPage])

	const getExamples = (value: string) => {
		const reg = '<input>([\\s\\S]*?)</input><output>([\\s\\S]*?)</output>'
		const re = RegExp(reg, 'g')
		const objList: ProblemExample[] = []
		let tmp = re.exec(value)
		while (tmp) {
			objList.push({input: tmp[1], output: tmp[2]})
			tmp = re.exec(value)
		}
		return objList
	}

	// 拖动处理函数
	const handleDragStart = () => {
		setIsDragging(true);
	};

	const handleDrag = (e: MouseEvent) => {
		if (!isDragging) return;

		// 获取容器总宽度
		const containerWidth = document.querySelector('.flex.h-full.rounded-lg')?.clientWidth || 0;
		if (containerWidth === 0) return;

		// 计算鼠标位置对应的百分比
		const newLeftPanelWidth = Math.min(Math.max((e.clientX / containerWidth) * 100, 20), 80);

		// 更新宽度
		setLeftPanelWidth(newLeftPanelWidth);
	};

	const handleDragEnd = () => {
		setIsDragging(false);
	};

	// 添加鼠标移动和抬起事件监听
	useEffect(() => {
		if (isDragging) {
			document.addEventListener('mousemove', handleDrag);
			document.addEventListener('mouseup', handleDragEnd);
		}

		return () => {
			document.removeEventListener('mousemove', handleDrag);
			document.removeEventListener('mouseup', handleDragEnd);
		};
	}, [isDragging]);

  // 直接使用id获取问题详情
  useEffect(() => {
    // 获取问题详情
    fetchProblemDetail();

    // 模拟用户数据 - 确保默认有用户
		const mockUser = {id: 'user1', name: '小明同学'};
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        setUser(userData);
      } catch (error) {
        console.error('解析用户数据错误:', error);
        localStorage.removeItem('user');
        setUser(mockUser); // 使用默认用户
      }
    } else {
      // 如果本地存储中没有用户，使用默认用户
      setUser(mockUser);
      localStorage.setItem('user', JSON.stringify(mockUser));
    }
  }, [problemId]); // 依赖id而不是params

  const fetchProblemDetail = async (retry = 0) => {
    setLoading(true);
    setError('');

		const params = {
			id: problemId,
			envId: searchParams.get("envId")
		}
		CodingProblemService.getProblemDetail(params).then(resp => {
			console.log(resp)
			setProblem(resp.data)
		}).finally(() => {
			setLoading(false)
		})
	};

	const handleLanguageChange = useCallback((newLanguage: string) => {
		setLanguage(newLanguage);
	}, [problem]);

	const resetCode = () => {
	};

	// 更新 fillTestCase 函数，使用缓存
	const fillTestCase = (index: number) => {
		const examples = getExamples(problem?.problem.examples || '')

		// 如果找到示例，则填充
		if (examples.length > index) {
			setTestInput(examples[index].input);
			setTestOutput(examples[index].output);
			setFeedbackMessage(`已填充用例 ${index + 1}`);
			setTimeout(() => setFeedbackMessage(''), 1500);
      } else {
			setFeedbackMessage('未找到此用例，请尝试手动提取');
			setTimeout(() => setFeedbackMessage(''), 3000);
		}

	};

	const getTestResult = (key: string) => {
		const t = setInterval(async () => {
			try {
				const resp = await JudgeService.getTestResult(key)
				console.log(resp?.data.status, resp)
				if (resp?.data.status !== 5) {
					setRunResult(resp.data)
					setIsRunning(false)
					setActiveResultTab('execution-results')
					clearInterval(t)
				}
			} catch (err) {
				console.error('获取测试结果失败', err)
				setIsRunning(false)
				setActiveResultTab('execution-results')
				clearInterval(t)
			}
		}, 1000)
	}

	const getJudgeResult = (id: number) => {
		const t = setInterval(async () => {
			try {
				const resp = await JudgeService.getSubmitDetail(id)
				console.log(resp)
				if (resp.data.submission.status !== 5) {
					setIsRunning(false)
					setShowSubmissionModal(true)
					setSelectedSubmission(resp.data.submission)
					setActiveTab('submissions')
					setCurrentPage(1)
					fetchSubmissionList()
					clearInterval(t)
				}
			} catch (err) {
				console.error('获取提交结果失败', err)
				setIsRunning(false)
				clearInterval(t)
			}
		}, 1000)
	}

	// 修改运行代码函数，使用测试用例的输入
  const runCode = async () => {
    if (!checkAuth(2)) {
      return;
    }

    if (isMobile && !showCodeEditor) {
      setShowCodeEditor(true);
      return;
    }

    // 检查代码是否为空
    if (!code || code.trim() === '') {
      alert('代码不能为空');
      return;
    }

    setIsRunning(true);
    setRunResult(null);

    try {
      // 获取代码、语言、输入、输出，并打印到控制台
      console.log({
        操作: '运行代码',
        代码: code,
        语言: language,
        输入: testInput,
        预期输出: testOutput
      });

      const data = {
        pid: problem?.problem.id,
        code: code,
        language: language,
        userInput: testInput,
        expectedOutput: null,
        type: 'public',
        isRemoteJudge: false,
        cid: 0
      }
      
      await JudgeService.submitTest(data).then(resp => {
        getTestResult(resp.data)
      }).catch(err => {
        console.error('运行代码失败', err);
        setIsRunning(false);
        alert('运行代码失败，请稍后重试');
      });
    } catch (err) {
      console.error('运行代码失败', err);
      setIsRunning(false);
      alert('运行代码失败，请稍后重试');
    }
  };

	const searchParams = useSearchParams();
  const submitCode = async () => {
	  if (!checkAuth(2)) {
		  return;
	  }

	  if (isMobile && !showCodeEditor) {
      setShowCodeEditor(true);
      return;
    }

    // 检查代码是否为空
    if (!code || code.trim() === '') {
      alert('代码不能为空');
      return;
    }

    setIsRunning(true);

		// 获取代码、语言并打印到控制台
		console.log({
			操作: '提交代码',
			代码: code,
			语言: language,
			题目ID: id
		});
		const data = {
			pid: id,
			code: code,
			language: language,
			cid: 0,
			envType: searchParams.get('envType'),
			envId: searchParams.get('envId')
		}

		try {
			await JudgeService.judge(data)
				.then(result => {
					console.log(result);
					getJudgeResult(result.data.id);
				})
				.catch(err => {
					console.error('提交代码失败', err);
					setIsRunning(false);
					alert('提交代码失败，请稍后重试');
				});
		} catch (err) {
			console.error('提交代码失败', err);
			setIsRunning(false);
			alert('提交代码失败，请稍后重试');
		}
  };

  const toggleLeftPanel = () => {
    // 直接通过状态控制面板是否折叠
    setIsLeftPanelCollapsed(prev => !prev);
  };

  const toggleResultsPanel = () => {
		// 切换结果面板的折叠状态
    setIsResultsPanelCollapsed(prev => !prev);

		// 如果有必要，可以在这里添加其他相关状态的更新或DOM操作
		// 例如，滚动到合适的位置，更新其他元素的状态等
	};

	const getDifficultyColor = (difficulty?: number) => {
		if (!difficulty) {
			return ''
		}
		return CODING_PROBLEM_DIFFICULTY_MAP[difficulty].bgColor + ' ' + CODING_PROBLEM_DIFFICULTY_MAP[difficulty].color
	};

	// 使用useCallback包装setCode函数，避免不必要的重新渲染
	const handleCodeChange = useCallback((value: string) => {
		setCode(value);
	}, []);

	// 处理每页记录数变化
  const handlePageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setCurrentPage(1); // 重置为第一页
  };

  if (!id) {
    return <div className="flex justify-center items-center h-screen">
      <div className="text-xl">正在加载题目...</div>
    </div>;
  }

  // 添加状态管理题解列表
  const [solutionList, setSolutionList] = useState<ProblemSolution[]>([]);
  const [solutionTotal, setSolutionTotal] = useState<number>(0);
  const [solutionPage, setSolutionPage] = useState<number>(1);
  const [solutionPageSize, setSolutionPageSize] = useState<number>(5);
  const [loadingSolutions, setLoadingSolutions] = useState<boolean>(false);
  const [hasMoreSolutions, setHasMoreSolutions] = useState<boolean>(true);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement>(null);
  // 添加状态追踪每个题解的展开/收起状态
  const [expandedSolutions, setExpandedSolutions] = useState<{[key: number]: boolean}>({});

  // 添加获取题解列表的函数
  const fetchSolutionList = useCallback(() => {
    if (!problem?.problem.id) return;

    setLoadingSolutions(true);
    ProblemSolutionService.getProblemSolution({
      pageNum: solutionPage,
      pageSize: solutionPageSize,
      pid: problem?.problem.id | 0
    })
    .then(resp => {
      // 根据接口返回数据结构进行处理
      // 修复：接口返回的是分页结果对象，不是ProblemSolution类型
      const newSolutions = resp.data.items || [];
      setSolutionList(prev => solutionPage === 1 ? newSolutions : [...prev, ...newSolutions]);
      setSolutionTotal(resp.data.total || 0);
      setHasMoreSolutions(newSolutions.length === solutionPageSize && (solutionPage * solutionPageSize) < (resp.data.total || 0));
    })
    .catch(err => {
      console.error('获取题解列表失败:', err);
    })
    .finally(() => {
      setLoadingSolutions(false);
    });
  }, [problem?.problem.id, solutionPage, solutionPageSize]);

  // 使用IntersectionObserver检测滚动到底部
  useEffect(() => {
    // 创建观察器
    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting && !loadingSolutions && hasMoreSolutions) {
          setSolutionPage(prev => prev + 1);
        }
      },
      { threshold: 0.1 }
    );

    // 保存观察器实例
    observerRef.current = observer;

    // 观察加载更多元素
    const currentLoadMoreRef = loadMoreRef.current;
    if (currentLoadMoreRef) {
      observer.observe(currentLoadMoreRef);
    }

    // 清理观察器
    return () => {
      if (currentLoadMoreRef && observerRef.current) {
        observerRef.current.unobserve(currentLoadMoreRef);
      }
    };
  }, [loadingSolutions, hasMoreSolutions]);

  useEffect(() => {
    // 当activeTab切换到solution时获取题解列表
    if (activeTab === 'solution') {
      if (solutionList.length === 0) {
        // 重置分页并获取第一页数据
        setSolutionPage(1);
        fetchSolutionList();
      }
    }
  }, [activeTab, fetchSolutionList, solutionList.length]);

  // 当页码变化时获取数据
  useEffect(() => {
    if (activeTab === 'solution' && solutionPage > 1) {
      fetchSolutionList();
    }
  }, [activeTab, solutionPage, fetchSolutionList]);

  // 处理每页数量变化
  const handleSolutionPageSizeChange = (newSize: number) => {
    setSolutionPageSize(newSize);
    setSolutionPage(1); // 重置为第一页
    setSolutionList([]); // 清空现有列表
    setHasMoreSolutions(true); // 重置加载更多状态
  };

  // 添加切换题解展开/收起状态的函数
  const toggleSolutionExpand = (solutionId: number) => {
    setExpandedSolutions(prev => ({
      ...prev,
      [solutionId]: !prev[solutionId]
    }));
  };

  // 监听 urlChange 事件
  useEffect(() => {
    const handleUrlChange = (event: CustomEvent<{ problemId: string, problemType: number }>) => {
			if (event.detail.problemType === 2) {
				setProblemId(event.detail.problemId)
			}
    };

    window.addEventListener('urlChange', handleUrlChange as EventListener);
  }, [params.id]);

  return (
    <div className="h-screen max-h-screen w-full overflow-hidden bg-gray-50 pb-4">
      {/* 移动端推荐使用电脑提示 */}
      {isMobile && !hideDesktopTip && (
        <div className="fixed top-0 left-0 right-0 bg-yellow-50 border-b border-yellow-200 p-3 z-50 shadow-md">
          <div className="flex justify-between items-center">
            <div className="flex items-center text-yellow-800">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
              <span className="text-sm font-medium">建议使用电脑浏览器打开，获得更好的编程体验</span>
            </div>
            <button
              onClick={() => setHideDesktopTip(true)}
              className="text-yellow-700 hover:text-yellow-900"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* 主要内容容器 */}
      <div className="w-full h-full px-4 py-2 pb-4 overflow-hidden flex flex-col">
        {/* 错误和加载状态 */}
        {error && (
          <div className="bg-red-50 p-3 rounded-lg text-red-600 mb-2 text-sm">
            {error}
          </div>
        )}

        {loading && (
          <div className="flex-grow flex items-center justify-center">
            <div className="bg-white rounded-lg shadow-md p-8 text-center">
							<div
								className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600 mb-2"></div>
              <p className="text-gray-600">加载中...</p>
            </div>
          </div>
        )}

        {/* 题目详情 - 确保高度填满剩余空间 */}
        {!loading && !error && problem && (
          <div className="flex h-full rounded-lg overflow-hidden">
            {/* 左侧面板 - 使用动态宽度 */}
            <div className={`
              ${isLeftPanelCollapsed ? 'w-16 flex-none' : ''} 
              ${isMobile ? 'w-full' : ''}
              bg-white shadow-md
              transition-all duration-300 ease-in-out
            `}
						     ref={leftPanelRef}
						     style={{
							     width: isMobile ? '100%' : (isLeftPanelCollapsed ? '4rem' : `${leftPanelWidth}%`),
							     transition: isDragging ? 'none' : 'all 0.3s',
							     display: 'flex',
							     flexDirection: 'column',
							     height: '100%',
							     overflow: 'hidden' // 确保外层容器不溢出
						     }}>
              <div className="flex flex-col h-full">
								{/* 选项卡导航栏 - 不折叠，固定高度 */}
                <div className={`
                  ${isLeftPanelCollapsed ? 'border-r' : 'border-b'} 
                  border-gray-200 shrink-0
                `}>
                  <div className={`
                    ${isLeftPanelCollapsed ? 'flex-col py-2' : 'px-6 py-1'} 
                    flex justify-between items-center
                  `}>
                    <ul className={`
                      ${isLeftPanelCollapsed ? 'flex-col w-full mb-0 space-y-4' : 'flex -mb-px'} 
                    `} role="tablist">
                      <Tab
                        id="description"
                        label="题目描述"
                        isActive={activeTab === 'description'}
                        onClick={() => setActiveTab('description')}
                        isCollapsed={isLeftPanelCollapsed}
                      />
                      <Tab
                        id="solution"
                        label="题解"
                        isActive={activeTab === 'solution'}
                        onClick={() => {
													if (!checkAuth(2)) {
														return;
													}
	                        setActiveTab('solution')
                        }}
                        isCollapsed={isLeftPanelCollapsed}
                      />
                      <Tab
                        id="submissions"
                        label="提交记录"
                        isActive={activeTab === 'submissions'}
                        onClick={() => {
													if (!checkAuth()) {
														return
													}
	                        setActiveTab('submissions')
                        }}
                        isCollapsed={isLeftPanelCollapsed}
                      />
                    </ul>
                    {/* 在移动端隐藏收起按钮 */}
                    {!isMobile && (
                      <button
                        className={`${isLeftPanelCollapsed ? 'mt-4' : ''} text-gray-500 hover:text-gray-700 focus:outline-none`}
                        onClick={toggleLeftPanel}
                        title={isLeftPanelCollapsed ? "展开题目面板" : "折叠题目面板"}
                      >
                        <FontAwesomeIcon icon={isLeftPanelCollapsed ? faChevronRight : faChevronLeft}/>
                      </button>
                    )}
                  </div>
                </div>

								{/* 可折叠的内容部分 - 这里是滚动的关键部分 */}
                <div className={`panel-content ${isLeftPanelCollapsed ? 'hidden' : 'flex flex-col flex-grow'}`}>
									{/* 题目标题栏 - 固定不滚动 */}
                  <div className="px-6 py-4 border-b border-gray-200 shrink-0">
                    <div className="flex justify-between items-start">
                      <div>
												<h1 className="text-2xl font-bold text-gray-800">{problem?.problem.id}.{problem?.problem.title}</h1>
                        <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                          <span className={`px-2 py-1 text-xs rounded-md ${getDifficultyColor(problem?.problem.difficulty)}`}>
                            {problem?.problem.difficulty && CODING_PROBLEM_DIFFICULTY_MAP[problem?.problem.difficulty].text}
                          </span>
                          <span className="flex items-center">
                            <FontAwesomeIcon icon={faCheckCircle} className="mr-1 text-green-500"/>
                            通过率: {problem.statistics?.commitAmt && problem.statistics?.acceptAmt ?
	                          `${Math.round((problem.statistics.acceptAmt / problem.statistics.commitAmt) * 100)}%` :
	                          '-'
                          }
                          </span>
                          <span className="flex items-center">
                            <FontAwesomeIcon icon={faUser} className="mr-1"/>
                            提交数: {problem.statistics?.commitAmt || 0}
                          </span>
                          {problem.tags.length > 0 && (
	                          problem.tags.map((tag) => (
		                          <div key={tag.id}>
			                          <span className="flex items-center">
		                              <FontAwesomeIcon icon={faTag} className="mr-1"/>
						                          {tag.name}
		                            </span>
		                          </div>
	                          ))
                          )}
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <FavoriteButton
                          dataType={2}
                          dataId={problem.problem.id}
                          isCollected={problem.isCollected}
                          className="text-xl"
                        />
                      </div>
                    </div>
                  </div>

                  {/* 题目内容区域 - 可滚动，占满剩余高度 */}
									<div className="overflow-auto px-6 py-4" style={{'flex': '1 1 auto', height: '0'}}>
                    {/* 题目描述标签页 */}
                    {activeTab === 'description' && (
                      <div className="space-y-5 pb-10">
                        <div>
                          <h2 className="text-lg font-semibold text-gray-800 mb-2">题目描述</h2>
													<MarkdownRenderer content={problem?.problem.description || ''}/>
													{/*<p className="text-gray-700">*/}
													{/*  {problem?.problem.description}*/}
													{/*</p>*/}
                        </div>

                        <div>
                          <h2 className="text-lg font-semibold text-gray-800 mb-2">输入格式</h2>
	                        <MarkdownRenderer content={problem?.problem.input || ''}/>
                        </div>

                        <div>
                          <h2 className="text-lg font-semibold text-gray-800 mb-2">输出格式</h2>
	                        <MarkdownRenderer content={problem?.problem.output || ''}/>
                        </div>

												{getExamples(problem.problem.examples).map((example, index) => (
                          <div key={index} className="mb-4">
                            <h2 className="text-lg font-semibold text-gray-800 mb-2">示例 {index + 1}</h2>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              <div>
                                <h3 className="font-medium text-gray-700 mb-1">输入:</h3>
                                <div className="bg-gray-100 p-3 rounded-md">
																	<pre
																		className="text-gray-800 whitespace-pre-wrap text-sm font-mono">{example.input}</pre>
                                </div>
                              </div>
                              <div>
                                <h3 className="font-medium text-gray-700 mb-1">输出:</h3>
                                <div className="bg-gray-100 p-3 rounded-md">
																	<pre
																		className="text-gray-800 whitespace-pre-wrap text-sm font-mono">{example.output}</pre>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}

												{problem?.problem.hint && (
                          <div>
                            <h2 className="text-lg font-semibold text-gray-800 mb-2">提示</h2>
	                          <MarkdownRenderer content={problem?.problem.hint || ''}/>
                          </div>
                        )}
                      </div>
                    )}

                    {/* 题解标签页 */}
                    {activeTab === 'solution' && (
                      <div className="space-y-5">
                        <div className="mb-6 flex justify-between items-center">
                          <h2 className="text-xl font-semibold text-gray-800">题解大全 <span className="text-gray-500 text-sm font-normal ml-2">共 {solutionTotal} 篇</span></h2>

                          {/*<div className="flex space-x-2">*/}
                          {/*  <div className="relative">*/}
                          {/*    <select className="appearance-none bg-white border border-gray-300 text-gray-700 py-2 px-4 pr-8 rounded-lg leading-tight focus:outline-none focus:border-indigo-500">*/}
                          {/*      <option>按热度排序</option>*/}
                          {/*      <option>按时间排序</option>*/}
                          {/*      <option>按难度排序</option>*/}
                          {/*    </select>*/}
                          {/*    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">*/}
                          {/*      <FontAwesomeIcon icon={faChevronDown} className="text-xs" />*/}
                          {/*    </div>*/}
                          {/*  </div>*/}
                          {/*  <Link href={`/coding-problems/${id}/submit-solution`}>*/}
                          {/*    <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">*/}
                          {/*      <FontAwesomeIcon icon={faCode} className="mr-1" /> 写题解*/}
                          {/*    </button>*/}
                          {/*  </Link>*/}
                          {/*</div>*/}
                        </div>

                        {/* 题解筛选标签 */}
                        {/*<div className="flex flex-wrap gap-2 mb-6">*/}
                        {/*  <span className="px-3 py-1 bg-indigo-600 text-white text-sm rounded-full">全部</span>*/}
                        {/*  <span className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200 cursor-pointer">官方题解</span>*/}
                        {/*  <span className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200 cursor-pointer">Python</span>*/}
                        {/*  <span className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200 cursor-pointer">C++</span>*/}
                        {/*  <span className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200 cursor-pointer">Java</span>*/}
                        {/*  <span className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200 cursor-pointer">入门</span>*/}
                        {/*  <span className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200 cursor-pointer">详细讲解</span>*/}
                        {/*</div>*/}

                        {/* 题解列表 */}
                        <div className="space-y-6">
                          {loadingSolutions ? (
                            <div className="text-center py-12">
                              <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600 mb-2"></div>
                              <p className="text-gray-600">加载题解中...</p>
                            </div>
                          ) : solutionList.length > 0 ? (
                            <>
                              {solutionList.map((solution, index) => (
                                <div key={solution.id} className="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
                                  <div className="p-5 border-b border-gray-100">
                                    <div className="flex justify-between">
                                      <div className="flex items-center space-x-3">
                                        <div>
                                          <h3 className="text-lg font-bold text-gray-900 hover:text-indigo-600">{solution.title || `${solution.author?.nickname || "匿名用户"}的题解`}</h3>
                                          <div className="flex items-center text-sm text-gray-500">
                                            <span className="flex items-center">
                                              <Image
                                                className="h-5 w-5 rounded-full mr-1"
                                                src={solution.author?.avatar || 'https://static.zhubaopen.com/avatar/default.png'}
                                                alt={solution.author?.nickname || "作者头像"}
                                                width={20}
                                                height={20}
                                              />
                                              {solution.author?.nickname || "匿名用户"}
                                            </span>
                                            <span className="mx-2">·</span>
                                            <span>{new Date(solution.createdTime).toLocaleDateString('zh-CN')}</span>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>

                                  <div className="relative">
                                    <div
                                      className={`overflow-hidden ${
                                        expandedSolutions[solution.id] ? 'max-h-full' : 'max-h-[200px]'
                                      }`}
                                    >
                                      <div className="p-5">
                                        <MarkdownRenderer content={solution.content} className="prose prose-indigo max-w-none" />
                                      </div>
                                    </div>

                                    {/* 渐变遮罩层，在收起状态下显示 */}
                                    {!expandedSolutions[solution.id] && (
                                      <div className="absolute bottom-10 left-0 right-0 h-16 bg-gradient-to-t from-white to-transparent pointer-events-none"></div>
                                    )}

                                    {/* 展开/收起按钮 */}
                                    <div className="text-center py-2 border-t border-gray-100">
                                      <button
                                        onClick={() => toggleSolutionExpand(solution.id)}
                                        className="text-indigo-600 hover:text-indigo-800 text-sm font-medium focus:outline-none cursor-pointer z-10 relative"
                                      >
                                        {expandedSolutions[solution.id] ? (
                                          <>
                                            <FontAwesomeIcon icon={faChevronUp} className="mr-1" />
                                            收起
                                          </>
                                        ) : (
                                          <>
                                            <FontAwesomeIcon icon={faChevronDown} className="mr-1" />
                                            展开全文
                                          </>
                                        )}
                                      </button>
                                    </div>
                                  </div>

                                  {/*<div className="px-5 py-3 bg-gray-50 border-t border-gray-100 flex justify-between items-center">*/}
                                  {/*  <div className="flex items-center space-x-4">*/}
                                  {/*    <button className="text-gray-500 hover:text-indigo-600 flex items-center">*/}
                                  {/*      <FontAwesomeIcon icon={faCheckCircle} className="mr-1" />*/}
                                  {/*      <span>点赞</span>*/}
                                  {/*    </button>*/}
                                  {/*    <button className="text-gray-500 hover:text-indigo-600 flex items-center">*/}
                                  {/*      <FontAwesomeIcon icon={faBookmark} className="mr-1" />*/}
                                  {/*      <span>收藏</span>*/}
                                  {/*    </button>*/}
                                  {/*    <button className="text-gray-500 hover:text-indigo-600 flex items-center">*/}
                                  {/*      <FontAwesomeIcon icon={faComments} className="mr-1" />*/}
                                  {/*      <span>评论</span>*/}
                                  {/*    </button>*/}
                                  {/*  </div>*/}
                                  {/*</div>*/}
                                </div>
                              ))}

                              {/* 无限滚动加载指示器 */}
                              <div
                                ref={loadMoreRef}
                                className="py-8 flex justify-center items-center"
                              >
                                {loadingSolutions && (
                                  <div className="flex items-center">
                                    <div className="inline-block animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-indigo-600 mr-2"></div>
                                    <span className="text-gray-600">加载更多题解...</span>
                                  </div>
                                )}
                                {!loadingSolutions && hasMoreSolutions && (
                                  <span className="text-gray-500">向下滚动加载更多</span>
                                )}
                                {!loadingSolutions && !hasMoreSolutions && solutionList.length > 0 && (
                                  <span className="text-gray-500">已加载全部题解</span>
                                )}
                              </div>
                            </>
                          ) : (
                            <div className="text-center py-8 text-gray-500">
                              <div className="text-5xl mb-4">📝</div>
                              <p className="mb-2">暂无题解</p>
                              {/*<Link href={`/coding-problems/${id}/submit-solution`}>*/}
                              {/*  <button className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm">*/}
                              {/*    成为第一个提交题解的人*/}
                              {/*  </button>*/}
                              {/*</Link>*/}
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* 提交记录标签页 */}
                    {activeTab === 'submissions' && (
											<div className="space-y-4 pb-8">
												<div className="flex flex-col md:flex-row md:justify-between md:items-center gap-3 mb-4">
													<h2 className="text-lg font-semibold text-gray-800">提交记录</h2>
												</div>

												{user ? (
													<div className="overflow-hidden rounded-lg border border-gray-200 shadow relative">
														{/* 加载状态，覆盖在表格上方 */}
														{isLoadingSubmissions && (
															<div className="absolute inset-0 bg-white/80 flex items-center justify-center z-10">
																<div className="text-center">
																	<div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600 mb-4"></div>
																	<p className="text-gray-700 font-medium">正在加载提交记录...</p>
																</div>
															</div>
														)}

														<table className="min-w-full divide-y divide-gray-200">
															<thead className="bg-gray-50">
															<tr>
																<th scope="col"
																    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态
																</th>
																<th scope="col"
																    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">语言
																</th>
																<th scope="col"
																    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">执行时间
																</th>
																<th scope="col"
																    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">内存
																</th>
																<th scope="col"
																    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">提交时间
																</th>
																<th scope="col"
																    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作
																</th>
															</tr>
															</thead>
															<tbody className="bg-white divide-y divide-gray-200">
																{/* 过滤后的提交记录，并应用分页 */}
																{submissionList && submissionList
																	.map(submission => (
																		<tr
																			key={submission.id}
																			className="hover:bg-gray-50 transition-colors"
																		>
																			<td className="px-4 py-3 whitespace-nowrap">
																				<span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusStyle(submission.status)}`}>
																					{getStatusText(submission.status)}
																				</span>
																			</td>
																			<td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{submission.language}</td>
																			<td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{submission.time}ms</td>
																			<td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatKb(submission.memory)}</td>
																			<td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{submission.submitTime}</td>
																			<td className="px-4 py-3 whitespace-nowrap text-sm text-indigo-600 hover:text-indigo-800">
																				<button
																					className="font-medium"
																					onClick={() => {
																						setSelectedSubmission(submission);
																						setShowSubmissionModal(true);
																					}}
																				>
																					查看详情
																				</button>
																			</td>
																		</tr>
																	))}
															</tbody>
														</table>

														{/* 添加分页组件 */}
														<div className="px-4 py-3 bg-white border-t border-gray-200">
															<div className="flex flex-col sm:flex-row justify-between items-center mb-2">
																<div className="flex items-center text-sm text-gray-600 mb-2 sm:mb-0">
																	<span className="mr-2">每页显示:</span>
																	<select
																		className="border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
																		value={pageSize}
																		onChange={(e) => handlePageSizeChange(Number(e.target.value))}
																	>
																		{pageSizeOptions.map(size => (
																			<option key={size} value={size}>{size} 条</option>
																		))}
																	</select>
																</div>
															</div>

															<Pagination
																currentPage={currentPage}
																totalItems={submissionTotal}
																pageSize={pageSize}
																onPageChange={setCurrentPage}
															/>
														</div>

														{/* 提交为空的状态 */}
														{!isLoadingSubmissions && (submissionList == null || submissionList.length === 0) && (
															<div className="text-center py-8 text-gray-500 bg-white">
																<p className="mb-2">暂无符合条件的提交记录</p>
                      </div>
                    )}

														{/* 移除之前的加载状态 */}
													</div>
												) : (
													<div className="text-center py-8 text-gray-500 bg-white rounded-lg border border-gray-200 shadow-sm">
														<div className="text-5xl mb-4">📊</div>
														<p className="mb-2">登录后可查看提交记录</p>
														<button className="mt-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm">
															登录
														</button>
													</div>
												)}
											</div>
										)}

										{/* 添加提交详情模态框 */}
										{showSubmissionModal && selectedSubmission && (
                      <SubmissionDetailModal
                        submission={selectedSubmission}
                        onClose={() => setShowSubmissionModal(false)}
                      />
                    )}

                    {/* 讨论标签页 */}
                    {activeTab === 'discussions' && (
                      <div className="p-6">
                        <div className="text-center py-6">
                          <p className="text-gray-600">请登录后参与讨论</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

						{/* 可拖动的分隔线 */}
						{!isMobile && (
							<div
								className="w-1 bg-gray-200 hover:bg-indigo-400 cursor-col-resize relative z-10 transition-colors"
								onMouseDown={handleDragStart}
							>
								{/* 拖动时的辅助线 */}
								{isDragging && (
									<div className="absolute inset-0 bg-indigo-500 opacity-50" style={{width: '3px', left: '-1px'}}></div>
								)}
							</div>
						)}

						{/* 右侧代码编辑区域 - 在移动设备上隐藏 */}
						{!isMobile && (
							<div className="flex flex-col h-full overflow-hidden pb-5" style={{
								width: isLeftPanelCollapsed ? 'calc(100% - 4rem - 4px)' : `calc(${100 - leftPanelWidth}% - 4px)`
							}}>
								{/* 将右侧区域分为编辑器和结果面板两部分，使用flex来分配空间 */}
								<div className="flex flex-col h-full">
									{/* 编辑器容器 - 使用flex-grow让它占据除结果面板外的所有空间 */}
									<div className="bg-white shadow-md overflow-hidden flex flex-col mb-2" style={{
										height: isResultsPanelCollapsed ? 'calc(100% - 54px)' : '60%',
										transition: 'min-height 0.3s'
									}}>
										{/* 语言选择区域 */}
                <div className="shrink-0 border-b border-gray-200">
                  {/* 使用CodeEditor内置的语言选择而不是单独实现 */}
                </div>

										{/* 编辑器区域 */}
                <div className="flex-1 overflow-auto min-h-0">
                  <CodeEditor
                    code={code}
                    language={language}
                    onChange={handleCodeChange}
                    onLanguageChange={handleLanguageChange}
										darkMode={false}
                    className="h-full"
                    showLanguageSelect={true}
                  />
                </div>

										{/* 按钮区域 */}
										<div className="px-3 py-2 border-t border-gray-200 flex justify-between items-center bg-white">
											<span></span>
											{/*<button*/}
											{/*  className="px-3 py-1.5 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm"*/}
											{/*  onClick={resetCode}*/}
											{/*>*/}
											{/*  <FontAwesomeIcon icon={faSyncAlt} className="mr-1" /> 重置*/}
											{/*</button>*/}
                  <div className="space-x-2">
                    <button
                      className="px-3 py-1.5 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 text-sm"
                      onClick={runCode}
                      disabled={isRunning || !testInput.trim() || !code || code.trim() === ''}
                    >
                      {isRunning ? (
                        <>
                          <span className="inline-block animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-indigo-700 mr-2"></span> 运行中...
                        </>
                      ) : (
                        <>
                          <FontAwesomeIcon icon={faPlay} className="mr-2"/> 运行
                        </>
                      )}
                    </button>
                    <button
                      className="px-3 py-1.5 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm"
                      onClick={submitCode}
                      disabled={isRunning}
                    >
                      {isRunning ? (
                        <>
                          <span className="inline-block animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></span> 提交中...
                        </>
                      ) : (
                        <>
                          <FontAwesomeIcon icon={faPaperPlane} className="mr-2"/> 提交
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>

								{/* 测试用例/执行结果区域 - 固定高度，不使用flex-grow，避免超出容器 */}
              <div
									className="bg-white shadow-md overflow-hidden flex flex-col"
                ref={resultsContainerRef}
                style={{
                  height: isResultsPanelCollapsed ? '40px' : 'calc(40% - 10px)',
                  maxHeight: isResultsPanelCollapsed ? '40px' : '350px'
                }}
              >
									{/* 选项卡导航 */}
									<div
										className="border-t border-l border-r border-gray-200 flex justify-between items-center h-10 bg-white">
                  <div className="flex">
                    <button
                      className={`py-2 px-4 text-sm font-medium ${
                        activeResultTab === 'test-cases' 
                          ? 'border-b-2 border-indigo-600 text-indigo-600' 
                          : 'text-gray-500 hover:text-gray-700'
                      }`}
                      onClick={() => setActiveResultTab('test-cases')}
                    >
                      测试用例
                    </button>
                    <button
                      className={`py-2 px-4 text-sm font-medium ${
                        activeResultTab === 'execution-results' 
                          ? 'border-b-2 border-indigo-600 text-indigo-600' 
                          : 'text-gray-500 hover:text-gray-700'
                      }`}
                      onClick={() => setActiveResultTab('execution-results')}
                    >
                      执行结果
                    </button>
                  </div>
                  <button
                    className="mr-4 text-gray-500 hover:text-gray-700 focus:outline-none"
                    onClick={toggleResultsPanel}
                    title={isResultsPanelCollapsed ? "展开结果面板" : "折叠结果面板"}
                  >
											<FontAwesomeIcon icon={isResultsPanelCollapsed ? faChevronUp : faChevronDown}/>
                  </button>
                </div>

									{/* 内容区域 */}
									<div className="overflow-auto bg-white border-l border-r border-b border-gray-200" style={{
										height: isResultsPanelCollapsed ? '0' : 'calc(100% - 54px)',
										overflow: 'auto'
									}}>
										{/* 测试用例内容 - 取消注释并修改为可编辑的输入框 */}
										{activeResultTab === 'test-cases' && problem && (
                      <div className="p-4">
												<div className="flex justify-between items-center mb-4">
													<h3 className="font-medium text-gray-800">测试用例</h3>
													<div className="flex space-x-2">
														{/* 测试用例按钮 */}

														{
															problem?.problem.examples && getExamples(problem.problem.examples).map((_, index) => (
																<button
																	key={index}
																	onClick={() => fillTestCase(index)}
																	className="px-2 py-1 bg-indigo-50 text-indigo-600 rounded-md text-sm hover:bg-indigo-100"
																>
																	用例 {index + 1}
																</button>
															))
														}


                                </div>
                                </div>

												{/* 反馈消息 */}
												{feedbackMessage && (
													<div className="mb-3 px-3 py-2 bg-blue-50 text-blue-700 text-sm rounded-md animate-fadeIn">
														{feedbackMessage}
                              </div>
												)}

												<div>
													<div>
														<div className="flex justify-between items-center mb-2">
															<label htmlFor="test-input" className="font-medium text-gray-700">输入:</label>
															<button
																onClick={() => setTestInput('')}
																className="text-xs text-gray-500 hover:text-gray-700"
															>
																清空
															</button>
                            </div>
														<textarea
															id="test-input"
															value={testInput}
															onChange={(e) => setTestInput(e.target.value)}
															placeholder="请输入测试用例的输入数据..."
															className="w-full h-32 p-3 border border-gray-300 rounded-md bg-gray-50 font-mono text-sm resize-none focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
														/>
													</div>
												</div>

												<div className="mt-4 flex justify-end">
													<button
														onClick={runCode}
														disabled={isRunning || !testInput.trim() || !code || code.trim() === ''}
														className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:bg-indigo-300 disabled:cursor-not-allowed text-sm flex items-center"
													>
														{isRunning ? (
															<>
																<span
																	className="inline-block animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></span>
																运行中...
															</>
														) : (
															<>
																<FontAwesomeIcon icon={faPlay} className="mr-2"/>
																使用此用例运行
															</>
														)}
													</button>
                        </div>
                      </div>
                    )}

                    {/* 执行结果内容 */}
                    {activeResultTab === 'execution-results' && (
                      <div className="p-4">
                        {isRunning ? (
                          <div className="text-center py-4">
														<div
															className="inline-block animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-indigo-600 mb-2"></div>
                            <p className="text-gray-600">代码执行中...</p>
                          </div>
                        ) : runResult ? (
													<div>
														<div className={`flex gap-2 items-end`}>
															<span
																className={`text-2xl ${runResult.status === 0 ? 'text-green-500' : 'text-red-500'}`}>
                                  {(() => {
                                    const statusKey = String(runResult.status);
                                    return (JUDGE_STATUS as any)[statusKey]?.name || '未知状态';
                                  })()}
                                </span>
															<div className={`flex gap-4 text-gray-400 text-sm`}>
																<span>执行用时: {runResult.time}ms</span>
																<span>运行内存: {formatKb(runResult.memory)}</span>
                          </div>
														</div>

														{
															(runResult.status === -10 || runResult.status === -5 || runResult.status === -2 || runResult.status === 3 || runResult.status === 4 || runResult.status === 10) ?
																(runResult.stderr &&
                                    <div className={'bg-red-50 text-red-500 text-sm px-3 py-3 mt-2 rounded-xl'}
                                         style={{whiteSpace: 'pre-wrap'}}
                                         dangerouslySetInnerHTML={{__html: runResult.stderr}}></div>) :
																<div>
																	<div className={'mt-4'}>
																		<span className={'text-gray-400 text-sm'}>输入</span>
																		<div className={'bg-gray-100 px-4 py-3 rounded-xl mt-1'}>{runResult.userInput}</div>
																	</div>

																	<div className={'mt-4'}>
																		<span className={'text-gray-400 text-sm'}>输出</span>
																		<div
																			className={'bg-gray-100 px-4 py-3 rounded-xl mt-1'}>{runResult.userOutput}</div>
																	</div>
																	{
																		runResult.expectedOutput ? (
																			<div className={'mt-4'}>
																				<span className={'text-gray-400 text-sm'}>预期输出</span>
																				<div
																					className={'bg-gray-100 px-4 py-3 rounded-xl mt-1'}>{runResult.expectedOutput}</div>
																			</div>
																		) : null
																	}
																</div>
														}


													</div>
													// <div className={`p-3 rounded-md ${runResult.status === 'success' ? 'bg-green-50' : 'bg-red-50'}`}>
													//   <h3 className={`font-medium ${runResult.status === 'success' ? 'text-green-800' : 'text-red-800'} mb-2`}>
													//     {runResult.status === 'success' ? '执行成功' : '执行失败'}
													//   </h3>
													//   <pre className={`whitespace-pre-wrap text-sm ${runResult.status === 'success' ? 'text-green-700' : 'text-red-700'} font-mono`}>
													//     {runResult.status === 'success' ? runResult.output : runResult.error}
													//   </pre>
													// </div>
                        ) : (
                          <div className="text-center py-4 text-gray-500">
                            点击"运行"按钮测试您的代码
                          </div>
                        )}
                      </div>
                    )}
                  </div>
								</div>
              </div>
            </div>)}
          </div>
        )}

        {/* 移动设备上的浮动按钮 */}
        {isMobile && !loading && !error && problem && (
          <button
            className="fixed right-6 bottom-6 w-14 h-14 rounded-full bg-indigo-600 text-white shadow-lg flex items-center justify-center text-xl z-20 hover:bg-indigo-700"
            onClick={() => setShowCodeEditor(true)}
          >
            <FontAwesomeIcon icon={faCode} />
          </button>
        )}

        {/* 代码编辑器模态框 */}
        {showCodeEditor && isMobile && (
          <div className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-0">
            <div className="bg-white w-full h-full flex flex-col">
              <div className="flex justify-between items-center p-4 border-b border-gray-200">
                <h2 className="text-lg font-bold text-gray-800">编辑代码</h2>
                <button
                  className="text-gray-500 hover:text-gray-700"
                  onClick={() => setShowCodeEditor(false)}
                >
                  <FontAwesomeIcon icon={faTimes} />
                </button>
              </div>

              <div className="flex-1 overflow-hidden">
                <CodeEditor
                  code={code}
                  language={language}
                  onChange={handleCodeChange}
                  onLanguageChange={handleLanguageChange}
                  darkMode={false}
                  showLanguageSelect={true}
                />
              </div>

              {/* 测试用例输入区域 */}
              <div className="border-t border-gray-200">
                <div className="flex justify-between items-center p-4 cursor-pointer"
                     onClick={() => setIsTestInputCollapsed(!isTestInputCollapsed)}>
                  <h3 className="font-medium text-gray-700">测试用例输入</h3>
                  <FontAwesomeIcon
                    icon={isTestInputCollapsed ? faChevronDown : faChevronUp}
                    className="text-gray-500"
                  />
                </div>

                {!isTestInputCollapsed && (
                  <div className="px-4 pb-4">
                    <div className="flex justify-end items-center mb-2">
                      <div className="flex space-x-2">
                        {problem?.problem.examples && getExamples(problem.problem.examples).map((_, index) => (
                          <button
                            key={index}
                            onClick={() => fillTestCase(index)}
                            className="px-2 py-1 bg-indigo-50 text-indigo-600 rounded-md text-xs hover:bg-indigo-100"
                          >
                            用例 {index + 1}
                          </button>
                        ))}
                      </div>
                    </div>
                    <textarea
                      id="mobile-test-input"
                      value={testInput}
                      onChange={(e) => setTestInput(e.target.value)}
                      placeholder="请输入测试用例的输入数据..."
                      className="w-full h-20 p-3 border border-gray-300 rounded-md bg-gray-50 font-mono text-sm resize-none focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    />
                  </div>
                )}
              </div>

              <div className="p-4 border-t border-gray-200 flex justify-end space-x-3">
                <button
                  className="px-4 py-2 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200"
                  onClick={runCode}
                  disabled={isRunning || !testInput.trim() || !code || code.trim() === ''}
                >
                  {isRunning ? (
                    <>
                      <span className="inline-block animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-indigo-700 mr-2"></span> 运行中...
                    </>
                  ) : (
                    <>
                      <FontAwesomeIcon icon={faPlay} className="mr-2"/> 运行
                    </>
                  )}
                </button>
                <button
                  className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                  onClick={submitCode}
                  disabled={isRunning}
                >
                  {isRunning ? (
                    <>
                      <span className="inline-block animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></span> 提交中...
                    </>
                  ) : (
                    <>
                      <FontAwesomeIcon icon={faPaperPlane} className="mr-2"/> 提交
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
