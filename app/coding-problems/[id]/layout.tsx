"use client";

import React, {useState} from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Image from 'next/image';
import { faRocket, faChevronLeft, faChevronRight, faSearch, faFilter, faCheckCircle, faExternalLinkAlt, faCode, faBook, faTrophy, faUserGraduate, faHome, faListAlt, faNewspaper, faCrown } from '@fortawesome/free-solid-svg-icons';
import Link from 'next/link';
import UserInfo from '@/app/components/UserInfo';
import { useParams, useRouter } from 'next/navigation';
import { useSearchParams } from 'next/navigation';
import CodingProblemService from "@/app/service/coding-problem-service";
import LearningContextService, {LearningContext, LearningContextProblem} from "@/app/service/learning-context-service";
import ProblemListService from "@/app/service/problem-list-service";
import {Tag} from "@/app/service/tag-service";
import ProblemDrawer from '@/app/components/ProblemDrawer';

export interface Problem {
  id: string;
  title: string;
  difficulty: number;
  categoryName: number;
  tags: Tag[];
}

// 增强版的编程题详情页布局组件
export default function CodingProblemLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const params = useParams<{ id: string }>();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isDrawerOpen, setIsDrawerOpen] = React.useState(false);
  const [isMobile, setIsMobile] = React.useState(false);
  const [learningContext, setLearningContext] = useState<LearningContext | null>(null);
  const [currentProblemId, setCurrentProblemId] = useState(params.id);
  const [isLogoMenuOpen, setIsLogoMenuOpen] = useState(false);

  const queryLearningContext = () => {
    let filter = {}

    const savedFilters = sessionStorage.getItem(`problemFilters_2`);
    if (savedFilters) {
      const { keyword, tags, difficulty, status } = JSON.parse(savedFilters);
      filter = {
        keyword,
        tags,
        difficulty,
        status
      }
    }
    const data = {
      envId: searchParams.get("envId"),
      envType: searchParams.get("envType"),
      currentQuestion: Number(currentProblemId),
      currentQuestionType: 2,
      filter
    }
    LearningContextService.query(data).then(resp => {
      setLearningContext(resp.data)
    })
  };
  // 获取学习上下文
  React.useEffect(() => {
    if (currentProblemId) {
      queryLearningContext();
    }
  }, [currentProblemId, searchParams]);

  // 导航到上一题或下一题
  const navigate = (problem?: LearningContextProblem) => {
    if (!problem) return;

    // 保持当前的查询参数
    const query = new URLSearchParams(searchParams.toString());
    if (problem.type == 1) {
      router.push(`/basic-problems/${problem.id}?${query.toString()}`);
    } else {
      router.push(`/coding-problems/${problem.id}?${query.toString()}`);
    }
  };

  // 打开抽屉
  function openDrawer() {
    setIsDrawerOpen(true);
  }

  // 使用useEffect隐藏页脚并设置主体样式
  React.useEffect(() => {
    // 检测移动设备
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    // 获取页脚元素
    const footer = document.querySelector('footer');
    // 获取公共header
    const navbar = document.querySelector('nav');
    const navbarPlaceholder = document.getElementById('navbar-placeholder');

    // 仅在非移动设备上隐藏导航栏
    if (footer) {
      footer.style.display = 'none';
    }
    if (!isMobile) {
      if (navbar) {
        navbar.style.display = 'none';
      }
      if (navbarPlaceholder) {
        navbarPlaceholder.style.display = 'none';
      }
    }

    // 获取主体元素
    const mainElement = document.querySelector('main');
    if (mainElement) {
      // 保存原始样式
      const originalOverflow = mainElement.style.overflow;
      const originalHeight = mainElement.style.height;
      const originalPadding = mainElement.style.padding;

      // 修改主体样式 - 移动端时需要考虑公共header的高度
      mainElement.style.overflow = 'hidden';
      if (isMobile) {
        // 移动端使用公共导航栏，需要减去导航栏高度
        mainElement.style.height = 'calc(100vh - 56px)'; // 假设公共导航栏高度是56px
        mainElement.style.padding = '0';
      } else {
        mainElement.style.height = '100vh';
        mainElement.style.padding = '0 0 16px 0';
      }

      // 添加额外的溢出控制
      document.body.style.overflow = 'hidden';

      // 组件卸载时恢复
      return () => {
        if (footer) {
          footer.style.display = '';
        }
        if (navbar) {
          navbar.style.display = '';
        }
        if (navbarPlaceholder) {
          navbarPlaceholder.style.display = '';
        }
        if (mainElement) {
          mainElement.style.overflow = originalOverflow;
          mainElement.style.height = originalHeight;
          mainElement.style.padding = originalPadding;
        }
        document.body.style.overflow = '';
        window.removeEventListener('resize', checkMobile);
      };
    }

    // 如果没有找到主体元素，仍然恢复页脚和导航栏
    return () => {
      if (footer) {
        footer.style.display = '';
      }
      if (navbar) {
        navbar.style.display = '';
      }
      if (navbarPlaceholder) {
        navbarPlaceholder.style.display = '';
      }
      window.removeEventListener('resize', checkMobile);
    };
  }, [isMobile]);

  const handleChange = (problemType: number, problemId: string) => {
    setCurrentProblemId(problemId)
  }

  // 设置页面样式
  return (
    <div className="h-screen w-full overflow-hidden flex flex-col">
      {/* 仅在非移动设备上显示自定义header */}
      {!isMobile && (
        <header className="bg-white border-b border-gray-200 h-14 flex items-center px-4 shrink-0">
          <div className="flex items-center justify-between w-full">
            {/* 左侧导航 */}
            <div className="flex items-center space-x-2">
              <div
                className="relative"
                onMouseEnter={() => setIsLogoMenuOpen(true)}
                onMouseLeave={() => setIsLogoMenuOpen(false)}
              >
                <Link
                  href="/"
                  className="flex items-center space-x-2"
                >
                  <Image src="/logo.png" alt="信竞星球" width={100} height={40} />
                </Link>

                {/* Logo悬停菜单 */}
                {isLogoMenuOpen && (
                  <div className="absolute top-full left-0 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                    {/* 添加一个透明的连接区域，填充logo和菜单之间的空隙 */}
                    <div className="absolute -top-2 left-0 w-full h-2"></div>

                    <Link href="/" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-600">
                      <FontAwesomeIcon icon={faHome} className="mr-2 text-indigo-600" />
                      <span>首页</span>
                    </Link>
                    <Link href="/basic-problems" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-600">
                      <FontAwesomeIcon icon={faBook} className="mr-2 text-indigo-600" />
                      <span>基础题库</span>
                    </Link>
                    <Link href="/coding-problems" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-600">
                      <FontAwesomeIcon icon={faCode} className="mr-2 text-indigo-600" />
                      <span>编程题库</span>
                    </Link>
                    <Link href="/problem-lists" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-600">
                      <FontAwesomeIcon icon={faListAlt} className="mr-2 text-indigo-600" />
                      <span>题单</span>
                    </Link>
                    <Link href="/exams" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-600">
                      <FontAwesomeIcon icon={faTrophy} className="mr-2 text-indigo-600" />
                      <span>模拟考试</span>
                    </Link>
                    <Link href="/news-list" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-600">
                      <FontAwesomeIcon icon={faNewspaper} className="mr-2 text-indigo-600" />
                      <span>编程资讯</span>
                    </Link>
                    <Link href="/membership" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-600">
                      <FontAwesomeIcon icon={faCrown} className="mr-2 text-indigo-600" />
                      <span>会员服务</span>
                    </Link>
                  </div>
                )}
              </div>
              <div className="relative group rounded hover:bg-black/5 px-3 py-2">
                <button
                  onClick={openDrawer}
                  className="text-gray-600 hover:text-indigo-600 max-w-[120px] text-sm text-ellipsis overflow-hidden whitespace-nowrap"
                  title={learningContext?.envTitle || '题库'}
                >
                  {learningContext?.envTitle || '题库'}
                </button>
                {/* 新标签页打开按钮 */}
                <Link
                  href={learningContext?.envId ? `/problem-lists/${learningContext.envId}` : '/coding-problems'}
                  target="_blank"
                  className="left-full top-1/2 -translate-y-1/2 ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-gray-400 hover:text-indigo-600"
                  title="在新标签页打开"
                >
                  <FontAwesomeIcon icon={faExternalLinkAlt} className="text-sm" />
                </Link>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  className={`p-2 hover:bg-black/5 px-4 rounded ${learningContext?.previousQuestion ? 'text-gray-600 hover:text-indigo-600' : 'text-gray-300 cursor-not-allowed'}`}
                  onClick={() => navigate(learningContext?.previousQuestion)}
                  disabled={!learningContext?.previousQuestion}
                  title={learningContext?.previousQuestion ? "上一题" : "没有上一题"}
                >
                  <FontAwesomeIcon icon={faChevronLeft} />
                </button>
                <button
                  className={`p-2 hover:bg-black/5 px-4 rounded ${learningContext?.nextQuestion ? 'text-gray-600 hover:text-indigo-600' : 'text-gray-300 cursor-not-allowed'}`}
                  onClick={() => navigate(learningContext?.nextQuestion)}
                  disabled={!learningContext?.nextQuestion}
                  title={learningContext?.nextQuestion ? "下一题" : "没有下一题"}
                >
                  <FontAwesomeIcon icon={faChevronRight} />
                </button>
              </div>
            </div>

            {/* 右侧用户信息 */}
            <div>
              <UserInfo />
            </div>
          </div>
        </header>
      )}

      {/* 使用ProblemDrawer组件 */}
      <ProblemDrawer
        isOpen={isDrawerOpen}
        onClose={() => setIsDrawerOpen(false)}
        onChange={handleChange}
        currentProblemId={currentProblemId}
        currentProblemType={2}
        envTitle={learningContext?.envTitle || '题库'}
        envId={learningContext?.envId ? Number(learningContext.envId) : undefined}
        envType={learningContext?.envType}

      />

      {/* 主要内容 */}
      <div className={`flex-1 overflow-hidden ${isMobile ? 'pt-0' : ''}`}>
        {children}
      </div>
    </div>
  );
}
