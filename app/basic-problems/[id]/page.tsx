import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import BasicProblemClient from './BasicProblemClient';
import BaseProblemService from '../../service/base-problem-service';
import type { Problem } from '../../service/base-problem-service';
import { SUBJECT_DIFFICULTY_MAP, SUBJECT_TYPE_MAP } from '@/app/utils/constant';
interface CommitResult {
  isCorrect: boolean;
  correctAnswer: string;
  explanation: string;
}

// 创建一个缓存解包函数
const getParams = cache((params: { id: string }) => {
  return { id: params.id };
});

export default function BasicProblemDetail({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { id } = getParams(params);
  const [problem, setProblem] = useState<Problem | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  const [isFlagged, setIsFlagged] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [commitResult, setCommitResult] = useState<CommitResult | null>(null);
  const [correctAnswer, setCorrectAnswer] = useState<string[] | string>([])
  const [learningContext, setLearningContext] = useState<LearningContext | null>(null)
  const [problemId, setProblemId] = useState<string>(id)

  useEffect(() => {
    fetchProblemDetail();

    queryLearningContext();
  }, [problemId]);

  const searchParams = useSearchParams();

  const queryLearningContext = () => {
    let filter = {}

    const savedFilters = sessionStorage.getItem(`problemFilters_1`);
    if (savedFilters) {
      const { keyword, tags, difficulty, status } = JSON.parse(savedFilters);
      filter = {
        keyword,
        tags,
        difficulty,
        status
      }
    }

    const data = {
      envId: searchParams.get("envId"),
      envType: searchParams.get("envType"),
      currentQuestion: Number(problemId),
      currentQuestionType: 1,
      filter
    }
    LearningContextService.query(data).then(resp => {
      setLearningContext(resp.data)
    })
  }

  const fetchProblemDetail = async () => {
    setLoading(true);
    setError('');
    setIsSubmitted(false);
    setSelectedOptions([]);
    const params: { problemListId?: string} = {}
    if (searchParams.get('envId')) {
      params.problemListId = searchParams.get('envId') || undefined
    }
    BaseProblemService.getProblemDetail(problemId, params).then(res => {
      res.data.optionsArray = JSON.parse(res.data.options);
      res.data.optionsArray.forEach((option, index) => (option.id = String.fromCharCode(65 + index)));
      if (res.data.type === 3) {
        res.data.optionsArray = [{ id: 'A', content: '正确' }, { id: 'B', content: '错误' }]
      }
      setProblem(res.data);
    }).catch(err => {
      setError(err.message);
    }).finally(() => {
      setLoading(false);
    });
  }


  const handleOptionClick = (optionId: string) => {
    if (isSubmitted) return; // 已提交答案后不能再选择

    if (problem?.type === 1) {
      setSelectedOptions([optionId]);
    } else if (problem?.type === 2) {
      if (selectedOptions.includes(optionId)) {
        // 如果已选中，则取消选中
        setSelectedOptions(selectedOptions.filter(id => id !== optionId));
      } else {
        // 如果未选中，则添加到选中列表
        setSelectedOptions([...selectedOptions, optionId]);
      }

    } else if (problem?.type === 3) {
      setSelectedOptions([optionId]);
    }
  };

  const handleSubmit = () => {
    if (!problem || selectedOptions.length === 0) return;

    if (!checkAuth(1)) {
      return;
    }

    BaseProblemService.submit({
      id: problem.id,
      answer: selectedOptions.sort().join(','),
      problemListId: searchParams.get("envId")
    }).then(res => {
      setCommitResult(res.data);
      setIsCorrect(res.data.isCorrect);
      setIsSubmitted(true);
      if (res.data.correctAnswer) {
        if (problem.type === 2) {
          setCorrectAnswer(res.data.correctAnswer.split(','))
        } else {
          setCorrectAnswer(res.data.correctAnswer)
        }
      }
    }).catch(err => {
      console.error(err);
    });
  };

  const goToNextProblem = () => {
    console.log(learningContext)
    if (!learningContext?.nextQuestion) return;
    const next = learningContext.nextQuestion
    let query = ''
    if (searchParams.get("envType") === 'problem-list' && searchParams.get('envId')) {
      query = `?envType=${searchParams.get('envType')}&envId=${searchParams.get('envId')}`
    }
    if (next.type === 1) {
      router.push(`/basic-problems/${next.id}${query}`);
    } else {
      router.push(`/coding-problems/${next.id}${query}`);
    }
  };

  const goToPrevProblem = () => {
    if (!learningContext?.previousQuestion) return;
    const previous = learningContext.previousQuestion
    let query = ''
    if (searchParams.get("envType") === 'problem-list' && searchParams.get('envId')) {
      query = `?envType=${searchParams.get('envType')}&envId=${searchParams.get('envId')}`
    }
    if (previous.type === 1) {
      router.push(`/basic-problems/${previous.id}${query}`);
    } else {
      router.push(`/coding-problems/${previous.id}${query}`);
    }
  };

  const toggleHint = () => {
    setShowHint(!showHint);
  };

  const getDifficultyColor = (difficulty: number) => {
    return SUBJECT_DIFFICULTY_MAP[difficulty].bgColor + ' ' + SUBJECT_DIFFICULTY_MAP[difficulty].color;
  };

  const getCategoryColor = () => {
    return 'bg-gray-100 text-gray-800';
  };



  // 监听 urlChange 事件
  useEffect(() => {
    const handleUrlChange = (event: CustomEvent<{ problemId: string, problemType: number }>) => {
      if (event.detail.problemType === 1) {
        setProblemId(event.detail.problemId)
      }
    };

    window.addEventListener('urlChange', handleUrlChange as EventListener);
  }, [params.id]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 主要内容 */}
      <div className="container mx-auto px-4 py-8">
        {/* 错误信息 */}
        {error && (
          <div className="bg-red-50 p-4 rounded-lg text-red-600 mb-8">
            {error}
          </div>
        )}

        {/* 加载中 */}
        {loading && (
          <div className="bg-white rounded-lg shadow-md p-16 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600 mb-2"></div>
            <p className="text-gray-600">加载中...</p>
          </div>
        )}

        {/* 题目详情 */}
        {!loading && !error && problem && (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* 左侧主要内容 */}
            <div className="lg:col-span-4">
              <div className="bg-white rounded-lg shadow-md overflow-hidden">
                {/* 题目头部信息 */}
                <div className="p-6 border-b border-gray-100">
                  <div className="flex flex-wrap items-center gap-4 mb-4">
                    <span className={`px-2 py-1 text-xs rounded-md ${getDifficultyColor(problem.difficulty)}`}>
                      {SUBJECT_DIFFICULTY_MAP[problem.difficulty].text}
                    </span>
                    <span className="px-2 py-1 text-xs rounded-md bg-blue-100 text-blue-800">
                      {SUBJECT_TYPE_MAP[problem.type]}
                    </span>
                    {
                      problem.tagViews?.length > 0 && (
                        problem.tagViews.map(tag => (
                          <span key={tag.id} className="px-2 py-1 text-xs rounded-md bg-blue-100 text-blue-800">
                            {tag.name}
                          </span>
                        ))
                      )
                    }
                  </div>

                  <div className="flex flex-wrap items-center justify-between">
                    <h1 className="text-2xl font-bold text-gray-800 mb-2">{problem.question}</h1>
                    <div className="flex items-center space-x-2">
                      <FavoriteButton
                        dataType={3}
                        dataId={problem.id}
                        isCollected={problem.isCollected}
                        className="text-xl"
                      />
                    </div>
                  </div>

                  <div className="flex items-center space-x-6 text-sm text-gray-500 mt-4">
                    <span>正确率: {(problem.interact?.commitAmt && problem.interact?.commitAmt) ? `${(problem.interact.acceptAmt / problem.interact.commitAmt * 100).toFixed(0)}%` : '-' }</span>
                    <span>已完成: {problem.interact.acceptAmt || 0}人</span>
                  </div>
                </div>

                {/* 题目描述 */}
                <div className="p-6 border-b border-gray-100">
                  <h2 className="text-lg font-semibold text-gray-800 mb-4">题目描述</h2>
                  <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: problem.description }} />
                </div>

                {/* 答题区域 */}
                <div className="p-6 border-b border-gray-100">
                  <h2 className="text-lg font-semibold text-gray-800 mb-4">请选择答案</h2>
                  <div className="space-y-3">
                    {problem.optionsArray.map((option, index) => (
                      <div
                        key={index}
                        onClick={() => handleOptionClick(option.id)}
                        className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                          isSubmitted ? (
                            // 已提交状态
                            correctAnswer.includes(option.id) ?
                              'bg-green-50 border-green-300' : // 正确答案
                              selectedOptions.includes(option.id) ?
                                'bg-red-50 border-red-300' : // 错误选择
                                'border-gray-200' // 未选中
                          ) : (
                            // 未提交状态
                            selectedOptions.includes(option.id) ? 
                              'bg-indigo-50 border-indigo-300' : // 已选中
                              'hover:bg-gray-50 border-gray-200' // 未选中
                          )
                        }`}
                      >
                        <div className="flex items-start">
                          <div className={`flex-shrink-0 w-6 h-6 rounded-full border flex items-center justify-center mr-3 ${
                            selectedOptions.includes(option.id) ? 
                              'bg-indigo-600 border-indigo-600 text-white' : 
                              'border-gray-300'
                          }`}>
                            {selectedOptions.includes(option.id) && (
                              problem.type === 3 ? (
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                </svg>
                              ) : (
                                <div className="w-4 h-4 rounded-full bg-white"></div>
                              )
                            )}
                          </div>
                          <div>
                            <span className="font-medium">{option.id}. </span>
                            <span>{option.content || option.text }</span>
                          </div>
                        </div>

                        {isSubmitted && (
                          <div className="mt-2 pl-9">
                            {typeof correctAnswer === 'string' ? (
                              // 单选题或判断题
                              correctAnswer === option.id ? (
                                <div className="text-green-600 flex items-center">
                                  <FontAwesomeIcon icon={faCheckCircle} className="mr-1" />
                                  <span>正确答案</span>
                                </div>
                              ) : selectedOptions[0] === option.id ? (
                                <div className="text-red-600 flex items-center">
                                  <FontAwesomeIcon icon={faTimes} className="mr-1" />
                                  <span>错误选择</span>
                                </div>
                              ) : null
                            ) : (
                              // 多选题
                              correctAnswer.includes(option.id) ? (
                                <div className="text-green-600 flex items-center">
                                  <FontAwesomeIcon icon={faCheckCircle} className="mr-1" />
                                  <span>正确答案的一部分</span>
                                </div>
                              ) : selectedOptions.includes(option.id) ? (
                                <div className="text-red-600 flex items-center">
                                  <FontAwesomeIcon icon={faTimes} className="mr-1" />
                                  <span>错误选择</span>
                                </div>
                              ) : null
                            )}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* 提示按钮和答案提交 */}
                  <div className="mt-8 flex flex-wrap items-center justify-between gap-4">
                    {/*<button*/}
                    {/*  onClick={toggleHint}*/}
                    {/*  className="px-4 py-2 border border-indigo-300 text-indigo-600 rounded-lg hover:bg-indigo-50 flex items-center"*/}
                    {/*>*/}
                    {/*  <FontAwesomeIcon icon={faLightbulb} className="mr-2" />*/}
                    {/*  {showHint ? '隐藏提示' : '查看提示'}*/}
                    {/*</button>*/}
                    <div></div>

                    {!isSubmitted ? (
                      <button
                        onClick={handleSubmit}
                        disabled={selectedOptions.length === 0}
                        className={`px-6 py-2 rounded-lg ${
                          selectedOptions.length === 0 ? 
                            'bg-gray-300 text-gray-500 cursor-not-allowed' : 
                            'bg-indigo-600 text-white hover:bg-indigo-700'
                        }`}
                      >
                        提交答案
                      </button>
                    ) : (
                      <div className={`px-4 py-2 rounded-lg ${isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                        {isCorrect ? '回答正确！' : '回答错误，请查看解析'}
                      </div>
                    )}
                  </div>
                </div>

                {/* 提示内容 */}
                {showHint && (
                  <div className="p-6 border-b border-gray-100 bg-yellow-50">
                    <h3 className="text-lg font-semibold text-yellow-800 mb-2">题目提示</h3>
                    <p className="text-yellow-700">
                      思考不同编程语言中用于存储整数值的数据类型。整数通常是没有小数部分的数字。
                    </p>
                  </div>
                )}

                {/* 答案解析 */}
                {isSubmitted && (
                  <div className="p-6 border-b border-gray-100">
                    <h2 className="text-lg font-semibold text-gray-800 mb-4">答案解析</h2>
                    <div className="prose prose-indigo max-w-none" dangerouslySetInnerHTML={{ __html: commitResult?.explanation || '' }} />
                  </div>
                )}

                {/* 题目导航 */}
                <div className="p-6 flex items-center justify-between">
                  <button
                    onClick={goToPrevProblem}
                    className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center"
                    disabled={!learningContext?.previousQuestion}
                    title={learningContext?.previousQuestion?.title || '没有上一题啦！'}
                  >
                    <FontAwesomeIcon icon={faChevronLeft} className="mr-2" />
                    上一题
                  </button>

                  <button
                    onClick={goToNextProblem}
                    className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 flex items-center"
                    disabled={!learningContext?.nextQuestion}
                    title={learningContext?.nextQuestion?.title || '没有下一题啦！'}
                  >
                    下一题
                    <FontAwesomeIcon icon={faChevronRight} className="ml-2" />
                  </button>
                </div>
              </div>
            </div>

            {/* 右侧相关题目 */}
            {/*<div className="lg:col-span-1">*/}
            {/*  <div className="bg-white rounded-lg shadow-md overflow-hidden">*/}
            {/*    <div className="p-4 bg-indigo-50 border-b border-indigo-100">*/}
            {/*      <h3 className="text-lg font-semibold text-gray-800">相关题目</h3>*/}
            {/*    </div>*/}

            {/*    <div className="divide-y divide-gray-100">*/}
            {/*      {relatedProblems?.map(relatedProblem => (*/}
            {/*        <Link*/}
            {/*          key={relatedProblem.id}*/}
            {/*          href={`/basic-problems/${relatedProblem.id}`}*/}
            {/*          className="block p-4 hover:bg-gray-50"*/}
            {/*        >*/}
            {/*          <div className="font-medium text-indigo-600 hover:text-indigo-800">*/}
            {/*            {relatedProblem.question}*/}
            {/*          </div>*/}
            {/*        </Link>*/}
            {/*      ))}*/}
            {/*    </div>*/}
            {/*  </div>*/}
            {/*</div>*/}
          </div>
        )}
      </div>
    </div>
  );
}
