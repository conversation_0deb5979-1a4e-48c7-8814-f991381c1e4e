"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import BaseProblemService from '../../service/base-problem-service';
import type { Problem } from '../../service/base-problem-service';
import { SUBJECT_DIFFICULTY_MAP } from '@/app/utils/constant';

interface RelatedProblemsProps {
  currentProblem: Problem;
}

interface RelatedProblem {
  id: number;
  question: string;
  difficulty: number;
  categoryName: string;
  interact: {
    acceptAmt: number;
    commitAmt: number;
  };
}

export default function RelatedProblems({ currentProblem }: RelatedProblemsProps) {
  const [relatedProblems, setRelatedProblems] = useState<RelatedProblem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchRelatedProblems();
  }, [currentProblem.id]);

  const fetchRelatedProblems = async () => {
    try {
      setLoading(true);
      
      // 构建查询参数，基于当前题目的标签和分类
      const tags = currentProblem.tagViews?.map(tag => tag.id) || [];
      const queryData = {
        pageNum: 1,
        pageSize: 6,
        difficulty: currentProblem.difficulty, // 相同难度
        tags: tags.length > 0 ? tags : undefined,
        excludeId: currentProblem.id // 排除当前题目
      };

      const response = await BaseProblemService.getProblemList(queryData);
      
      if (response.data && response.data.items) {
        // 过滤掉当前题目，取前5个
        const filtered = response.data.items
          .filter(item => item.id !== currentProblem.id)
          .slice(0, 5);
        setRelatedProblems(filtered);
      }
    } catch (error) {
      console.error('获取相关题目失败:', error);
      // 如果获取失败，尝试获取同分类的题目
      try {
        const fallbackData = {
          pageNum: 1,
          pageSize: 6,
          categoryName: currentProblem.categoryName,
          excludeId: currentProblem.id
        };
        const fallbackResponse = await BaseProblemService.getProblemList(fallbackData);
        if (fallbackResponse.data && fallbackResponse.data.items) {
          const filtered = fallbackResponse.data.items
            .filter(item => item.id !== currentProblem.id)
            .slice(0, 5);
          setRelatedProblems(filtered);
        }
      } catch (fallbackError) {
        console.error('获取备用相关题目也失败:', fallbackError);
      }
    } finally {
      setLoading(false);
    }
  };

  const getDifficultyColor = (difficulty: number) => {
    return SUBJECT_DIFFICULTY_MAP[difficulty]?.bgColor + ' ' + SUBJECT_DIFFICULTY_MAP[difficulty]?.color || 'bg-gray-100 text-gray-800';
  };

  if (loading) {
    return (
      <aside className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-4 bg-indigo-50 border-b border-indigo-100">
          <h3 className="text-lg font-semibold text-gray-800">相关题目</h3>
        </div>
        <div className="p-4">
          <div className="animate-pulse space-y-3">
            {[1, 2, 3, 4, 5].map(i => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </aside>
    );
  }

  if (relatedProblems.length === 0) {
    return null;
  }

  return (
    <aside className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="p-4 bg-indigo-50 border-b border-indigo-100">
        <h3 className="text-lg font-semibold text-gray-800">相关题目</h3>
        <p className="text-sm text-gray-600 mt-1">基于当前题目推荐的相似题目</p>
      </div>

      <div className="divide-y divide-gray-100">
        {relatedProblems.map(relatedProblem => (
          <Link
            key={relatedProblem.id}
            href={`/basic-problems/${relatedProblem.id}`}
            className="block p-4 hover:bg-gray-50 transition-colors"
            title={`查看题目：${relatedProblem.question}`}
          >
            <div className="space-y-2">
              <div className="font-medium text-indigo-600 hover:text-indigo-800 line-clamp-2">
                {relatedProblem.question}
              </div>
              
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 rounded-md ${getDifficultyColor(relatedProblem.difficulty)}`}>
                    {SUBJECT_DIFFICULTY_MAP[relatedProblem.difficulty]?.text || '未知'}
                  </span>
                  {relatedProblem.categoryName && (
                    <span className="px-2 py-1 rounded-md bg-gray-100 text-gray-700">
                      {relatedProblem.categoryName}
                    </span>
                  )}
                </div>
                
                <div className="text-gray-500">
                  {relatedProblem.interact?.commitAmt ? 
                    `${Math.round((relatedProblem.interact.acceptAmt / relatedProblem.interact.commitAmt) * 100)}%` : 
                    '0%'
                  }
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>

      <div className="p-4 bg-gray-50 border-t border-gray-100">
        <Link
          href="/basic-problems"
          className="text-sm text-indigo-600 hover:text-indigo-800 font-medium"
        >
          查看更多题目 →
        </Link>
      </div>
    </aside>
  );
}
