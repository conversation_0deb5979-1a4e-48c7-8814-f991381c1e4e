'use client';

import React, { useState, useEffect } from 'react';
import { useAppSelector, useAppDispatch } from '@/app/redux/hooks';
import {
  selectPageNotifications,
  selectUnreadCount,
  selectIsLoading,
  selectError,
  selectTotalItems,
  selectCurrentPage,
  selectPageSize,
  fetchNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  clearNotifications,
  setCurrentPage as setReduxCurrentPage,
  setPageSize as setReduxPageSize
} from '@/app/redux/features/notificationSlice';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCheck,
  faCheckDouble,
  faTrashAlt,
  faBell,
  faTrophy,
  faCode,
  faCommentAlt,
  faStar,
  faInfoCircle,
  faSpinner
} from '@fortawesome/free-solid-svg-icons';
import Link from 'next/link';
import Pagination from '@/app/components/Pagination';

type FilterType = 'all' | 'unread' | 'system' | 'contest' | 'problem' | 'interaction' | 'vip';

// API类型到过滤器类型的映射
const filterTypeToApiType: Record<FilterType, number | undefined> = {
  all: undefined, // 不过滤
  unread: undefined, // 在isRead参数中处理
  system: 1,
  vip: 2,
  contest: 3,
  problem: undefined, // API没有对应的类型
  interaction: undefined // API没有对应的类型
};

export default function NotificationsPage() {
  const dispatch = useAppDispatch();
  const notifications = useAppSelector(selectPageNotifications);
  const unreadCount = useAppSelector(selectUnreadCount);
  const isLoading = useAppSelector(selectIsLoading);
  const error = useAppSelector(selectError);
  const totalItems = useAppSelector(selectTotalItems);
  const reduxCurrentPage = useAppSelector(selectCurrentPage);
  const reduxPageSize = useAppSelector(selectPageSize);

  const [activeFilter, setActiveFilter] = useState<FilterType>('all');
  const [isReadFilter, setIsReadFilter] = useState<number | undefined>(undefined);

  // 当过滤器变更或页面变更时获取数据
  useEffect(() => {
    const type = filterTypeToApiType[activeFilter];
    // 如果过滤器是'unread'，设置isRead=0，否则使用state中的值
    const isRead = activeFilter === 'unread' ? 0 : isReadFilter;

    dispatch(fetchNotifications({
      pageNum: reduxCurrentPage,
      pageSize: 10,
      type,
      isRead
    }));
  }, [dispatch, activeFilter, isReadFilter, reduxCurrentPage]);

  // 处理标记已读
  const handleMarkAsRead = (id: number) => {
    dispatch(markNotificationAsRead(id));
  };

  // 处理全部标记已读
  const handleMarkAllAsRead = () => {
    dispatch(markAllNotificationsAsRead())
      .unwrap()
      .then(() => {
        // 刷新数据
        refreshData();
      });
  };

  // 处理清空通知
  const handleClearAll = () => {
    if (window.confirm('确定要清空所有通知吗？此操作不可撤销。')) {
      dispatch(clearNotifications());
    }
  };

  // 刷新数据
  const refreshData = () => {
    const type = filterTypeToApiType[activeFilter];
    const isRead = activeFilter === 'unread' ? 0 : isReadFilter;

    dispatch(fetchNotifications({
      pageNum: reduxCurrentPage,
      pageSize: 10,
      type,
      isRead
    }));
  };

  // 切换过滤器
  const handleFilterChange = (filter: FilterType) => {
    setActiveFilter(filter);
    dispatch(setReduxCurrentPage(1)); // 重置页码
  };

  // 处理页面变化
  const handlePageChange = (page: number) => {
    dispatch(setReduxCurrentPage(page));
    // 滚动到页面顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // 获取通知类型对应的样式
  const getNotificationTypeStyle = (type: string | number) => {
    if (typeof type === 'number') {
      // 处理数字类型的通知类型
      switch (type) {
        case 1:
          return 'bg-indigo-100 text-indigo-600';
        case 2:
          return 'bg-yellow-100 text-yellow-600';
        case 3:
          return 'bg-green-100 text-green-600';
        default:
          return 'bg-gray-100 text-gray-600';
      }
    } else {
      // 处理字符串类型的通知类型
      switch (type) {
        case 'system':
          return 'bg-indigo-100 text-indigo-600';
        case 'vip':
          return 'bg-yellow-100 text-yellow-600';
        case 'contest':
          return 'bg-green-100 text-green-600';
        case 'problem':
          return 'bg-blue-100 text-blue-600';
        case 'interaction':
          return 'bg-purple-100 text-purple-600';
        default:
          return 'bg-gray-100 text-gray-600';
      }
    }
  };

  // 获取通知类型对应的图标
  const getNotificationTypeIcon = (type: string) => {
    switch (type) {
      case 'system':
        return <FontAwesomeIcon icon={faBell} />;
      case 'contest':
        return <FontAwesomeIcon icon={faTrophy} />;
      case 'problem':
        return <FontAwesomeIcon icon={faCode} />;
      case 'interaction':
        return <FontAwesomeIcon icon={faCommentAlt} />;
      case 'vip':
        return <FontAwesomeIcon icon={faStar} />;
      default:
        return <FontAwesomeIcon icon={faInfoCircle} />;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 面包屑导航 */}
      <div className="mb-6 flex items-center text-gray-600 text-sm">
        <Link href="/" className="hover:text-indigo-600">首页</Link>
        <FontAwesomeIcon icon={faInfoCircle} className="text-xs mx-2" />
        <span className="text-gray-800">消息通知</span>
      </div>

      {/* 错误信息 */}
      {error && (
        <div className="mb-6 p-4 bg-red-100 text-red-700 rounded-lg">
          <p>{error}</p>
          <button
            onClick={refreshData}
            className="mt-2 px-3 py-1 bg-red-700 text-white rounded-md hover:bg-red-800"
          >
            重试
          </button>
        </div>
      )}

      {/* 消息通知标题 */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">消息通知</h1>
        <div className="flex space-x-2">
          {unreadCount > 0 && (
            <button
              onClick={handleMarkAllAsRead}
              className="px-3 py-1.5 text-sm text-indigo-600 border border-indigo-600 rounded-lg hover:bg-indigo-50 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isLoading}
            >
              {isLoading ? (
                <FontAwesomeIcon icon={faSpinner} className="mr-1 animate-spin" />
              ) : (
                <FontAwesomeIcon icon={faCheckDouble} className="mr-1" />
              )}
              全部已读
            </button>
          )}
          {notifications.length > 0 && (
            <button
              onClick={handleClearAll}
              className="px-3 py-1.5 text-sm text-red-600 border border-red-600 rounded-lg hover:bg-red-50"
            >
              <FontAwesomeIcon icon={faTrashAlt} className="mr-1" /> 清空通知
            </button>
          )}
        </div>
      </div>

      {/* 消息筛选标签 */}
      <div className="flex flex-wrap gap-2 mb-6">
        <button
          onClick={() => handleFilterChange('all')}
          className={`px-3 py-1 text-sm rounded-full ${
            activeFilter === 'all'
              ? 'bg-indigo-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          全部消息
        </button>
        <button
          onClick={() => handleFilterChange('unread')}
          className={`px-3 py-1 text-sm rounded-full flex items-center ${
            activeFilter === 'unread'
              ? 'bg-indigo-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          未读消息
          {unreadCount > 0 && (
            <span className="bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 ml-1">
              {unreadCount}
            </span>
          )}
        </button>
        <button
          onClick={() => handleFilterChange('system')}
          className={`px-3 py-1 text-sm rounded-full ${
            activeFilter === 'system'
              ? 'bg-indigo-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          系统通知
        </button>
        <button
          onClick={() => handleFilterChange('contest')}
          className={`px-3 py-1 text-sm rounded-full ${
            activeFilter === 'contest'
              ? 'bg-indigo-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          竞赛消息
        </button>
        <button
          onClick={() => handleFilterChange('vip')}
          className={`px-3 py-1 text-sm rounded-full ${
            activeFilter === 'vip'
              ? 'bg-indigo-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          会员消息
        </button>
      </div>

      {/* 消息列表 */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
        {isLoading ? (
          <div className="p-8 text-center text-gray-500">
            <FontAwesomeIcon icon={faSpinner} spin className="text-indigo-600 text-2xl mb-2" />
            <p>加载中...</p>
          </div>
        ) : notifications.length > 0 ? (
          notifications.map((notification) => (
            <div
              key={notification.id}
              className={`p-5 border-b border-gray-100 ${
                !notification.isRead ? 'bg-indigo-50 border-l-4 border-indigo-600' : ''
              } flex`}
            >
              <div className="flex-shrink-0 mr-4">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${getNotificationTypeStyle(notification.type)}`}>
                  {getNotificationTypeIcon(notification.type)}
                </div>
              </div>
              <div className="flex-grow">
                <div className="flex justify-between items-start mb-1">
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                    {notification.title}
                    {!notification.isRead && (
                      <span className="ml-2 px-2 py-0.5 bg-red-100 text-red-800 text-xs rounded-full">未读</span>
                    )}
                  </h3>
                  <span className="text-sm text-gray-500">{notification.date}</span>
                </div>
                <p className="text-gray-700">{notification.content}</p>
                <div className="mt-2 flex justify-between items-center">
                  {notification.link ? (
                    <a
                      href={notification.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-indigo-600 hover:text-indigo-800 text-sm"
                    >
                      查看详情
                    </a>
                  ) : (
                    <Link
                      href={`/notifications/${notification.id}`}
                      className="text-indigo-600 hover:text-indigo-800 text-sm"
                    >
                      查看详情
                    </Link>
                  )}
                  {!notification.isRead && (
                    <button
                      onClick={() => handleMarkAsRead(notification.id)}
                      className="text-gray-500 hover:text-indigo-600 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <FontAwesomeIcon icon={faSpinner} className="mr-1 animate-spin" />
                      ) : (
                        <FontAwesomeIcon icon={faCheck} className="mr-1" />
                      )}
                      标记已读
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="p-8 text-center text-gray-500">
            暂无消息通知
          </div>
        )}
      </div>

      {/* 分页 - 使用公共Pagination组件 */}
      {totalItems > 0 && (
        <Pagination
          currentPage={reduxCurrentPage}
          totalItems={totalItems}
          pageSize={10}
          onPageChange={handlePageChange}
          maxPageButtons={5}
        />
      )}
    </div>
  );
}
