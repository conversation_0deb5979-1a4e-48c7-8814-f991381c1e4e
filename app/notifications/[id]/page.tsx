'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAppSelector, useAppDispatch } from '@/app/redux/hooks';
import { 
  selectPageNotifications,
  selectHeaderNotifications,
  fetchNotificationDetail, 
  markNotificationAsRead 
} from '@/app/redux/features/notificationSlice';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faArrowLeft, 
  faBell, 
  faTrophy, 
  faCode, 
  faCommentAlt, 
  faStar, 
  faInfoCircle,
  faCalendarAlt,
  faSpinner
} from '@fortawesome/free-solid-svg-icons';
import Link from 'next/link';

export default function NotificationDetailPage() {
  const params = useParams();
  const router = useRouter();
  const dispatch = useAppDispatch();
  const pageNotifications = useAppSelector(selectPageNotifications);
  const headerNotifications = useAppSelector(selectHeaderNotifications);
  const notificationId = Number(params.id);
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [notification, setNotification] = useState<any>(null);
  
  // 获取通知详情
  useEffect(() => {
    const fetchNotification = async () => {
      setLoading(true);
      setError(null);
      
      try {
        // 先从Redux中查找，优先使用页面通知列表，因为可能包含更多详细信息
        let existingNotification = pageNotifications.find(n => n.id === notificationId);
        
        // 如果页面通知列表中没有，再从header通知列表中查找
        if (!existingNotification) {
          existingNotification = headerNotifications.find(n => n.id === notificationId);
        }
        
        if (existingNotification) {
          setNotification(existingNotification);
          
          // 如果通知未读，标记为已读
          if (!existingNotification.isRead) {
            await dispatch(markNotificationAsRead(notificationId)).unwrap();
          }
        } else {
          // 如果Redux中没有，从API获取
          const result = await dispatch(fetchNotificationDetail(notificationId)).unwrap();
          setNotification(result);
          
          // 标记为已读
          if (!result.isRead) {
            await dispatch(markNotificationAsRead(notificationId)).unwrap();
          }
        }
      } catch (err: any) {
        setError(err.message || '获取通知详情失败');
      } finally {
        setLoading(false);
      }
    };
    
    fetchNotification();
  }, [notificationId, dispatch, pageNotifications, headerNotifications]);
  
  // 加载中状态
  if (loading && !notification) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <FontAwesomeIcon icon={faSpinner} spin className="text-indigo-600 text-4xl mb-4" />
        <h2 className="text-xl text-gray-600">加载中...</h2>
      </div>
    );
  }
  
  // 错误状态
  if (error) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <div className="bg-red-100 p-6 rounded-lg max-w-lg mx-auto">
          <h1 className="text-2xl font-bold text-red-800 mb-4">加载失败</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <div className="flex justify-center gap-4">
            <button 
              onClick={() => router.back()} 
              className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
            >
              返回上一页
            </button>
            <Link 
              href="/notifications" 
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
            >
              返回通知列表
            </Link>
          </div>
        </div>
      </div>
    );
  }
  
  // 通知不存在状态
  if (!notification) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">通知不存在</h1>
        <p className="text-gray-600 mb-8">您查找的通知不存在或已被删除。</p>
        <Link 
          href="/notifications" 
          className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
        >
          返回通知列表
        </Link>
      </div>
    );
  }
  
  // 获取通知类型对应的样式
  const getNotificationTypeStyle = (type: string) => {
    switch (type) {
      case 'system':
        return 'bg-indigo-100 text-indigo-600';
      case 'contest':
        return 'bg-green-100 text-green-600';
      case 'problem':
        return 'bg-blue-100 text-blue-600';
      case 'interaction':
        return 'bg-purple-100 text-purple-600';
      case 'vip':
        return 'bg-yellow-100 text-yellow-600';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  };
  
  // 获取通知类型对应的图标
  const getNotificationTypeIcon = (type: string) => {
    switch (type) {
      case 'system':
        return <FontAwesomeIcon icon={faBell} />;
      case 'contest':
        return <FontAwesomeIcon icon={faTrophy} />;
      case 'problem':
        return <FontAwesomeIcon icon={faCode} />;
      case 'interaction':
        return <FontAwesomeIcon icon={faCommentAlt} />;
      case 'vip':
        return <FontAwesomeIcon icon={faStar} />;
      default:
        return <FontAwesomeIcon icon={faInfoCircle} />;
    }
  };
  
  // 获取通知类型对应的文本
  const getNotificationTypeText = (type: string) => {
    switch (type) {
      case 'system':
        return '系统通知';
      case 'contest':
        return '竞赛消息';
      case 'problem':
        return '题目更新';
      case 'interaction':
        return '互动消息';
      case 'vip':
        return '会员消息';
      default:
        return '其他通知';
    }
  };

  // 如果有link字段并且不是'/'，则跳转到外部链接
  if (notification.link && notification.link !== '/') {
    // 创建一个effect以便在渲染后执行重定向
    useEffect(() => {
      // 对于客户端导航，可以使用window.location
      window.location.href = notification.link;
    }, [notification.link]);
    
    // 显示跳转中的加载状态
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <FontAwesomeIcon icon={faSpinner} spin className="text-indigo-600 text-4xl mb-4" />
        <h2 className="text-xl text-gray-600">正在跳转到内容页面...</h2>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 面包屑导航 */}
      <div className="mb-6 flex items-center text-gray-600 text-sm">
        <Link href="/" className="hover:text-indigo-600">首页</Link>
        <FontAwesomeIcon icon={faInfoCircle} className="text-xs mx-2" />
        <Link href="/notifications" className="hover:text-indigo-600">消息通知</Link>
        <FontAwesomeIcon icon={faInfoCircle} className="text-xs mx-2" />
        <span className="text-gray-800">{notification.title}</span>
      </div>
      
      {/* 返回按钮 */}
      <div className="mb-6">
        <button 
          onClick={() => router.back()} 
          className="flex items-center text-indigo-600 hover:text-indigo-800"
        >
          <FontAwesomeIcon icon={faArrowLeft} className="mr-1" />
          返回通知列表
        </button>
      </div>
      
      {/* 通知详情卡片 */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div className="p-6">
          <div className="flex items-start gap-4">
            <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${getNotificationTypeStyle(notification.type)}`}>
              {getNotificationTypeIcon(notification.type)}
            </div>
            
            <div className="flex-grow">
              <h1 className="text-2xl font-bold text-gray-800 mb-2">{notification.title}</h1>
              
              <div className="flex flex-wrap items-center text-sm text-gray-500 mb-4 gap-4">
                <span className="flex items-center">
                  <FontAwesomeIcon icon={faInfoCircle} className="mr-1" />
                  {getNotificationTypeText(notification.type)}
                </span>
                <span className="flex items-center">
                  <FontAwesomeIcon icon={faCalendarAlt} className="mr-1" />
                  {notification.date}
                </span>
              </div>
              
              <div className="border-t border-gray-100 pt-4 text-gray-700 leading-relaxed">
                <p className="whitespace-pre-line">{notification.content}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* 相关操作 */}
      <div className="flex justify-end mb-8">
        <button 
          onClick={() => router.push('/notifications')} 
          className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
        >
          返回通知列表
        </button>
      </div>
      
      {/* 相关通知 */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-lg font-bold text-gray-800 mb-4">相关通知</h2>
        
        <div className="space-y-4">
          {pageNotifications
            .filter(n => n.id !== notificationId && n.type === notification.type)
            .slice(0, 3)
            .map(relatedNotification => (
              <Link 
                key={relatedNotification.id} 
                href={relatedNotification.link || `/notifications/${relatedNotification.id}`}
                className="block p-4 border border-gray-100 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center">
                  <div className={`flex-shrink-0 w-8 h-8 rounded-full mr-3 flex items-center justify-center ${getNotificationTypeStyle(relatedNotification.type)}`}>
                    {getNotificationTypeIcon(relatedNotification.type)}
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-800">{relatedNotification.title}</h3>
                    <p className="text-sm text-gray-500 mt-1">{relatedNotification.date}</p>
                  </div>
                </div>
              </Link>
            ))}
          
          {pageNotifications.filter(n => n.id !== notificationId && n.type === notification.type).length === 0 && (
            <p className="text-gray-500 text-center py-4">暂无相关通知</p>
          )}
        </div>
      </div>
    </div>
  );
} 