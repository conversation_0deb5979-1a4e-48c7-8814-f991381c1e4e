'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faArrowLeft, faInfoCircle, faCheckCircle, faSync
} from '@fortawesome/free-solid-svg-icons';
import { faAlipay, faWeixin } from '@fortawesome/free-brands-svg-icons';
import { checkAuth } from "@/app/utils/authCheck";
import ProfileService from "@/app/service/profile-service";
import {useAppSelector} from "@/app/redux/hooks";
import {selectIsAuthenticated} from "@/app/redux/features/authSlice";
import './purchase.css'; // 引入CSS文件

export default function Purchase() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedDuration, setSelectedDuration] = useState<number>(12); // 默认选择365天
  const [autoRenew, setAutoRenew] = useState<boolean>(false);
  const [paymentMethod, setPaymentMethod] = useState<'alipay' | 'wechat'>('alipay');
  const [currentMembershipLevel, setCurrentMembershipLevel] = useState<number | null>(null);
  const [isMounted, setIsMounted] = useState(false);

  // 从URL参数获取会员方案信息
  const planId = searchParams?.get('planId') || '';
  const plan = searchParams?.get('plan') || 'basic';
  const upgrade = searchParams?.get('upgrade') === '1' ? 1 : 0;

  const isAuthenticated = useAppSelector(selectIsAuthenticated);

  useEffect(() => {
    setIsMounted(true);
    if (isAuthenticated) {
      ProfileService.getProfile().then(resp => {
        const user = resp.data;
        setCurrentMembershipLevel(user.currentMembershipLevel);
      });
    }
  }, [isAuthenticated]);

  // 会员方案基本信息 - 更新基础版和Pro版价格
  const planInfo = {
    basic: {
      name: '基础版会员',
      description: '适合初学者入门',
      monthlyPrice: 9.9,
      yearlyPrice: 99,
    },
    pro: {
      name: 'Pro版会员',
      description: '适合进阶学习者',
      monthlyPrice: 19.8,
      yearlyPrice: 198,
    },
    ultra: {
      name: 'Ultra版会员',
      description: '适合竞赛',
      monthlyPrice: 39.8,
      yearlyPrice: 398,
    }
  };

  // 获取当前选择的方案信息
  const currentPlan = plan === 'basic' ? planInfo.basic : plan === 'ultra' ? planInfo.ultra : planInfo.pro;
  
  // 计算折扣和价格
  const calculatePrice = (months: number) => {
    const basePrice = currentPlan.monthlyPrice;
    let discountedPrice;
    let discount = 0;
    
    if (months === 1) {
      // 月付价格，如果选择自动续费则有5%折扣
      discount = autoRenew ? 0.05 : 0; 
      discountedPrice = basePrice * (1 - discount);
    } else if (months === 3) {
      // 3个月价格 - 无折扣
      discountedPrice = basePrice * months;
    } else if (months === 6) {
      // 6个月价格 - 10%折扣
      discount = 0.1;
      discountedPrice = basePrice * months * (1 - discount);
    } else if (months === 12) {
      // 年付价格 - 使用指定的年付价格
      discountedPrice = currentPlan.yearlyPrice;
      // 计算相对于月付的折扣
      discount = 1 - (discountedPrice / (basePrice * months));
    } else {
      discountedPrice = basePrice * months;
    }
    
    const dailyPrice = discountedPrice / (months * 30);
    
    return {
      totalPrice: discountedPrice.toFixed(2),
      dailyPrice: dailyPrice.toFixed(2),
      discount: Math.round(discount * 100)
    };
  };

  // 获取各种期限的价格
  const getPriceForDuration = (months: number) => {
    return calculatePrice(months);
  };

  // 月付价格
  const monthlyPrice = currentPlan.monthlyPrice;
  const monthlyPriceWithDiscount = autoRenew ? (monthlyPrice * 0.95).toFixed(2) : monthlyPrice.toFixed(2);
  const monthlyDailyPrice = (monthlyPrice / 30).toFixed(2);
  
  // 3个月价格（无优惠）
  const threeMonthPrice = getPriceForDuration(3);
  
  // 半年付价格（6个月）
  const sixMonthPrice = getPriceForDuration(6);
  
  // 年付价格（12个月）
  const yearlyPrice = getPriceForDuration(12);

  // 当前选择的总价
  const currentPrice = getPriceForDuration(selectedDuration);

  // 处理选择支付方式
  const handlePaymentMethodChange = (method: 'alipay' | 'wechat') => {
    // 连续包月只能使用支付宝
    if (autoRenew && method === 'wechat') {
      return;
    }
    setPaymentMethod(method);
  };

  // 处理自动续费切换
  const handleAutoRenewChange = (checked: boolean) => {
    setAutoRenew(checked);
    
    // 如果开启自动续费，只能选择支付宝
    if (checked && paymentMethod === 'wechat') {
      setPaymentMethod('alipay');
    }
    
    // 自动续费只能与月付结合
    if (checked && selectedDuration !== 1) {
      setSelectedDuration(1);
    }
  };

  // 处理继续到支付页面
  const handleContinueToPayment = () => {
    if (!checkAuth()) {
      return;
    }

    const params = new URLSearchParams();
    params.append('planId', planId);
    params.append('plan', plan);
    
    // 根据选择的时长决定计费周期
    let billingType = 'monthly';
    if (selectedDuration === 3) billingType = 'quarterly';
    else if (selectedDuration === 6) billingType = 'semiannually';
    else if (selectedDuration === 12) billingType = 'yearly';
    
    params.append('billingType', billingType);
    
    // 计算价格
    const price = parseFloat(getPriceForDuration(selectedDuration).totalPrice);
    params.append('price', price.toString());
    
    // 是否自动续费
    params.append('autoRenew', autoRenew ? '1' : '0');
    
    // 是否是升级
    if (upgrade) {
      params.append('upgrade', upgrade.toString());
    }

    // 添加支付平台参数
    params.append('platform', paymentMethod === 'alipay' ? '10' : '23');
    
    // 添加购买月份参数
    params.append('months', selectedDuration.toString());
    
    // 添加连续包月订阅参数（仅当选择了月付且开启了自动续费时才添加）
    if (selectedDuration === 1 && autoRenew) {
      params.append('isContinuousMonthlySubscription', '1');
    } else {
      params.append('isContinuousMonthlySubscription', '0');
    }

    // 跳转到支付页面
    router.push(`/payment?${params.toString()}`);
  };

  // 页面未挂载时显示加载状态
  if (!isMounted) {
    return (
      <div className="bg-gray-50 min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* 主要内容 */}
      <div className="container mx-auto px-4 py-12">
        {/* 页面标题 */}
        <div className="text-center mb-10">
          <h1 className="text-3xl font-bold text-gray-800 mb-4">确认您的订单</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">请选择会员时长和支付方式</p>
        </div>

        {/* 订单确认内容 */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden mb-8">
            <div className="p-6 bg-indigo-50 border-b border-indigo-100">
              <h2 className="text-2xl font-bold text-gray-800">您选择的套餐</h2>
            </div>
            
            <div className="p-8">
              {/* 套餐信息 */}
              <div className="mb-8">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="w-16 h-16 rounded-full bg-indigo-100 flex items-center justify-center">
                    <FontAwesomeIcon 
                      icon={plan === 'basic' ? faCheckCircle : faInfoCircle} 
                      className={plan === 'basic' ? 'text-blue-600' : plan === 'ultra' ? 'text-purple-600' : 'text-indigo-600'} 
                      size="2x"
                    />
                  </div>
                  <div>
                    <h3 className={`text-2xl font-bold ${plan === 'basic' ? 'text-blue-600' : plan === 'ultra' ? 'text-purple-600' : 'text-indigo-600'} mb-1`}>
                      {currentPlan.name}
                    </h3>
                    <p className="text-gray-600">{currentPlan.description}</p>
                    
                    {upgrade === 1 && (
                      <p className="text-indigo-600 text-sm mt-1">
                        <FontAwesomeIcon icon={faSync} className="mr-1" />
                        您将升级到更高级别的会员
                      </p>
                    )}
                  </div>
                </div>
              </div>
              
              {/* 选择购买时长 */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">请选择购买时长</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {/* 30天选项 */}
                  <div 
                    className={`border ${selectedDuration === 1 ? 'border-indigo-300 bg-indigo-50' : 'border-gray-200'} rounded-lg p-4 cursor-pointer relative ${autoRenew && selectedDuration !== 1 ? 'opacity-50' : ''}`}
                    onClick={() => setSelectedDuration(1)}
                  >
                    {/* {selectedDuration === 1 && (
                      <div className="absolute top-2 right-2 flex items-center">
                        <span className="text-xs text-gray-600 mr-2">连续包月</span>
                        <label className="switch">
                          <input 
                            type="checkbox" 
                            checked={autoRenew}
                            onChange={(e) => handleAutoRenewChange(e.target.checked)}
                          />
                          <span className="slider round"></span>
                        </label>
                      </div>
                    )} */}
                    {autoRenew && selectedDuration === 1 && (
                      <div className="absolute top-0 left-0 bg-red-500 text-white text-xs px-2 py-1 rounded-br-lg rounded-tl-lg">
                        省5%
                      </div>
                    )}
                    <div className="text-center mt-4">
                      <span className={`block text-xl font-bold ${selectedDuration === 1 ? 'text-indigo-800' : 'text-gray-800'} mb-1`}>30天</span>
                      <span className={selectedDuration === 1 ? 'text-indigo-700' : 'text-gray-600'}>¥{monthlyPriceWithDiscount}</span>
                      <span className={`block text-xs ${selectedDuration === 1 ? 'text-indigo-600' : 'text-gray-500'} mt-1`}>仅{monthlyDailyPrice}元/日</span>
                    </div>
                  </div>
                  
                  {/* 90天选项 */}
                  <div 
                    className={`border ${selectedDuration === 3 ? 'border-indigo-300 bg-indigo-50' : 'border-gray-200'} rounded-lg p-4 cursor-pointer relative ${autoRenew ? 'opacity-50 pointer-events-none' : ''}`}
                    onClick={() => !autoRenew && setSelectedDuration(3)}
                  >
                    <div className="text-center mt-4">
                      <span className={`block text-xl font-bold ${selectedDuration === 3 ? 'text-indigo-800' : 'text-gray-800'} mb-1`}>90天</span>
                      <span className={selectedDuration === 3 ? 'text-indigo-700' : 'text-gray-600'}>¥{threeMonthPrice.totalPrice}</span>
                      <span className={`block text-xs ${selectedDuration === 3 ? 'text-indigo-600' : 'text-gray-500'} mt-1`}>仅{threeMonthPrice.dailyPrice}元/日</span>
                    </div>
                  </div>
                  
                  {/* 180天选项 */}
                  <div 
                    className={`border ${selectedDuration === 6 ? 'border-indigo-300 bg-indigo-50' : 'border-gray-200'} rounded-lg p-4 cursor-pointer relative ${autoRenew ? 'opacity-50 pointer-events-none' : ''}`}
                    onClick={() => !autoRenew && setSelectedDuration(6)}
                  >
                    <div className="absolute top-0 left-0 bg-red-500 text-white text-xs px-2 py-1 rounded-br-lg rounded-tl-lg">
                      省{sixMonthPrice.discount}%
                    </div>
                    <div className="text-center mt-4">
                      <span className={`block text-xl font-bold ${selectedDuration === 6 ? 'text-indigo-800' : 'text-gray-800'} mb-1`}>180天</span>
                      <span className={selectedDuration === 6 ? 'text-indigo-700' : 'text-gray-600'}>¥{sixMonthPrice.totalPrice}</span>
                      <span className={`block text-xs ${selectedDuration === 6 ? 'text-indigo-600' : 'text-gray-500'} mt-1`}>仅{sixMonthPrice.dailyPrice}元/日</span>
                    </div>
                  </div>
                  
                  {/* 365天选项 */}
                  <div 
                    className={`border ${selectedDuration === 12 ? 'border-indigo-300 bg-indigo-50' : 'border-gray-200'} rounded-lg p-4 cursor-pointer relative ${autoRenew ? 'opacity-50 pointer-events-none' : ''}`}
                    onClick={() => !autoRenew && setSelectedDuration(12)}
                  >
                    <div className="absolute top-0 left-0 bg-red-500 text-white text-xs px-2 py-1 rounded-br-lg rounded-tl-lg">
                      省{yearlyPrice.discount}%
                    </div>
                    <div className="text-center mt-4">
                      <span className={`block text-xl font-bold ${selectedDuration === 12 ? 'text-indigo-800' : 'text-gray-800'} mb-1`}>365天</span>
                      <span className={selectedDuration === 12 ? 'text-indigo-700' : 'text-gray-600'}>¥{yearlyPrice.totalPrice}</span>
                      <span className={`block text-xs ${selectedDuration === 12 ? 'text-indigo-600' : 'text-gray-500'} mt-1`}>仅{yearlyPrice.dailyPrice}元/日</span>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* 选择支付方式 */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">选择支付方式</h3>
                {autoRenew && (
                  <p className="text-indigo-600 text-sm mb-4">
                    <FontAwesomeIcon icon={faInfoCircle} className="mr-1" />
                    连续包月当前仅限支付宝支付
                  </p>
                )}
                <div className="space-y-4">
                  {/* 支付宝 */}
                  <div
                    className={`border ${paymentMethod === 'alipay' ? 'border-indigo-300' : 'border-gray-200'} hover:border-indigo-300 rounded-lg p-4 cursor-pointer payment-option`}
                    onClick={() => handlePaymentMethodChange('alipay')}
                  >
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3">
                        <div className={`w-5 h-5 rounded-full ${paymentMethod === 'alipay' ? 'bg-blue-500' : 'bg-transparent'}`}></div>
                      </div>
                      <div className="flex items-center">
                        <FontAwesomeIcon icon={faAlipay} size="2x" className="text-blue-500 mr-3" />
                        <span className="text-gray-800 font-medium">支付宝</span>
                      </div>
                    </div>
                  </div>

                  {/* 微信支付 */}
                  <div
                    className={`border ${paymentMethod === 'wechat' ? 'border-indigo-300' : 'border-gray-200'} hover:border-indigo-300 rounded-lg p-4 cursor-pointer payment-option ${autoRenew ? 'opacity-50 pointer-events-none' : ''}`}
                    onClick={() => !autoRenew && handlePaymentMethodChange('wechat')}
                  >
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3">
                        <div className={`w-5 h-5 rounded-full ${paymentMethod === 'wechat' ? 'bg-blue-500' : 'bg-transparent'}`}></div>
                      </div>
                      <div className="flex items-center">
                        <FontAwesomeIcon icon={faWeixin} size="2x" className="text-green-500 mr-3" />
                        <span className="text-gray-800 font-medium">微信支付</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* 订单总额 */}
              <div className="border-t border-gray-200 pt-6 mb-6">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold text-gray-800">订单总额</span>
                  <span className="text-2xl font-bold text-indigo-700">¥{autoRenew && selectedDuration === 1 ? monthlyPriceWithDiscount : currentPrice.totalPrice}</span>
                </div>
                {autoRenew && (
                  <p className="text-sm text-gray-600 mt-2">
                    <FontAwesomeIcon icon={faInfoCircle} className="mr-1" />
                    您已选择连续包月，系统将在每月自动续费，可随时取消，享受5%优惠
                  </p>
                )}
              </div>
              
              {/* 订单确认按钮 */}
              <div className="flex justify-between items-center">
                <Link href="/membership" className="flex items-center text-indigo-600 hover:text-indigo-800">
                  <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
                  <span>返回会员页面</span>
                </Link>
                <button 
                  onClick={handleContinueToPayment}
                  className="px-8 py-3 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition"
                >
                  确认订单并支付
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// CSS 代码添加到页面底部
export const styles = `
  /* 开关样式 */
  .switch {
    position: relative;
    display: inline-block;
    width: 36px;
    height: 20px;
  }
  
  .switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
  }
  
  .slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
  }
  
  input:checked + .slider {
    background-color: #4f46e5;
  }
  
  input:focus + .slider {
    box-shadow: 0 0 1px #4f46e5;
  }
  
  input:checked + .slider:before {
    transform: translateX(16px);
  }
  
  .slider.round {
    border-radius: 34px;
  }
  
  .slider.round:before {
    border-radius: 50%;
  }
`; 