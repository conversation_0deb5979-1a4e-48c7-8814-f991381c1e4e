export interface ProblemExample {
    input: string;
    output: string;
}

export function getExamples(value: string) {
    const reg = '<input>([\\s\\S]*?)</input><output>([\\s\\S]*?)</output>'
    const re = RegExp(reg, 'g')
    const objList: ProblemExample[] = []
    let tmp = re.exec(value)
    while (tmp) {
        objList.push({ input: tmp[1], output: tmp[2] })
        tmp = re.exec(value)
    }
    return objList
}
