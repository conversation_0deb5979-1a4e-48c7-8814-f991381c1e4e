import {useAppSelector} from "@/app/redux/hooks";
import {selectIsAuthenticated, selectUser} from "@/app/redux/features/authSlice";
import {routerService} from "@/app/components/Router";
import {usePathname} from "next/navigation";
import {store} from "@/app/redux/store";
import {modalService} from "@/app/components/Modal";
import {toastService} from "@/app/components/Toast";

const MEMBERSHIP_LEVEL = {
	1: '基础版',
	2: 'Pro版',
	3: 'Ultra版'
}

// 创建一个非钩子函数，可以在任何地方使用
export const checkAuth = (requireMembershipLevel?: number) => {
	// 直接从store获取状态而不是使用钩子
	const state = store.getState();
	const isAuthenticated = selectIsAuthenticated(state);
	const user = selectUser(state);
	if (!isAuthenticated) {
		// 获取当前URL
		const pathname = window.location.pathname;
		const query = window.location.search
		routerService.navigateWithParams('/login', {redirect: pathname + query})
		return false
	}

	if (requireMembershipLevel) {
		const currentMembershipLevel = user.currentMembershipLevel || 0
		if (!user || currentMembershipLevel < requireMembershipLevel) {
			modalService.openModal('confirm', {
				title: '🔒 权限不足 - 会员等级限制',
				htmlContent: `<p>该功能需要「${MEMBERSHIP_LEVEL[requireMembershipLevel]}」及以上会员，您的当前等级为「${MEMBERSHIP_LEVEL[currentMembershipLevel] || '非会员'}」。</p><br /><p class="font-bold">👉 立即升级会员</p><p>解锁更多高级功能，享受专属权益！</p>`,
				dangerouslyUseHTML: true,
				confirmText: '去升级',
				onConfirm: () => routerService.navigate('/membership'),
			})
			return false
		}
	}
	return true
}

// 钩子版本，只在组件内使用
export const useAuthCheck = () => {
	const isAuthenticated = useAppSelector(selectIsAuthenticated);
	const user = useAppSelector(selectUser);
	const pathname = usePathname();

	const check = (requireMembershipLevel: number) => {
		if (!isAuthenticated) {
			console.log('未登录')
			routerService.navigateWithParams('/login', {redirect: pathname})
			return false
		}

		if (!user || user.currentMembershipLevel < requireMembershipLevel) {
			console.log('权限不足')
			return false
		}
		return true
	}

	return { check };
}

// 导出默认对象，包含钩子和普通函数
export default {
	useAuthCheck,
	checkAuth
};
