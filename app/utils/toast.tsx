/**
 * 全局Toast工具函数
 * 
 * 此文件提供了一套在应用任何地方都可以使用的Toast通知工具函数。
 * 这些函数可以在任何非React环境中使用，如工具函数、API响应处理等。
 */

import React from 'react';
import { toastService, ToastType } from '../components/Toast';

/**
 * 显示成功Toast通知
 * @param message 成功信息
 * @param duration 显示时间（毫秒）
 */
export const showSuccess = (message: string, duration: number = 3000): string => {
  return toastService.success(message, duration);
};

/**
 * 显示错误Toast通知
 * @param message 错误信息
 * @param duration 显示时间（毫秒）
 */
export const showError = (message: string, duration: number = 3000): string => {
  return toastService.error(message, duration);
};

/**
 * 显示警告Toast通知
 * @param message 警告信息
 * @param duration 显示时间（毫秒）
 */
export const showWarning = (message: string, duration: number = 3000): string => {
  return toastService.warning(message, duration);
};

/**
 * 显示信息Toast通知
 * @param message 提示信息
 * @param duration 显示时间（毫秒）
 */
export const showInfo = (message: string, duration: number = 3000): string => {
  return toastService.info(message, duration);
};

/**
 * 显示带图标的成功Toast
 * @param message 成功信息
 * @param duration 显示时间（毫秒）
 */
export const showSuccessWithIcon = (message: string, duration: number = 3000): string => {
  return toastService.success(
    message,
    duration
  );
};

/**
 * 显示带链接的Toast
 * @param message 消息前缀
 * @param linkText 链接文本
 * @param onClick 点击回调
 * @param duration 显示时间（毫秒）
 */
export const showActionToast = (
  message: string, 
  actionText: string,
  onClick: () => void,
  duration: number = 5000
): string => {
  // 由于无法在非React环境中直接使用JSX，所以对于带操作按钮的Toast，
  // 我们需要修改toastService以支持复杂的内容，或者使用简单的文本提示
  const actionMessage = `${message} [${actionText}]`;
  
  // 显示Toast
  const id = toastService.info(actionMessage, duration);
  
  // 存储回调函数，以便在后续实现点击操作时使用
  // 注意：这里只是示例，实际上这种方式无法直接支持点击操作
  // 真正实现带交互的Toast，需要扩展Toast组件本身
  console.log('Action callback stored for', id);
  
  return id;
};

/**
 * 显示API响应结果Toast
 * @param success 是否成功
 * @param successMessage 成功消息
 * @param errorMessage 错误消息
 */
export const showApiResultToast = (
  success: boolean,
  successMessage: string = '操作成功',
  errorMessage: string = '操作失败'
): string => {
  if (success) {
    return showSuccess(successMessage);
  } else {
    return showError(errorMessage);
  }
};

/**
 * 显示加载中Toast，返回关闭函数
 * @param message 加载消息
 * @returns 关闭Toast的函数
 */
export const showLoadingToast = (message: string = '加载中...'): () => void => {
  const id = toastService.info(message, 100000); // 较长时间
  return () => toastService.hideToast(id);
};

/**
 * 全局Toast工具集合
 */
export const ToastUtil = {
  success: showSuccess,
  error: showError,
  warning: showWarning,
  info: showInfo,
  withAction: showActionToast,
  apiResult: showApiResultToast,
  loading: showLoadingToast,
  // 直接暴露原始服务，以便进行更高级的定制
  service: toastService
};

export default ToastUtil; 