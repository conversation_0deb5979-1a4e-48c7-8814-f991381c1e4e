"use client";
import axios, { AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import {toastService} from "@/app/components/Toast";
import {modalService, ModalType} from "@/app/components/Modal";
import { routerService } from '../components/Router';
import { clearAuth, getToken, setToken } from './auth';

const uncatchableErrors = [30084, 20005];

// 创建axios实例
const instance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || '/api',
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
instance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 在发送请求之前做些什么
    // 例如添加token到请求头
    const token = getToken();
    if (token && config.headers) {
      config.headers['info-planets-token'] = `${token}`;
    }
    return config;
  },
  (error) => {
    // 对请求错误做些什么
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  (response: AxiosResponse) => {
    // 对响应数据做点什么
    const res = response.data;
    if (response.headers['info-planets-token'] !== undefined && response.headers['info-planets-token'] !== null) {
      setToken(response.headers['info-planets-token']);
    }
    // 根据实际业务情况处理响应
    if (res.code !== 1) {
      if (uncatchableErrors.includes(res.code)) {
        return Promise.reject(res);
      } else if (res.code === 20001) {
        toastService.info('需要登录后才能继续操作哦~')
        clearAuth();
        // 清除redux中的用户信息
        import('@/app/redux/features/authSlice').then(({ clearUser }) => {
          import('@/app/redux/store').then(({ store }) => {
            store.dispatch(clearUser());
          });
        });
        routerService.navigate('/login');
        return Promise.reject(res);
      } else if (res.code === 70014) {
        // 登录过期
        localStorage.removeItem('token')
        // 清除redux中的用户信息
        import('@/app/redux/features/authSlice').then(({ clearUser }) => {
          import('@/app/redux/store').then(({ store }) => {
            store.dispatch(clearUser());
          });
        });
        clearAuth();
      } else if (res.code === 20033) {
        modalService.openModal('confirm', {
          title: '🔒 权限不足 - 会员等级限制',
          htmlContent: `<p>${res.msg}</p><br /><p class="font-bold">👉 立即升级会员</p><p>解锁更多高级功能，享受专属权益！</p>`,
          dangerouslyUseHTML: true,
          confirmText: '去升级',
          onConfirm: () => routerService.navigate('/membership'),
        })
        return Promise.reject(res);
      } else {
        toastService.error(res.msg || '系统错误，请联系客服')
        return Promise.reject(res);
      }
    }
    return res;
  },
  (error) => {
    // 对响应错误做点什么
    if (error.response) {
      // 请求已发出，但服务器响应的状态码不在 2xx 范围内
      if (error.response.status === 401) {
        toastService.info('需要登录后才能继续操作哦~')
        modalService.openModal('confirm', {
          title: '提示',
          content: '登录已过期，请重新登录',
          onConfirm: () => {
            clearAuth();
            routerService.navigate('/login');
          }
        });
      } else {
        toastService.error(error.response.data?.message || '请求失败');
      }
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      toastService.error('网络错误，请检查网络连接');
    } else {
      // 发送请求时出了点问题
      toastService.error('请求配置错误');
    }
    return Promise.reject(error);
  }
);

// 封装GET请求
export const get = <T>(url: string, params?: any): Promise<T> => {
  return instance.get(url, { params });
};

// 封装POST请求
export const post = <T>(url: string, data?: any): Promise<T> => {
  return instance.post(url, data);
};

// 封装PUT请求
export const put = <T>(url: string, data?: any): Promise<T> => {
  return instance.put(url, data);
};

// 封装DELETE请求
export const del = <T>(url: string): Promise<T> => {
  return instance.delete(url);
};

export default instance;
