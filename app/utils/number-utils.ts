import {min} from "@floating-ui/utils";

export function formatKb(size: number) {
	if (size > 1024) {
		return (size / 1024).toFixed(2) + 'MB'
	}
	return size + 'KB'
}

/**
 * 格式化分钟
 *
 * @param minutes
 */
export function formatMinutes(minutes: number) {
	if (minutes < 60) {
		return minutes + '分钟'
	}
	const hours = Math.floor(minutes / 60)
	let format = hours + '小时'
	if (minutes % 60 > 0) {
		format += minutes % 60 + '分钟'
	}
	return format
}


/**
 * 格式化秒
 *
 * @param seconds
 */
export function formatSeconds(seconds: number) {
	if (seconds < 60) {
		return seconds + '秒'
	}
	let format = ''
	const hours = Math.floor(seconds / 3600)
	if (hours > 0) {
		format += hours + '小时'
		seconds = seconds % 3600
	}
	const minutes = Math.floor(seconds / 60)
	if (minutes > 0) {
		format += minutes + '分钟'
	}
	return format
}
