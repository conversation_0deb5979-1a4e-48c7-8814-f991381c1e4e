/**
 * 路由工具函数
 * 
 * 提供在应用的任何地方导航的工具函数，包括非React组件环境
 */

import { routerService, NavigateOptions } from '../components/Router';

/**
 * 导航到指定路径
 * @param path 目标路径
 * @param options 导航选项
 */
export const navigateTo = (path: string, options?: NavigateOptions): void => {
  routerService.navigate(path, options);
};

/**
 * 替换当前路由
 * @param path 目标路径
 * @param options 导航选项
 */
export const replaceTo = (path: string, options?: Omit<NavigateOptions, 'replace'>): void => {
  routerService.replace(path, options);
};

/**
 * 返回上一页
 */
export const goBack = (): void => {
  routerService.back();
};

/**
 * 前进到下一页
 */
export const goForward = (): void => {
  routerService.forward();
};

/**
 * 刷新当前页面
 */
export const refreshPage = (): void => {
  routerService.reload();
};

/**
 * 导航到带参数的路径
 * @param path 基础路径
 * @param params 查询参数
 * @param options 导航选项
 */
export const navigateWithParams = (
  path: string,
  params: Record<string, string | number | boolean>,
  options?: NavigateOptions
): void => {
  routerService.navigateWithParams(path, params, options);
};

/**
 * 导航到登录页
 * @param redirectAfter 登录后重定向的路径
 * @param options 导航选项
 */
export const navigateToLogin = (redirectAfter?: string, options?: NavigateOptions): void => {
  if (redirectAfter) {
    navigateWithParams('/login', { redirect: redirectAfter }, options);
  } else {
    navigateTo('/login', options);
  }
};

/**
 * 导航到首页
 * @param options 导航选项
 */
export const navigateToHome = (options?: NavigateOptions): void => {
  navigateTo('/', options);
};

/**
 * 全局Router工具集合
 */
export const RouterUtil = {
  to: navigateTo,
  replace: replaceTo,
  back: goBack,
  forward: goForward,
  refresh: refreshPage,
  withParams: navigateWithParams,
  toLogin: navigateToLogin,
  toHome: navigateToHome
};

export default RouterUtil; 