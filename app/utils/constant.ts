export const SUBJECT_DIFFICULTY_MAP: Record<number, { text: string, color: string, bgColor: string }> = {
    1: {
        text: '简单',
        color: 'text-green-500',
        bgColor: 'bg-green-100'
    },
    2: {
        text: '中等',
        color: 'text-yellow-500',
        bgColor: 'bg-yellow-100'
    },
    3: {
        text: '困难',
        color: 'text-red-500',
        bgColor: 'bg-red-100'
    }
}

export const CODING_PROBLEM_DIFFICULTY_MAP: Record<number, { text: string, color: string, bgColor: string }> = {
    1: {
        text: '简单',
        color: 'text-green-500',
        bgColor: 'bg-green-100'
    },
    2: {
        text: '中等',
        color: 'text-yellow-500',
        bgColor: 'bg-yellow-100'
    },
    3: {
        text: '困难',
        color: 'text-red-500',
        bgColor: 'bg-red-100'
    }
}

export const getProblemDifficultyColor = (difficulty: number) => {
    return CODING_PROBLEM_DIFFICULTY_MAP[difficulty].color + ' ' + CODING_PROBLEM_DIFFICULTY_MAP[difficulty].bgColor
}

export const getProblemDifficultyText = (difficulty: number) => {
    return CODING_PROBLEM_DIFFICULTY_MAP[difficulty].text
}

export const SUBJECT_TYPE_MAP: Record<number, string> = {
    1: '单选题',
    2: '多选题',
    3: '判断题'
}

export const JUDGE_STATUS = {
    '-10': {
        name: 'Not Submitted',
        short: 'NS',
        color: 'gray',
        type: 'info',
        rgb:'#909399'
    },
    '-5': {
        name: 'Submitted Unknown Result',
        short: 'SNR',
        color: 'gray',
        type: 'info',
        rgb:'#909399'
    },
    '-4': {
        name: '已取消',
        short: 'CA',
        color: 'purple',
        type: 'info',
        rgb:'#676fc1'
    },
    '-3': {
        name: '输出格式错误',
        short: 'PE',
        color: 'yellow',
        type: 'warning',
        rgb:'#f90'
    },
    '-2': {
        name: '编译失败',
        short: 'CE',
        color: 'yellow',
        type: 'warning',
        rgb:'#f90'
    },
    '-1': {
        name: '回答错误',
        short: 'WA',
        color: 'red',
        type: 'error',
        rgb:'#ed3f14'
    },
    '0': {
        name: '通过',
        short: 'AC',
        color: 'green',
        type: 'success',
        rgb:'#19be6b'
    },
    '1': {
        name: '时间超出限制',
        short: 'TLE',
        color: 'red',
        type: 'error',
        rgb:'#ed3f14'
    },
    '2': {
        name: '内存超出限制',
        short: 'MLE',
        color: 'red',
        type: 'error',
        rgb:'#ed3f14'
    },
    '3': {
        name: '运行时错误',
        short: 'RE',
        color: 'red',
        type: 'error',
        rgb:'#ed3f14'
    },
    '4': {
        name: '系统错误',
        short: 'SE',
        color: 'gray',
        type: 'info',
        rgb:'#909399'
    },
    '5': {
        name: '等待评测',
        color: 'yellow',
        type: 'warning',
        rgb:'#f90'
    },
    '6':{
        name: '编译中',
        short: 'CP',
        color: 'green',
        type: 'info',
        rgb:'#25bb9b'
    },
    '7': {
        name: '评测中',
        color: 'blue',
        type: '',
        rgb:'#2d8cf0'
    },
    '8': {
        name: '部分通过',
        short: 'PAC',
        color: 'blue',
        type: '',
        rgb:'#2d8cf0'
    },
    '9': {
        name: '提交中',
        color: 'yellow',
        type: 'warning',
        rgb:'#f90'
    },
    '10':{
        name:"提交失败",
        color:'gray',
        short:'SF',
        type: 'info',
        rgb:'#909399',
    }
}

export const EXAM_DIFFICULTY = {
    1: {
        text: '入门级',
    },
    2: {
        text: '基础级'
    },
    3: {
        text: '提高级'
    },
    4: {
        text: '竞赛级'
    }
}

export const EXAM_STATUS = {
    0: {
        text: '进行中',
        style: 'bg-blue-100 text-blue-800'
    },
    1: {
        text: '评分中',
        style: 'bg-yellow-100 text-yellow-800'
    },
    2: {
        text: '已完成',
        style: 'bg-green-100 text-green-800'
    }
}

export const PROBLEM_LIST_DIFFICULTY_MAP = {
    1: {
        text: '入门'
    },
    2: {
        text: '基础'
    },
    3: {
        text: '中等'
    },
    4: {
        text: '进阶'
    },
    5: {
        text: '挑战'
    }
}
