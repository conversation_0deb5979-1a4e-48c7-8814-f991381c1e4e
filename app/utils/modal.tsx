/**
 * 全局Modal工具函数
 * 
 * 此文件提供了一套在应用任何地方都可以使用的Modal对话框工具函数。
 * 这些函数可以在任何非React环境中使用，如工具函数、API响应处理等。
 */

import { modalService } from '@/components/Modal';

/**
 * 显示成功提示对话框
 * @param message 成功信息
 * @param onClose 关闭回调
 */
export const showSuccess = (message: string, onClose?: () => void): void => {
  modalService.alert(
    <div className="flex items-center text-green-600">
      <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
      </svg>
      {message}
    </div>,
    {
      title: '成功',
      onConfirm: onClose,
      okText: '确定'
    }
  );
};

/**
 * 显示错误提示对话框
 * @param message 错误信息
 * @param onClose 关闭回调
 */
export const showError = (message: string, onClose?: () => void): void => {
  modalService.alert(
    <div className="flex items-center text-red-600">
      <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
      </svg>
      {message}
    </div>,
    {
      title: '错误',
      onConfirm: onClose,
      okText: '关闭'
    }
  );
};

/**
 * 显示确认对话框
 * @param message 确认信息
 * @param onConfirm 确认回调
 * @param onCancel 取消回调
 */
export const confirm = (
  message: string,
  onConfirm: () => void,
  onCancel?: () => void
): void => {
  modalService.confirm(message, {
    title: '确认操作',
    onConfirm,
    onCancel,
    confirmText: '确认',
    cancelText: '取消'
  });
};

/**
 * 显示删除确认对话框
 * @param itemName 要删除的项目名称
 * @param onConfirm 确认删除回调
 */
export const confirmDelete = (
  itemName: string,
  onConfirm: () => void
): void => {
  modalService.confirm(
    <div className="text-red-600">
      <p>您确定要删除 <strong>{itemName}</strong> 吗？</p>
      <p className="mt-2 text-sm">此操作无法撤销，请谨慎操作。</p>
    </div>,
    {
      title: '确认删除',
      confirmText: '删除',
      cancelText: '取消',
      onConfirm,
      onCancel: () => console.log('取消删除')
    }
  );
};

/**
 * 显示加载对话框
 * @param message 加载提示信息
 * @returns 返回关闭函数
 */
export const showLoading = (message: string = '加载中，请稍候...'): () => void => {
  const id = modalService.alert(message, {
    title: '请稍候',
    showCloseButton: false,
    closeOnEsc: false,
    closeOnOutsideClick: false
  });

  return () => modalService.closeModal(id);
};

/**
 * 全局Modal工具集合
 */
export const ModalUtil = {
  showSuccess,
  showError,
  confirm,
  confirmDelete,
  showLoading,
  // 直接暴露原始服务，以便进行更高级的定制
  service: modalService
};

export default ModalUtil; 