// 初始化Mock服务
export async function initMockService() {
  // 只在开发环境和浏览器端启用mock
  if (typeof window === 'undefined' || process.env.NODE_ENV !== 'development') {
    console.log('跳过Mock服务初始化: 服务器端环境或生产模式');
    return null;
  }

  // 防止重复初始化
  if ((window as any).__MSW_INITIALIZED__) {
    console.log('Mock服务已经初始化，跳过');
    return (window as any).__MSW_WORKER__;
  }

  try {
    console.log('正在初始化Mock服务...');
    
    // 动态导入，防止在生产环境中包含此代码
    const { worker } = await import('../mocks/browser');
    
    // 启动MSW
    const setupPromise = worker.start({
      onUnhandledRequest: 'bypass', // 对于未被模拟的请求，直接通过
      serviceWorker: {
        url: '/mockServiceWorker.js',
        options: {
          scope: '/'
        }
      }
    });
    
    await setupPromise;
    
    // 将worker实例和初始化状态保存在window对象上
    (window as any).__MSW_WORKER__ = worker;
    (window as any).__MSW_INITIALIZED__ = true;
    
    console.log('Mock服务初始化成功! 已注册处理程序:', worker);
    
    return worker;
  } catch (error) {
    console.error('Mock服务初始化失败:', error);
    throw error;
  }
} 