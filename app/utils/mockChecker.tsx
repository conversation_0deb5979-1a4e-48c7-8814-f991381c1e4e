"use client";

import { useState, useEffect } from 'react';

export default function useMockStatus() {
  const [status, setStatus] = useState<'checking' | 'active' | 'inactive'>('checking');

  useEffect(() => {
    const checkMockStatus = async () => {
      try {
        // 简单的检查 - 尝试调用一个基本API
        const response = await fetch('/api/basic-problems?page=1&pageSize=1');
        
        // 检查响应是否正常并且是否有预期的格式
        const data = await response.json();
        
        if (data && data.code === 200 && data.data && Array.isArray(data.data.items)) {
          setStatus('active');
        } else {
          setStatus('inactive');
        }
      } catch (err) {
        console.error('Mock status check failed:', err);
        setStatus('inactive');
      }
    };

    // 给Mock服务一点时间启动
    const timer = setTimeout(() => {
      checkMockStatus();
    }, 2000);
    
    return () => clearTimeout(timer);
  }, []);

  return status;
}

// 可选的UI组件，用于显示Mock状态
export function MockStatusIndicator() {
  const status = useMockStatus();
  
  if (status === 'checking') {
    return null;
  }
  
  return (
    <div style={{ 
      position: 'fixed', 
      bottom: '10px', 
      left: '10px', 
      backgroundColor: status === 'active' ? 'rgba(40, 167, 69, 0.9)' : 'rgba(220, 53, 69, 0.9)', 
      color: 'white',
      padding: '8px 12px',
      borderRadius: '4px',
      fontSize: '12px',
      zIndex: 9999,
    }}>
      Mock服务: {status === 'active' ? '正常' : '异常'}
    </div>
  );
} 