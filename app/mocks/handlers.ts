import { http, HttpResponse } from 'msw';

// 模拟数据
const users = [
  { id: 1, username: 'student1', name: '张小明', avatar: 'https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?w=150&h=150&fit=crop' },
  { id: 2, username: 'student2', name: '李小华', avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop' },
];

const news = [
  { 
    id: 1, 
    title: '全国青少年信息学奥林匹克竞赛报名开始', 
    summary: '2023年全国青少年信息学奥林匹克竞赛报名已经开始，请符合条件的学生尽快报名参加。', 
    content: '全国青少年信息学奥林匹克竞赛（National Olympiad in Informatics, NOI）是面向全国青少年的信息学竞赛，旨在向广大青少年普及计算机科学知识，提高他们的计算思维能力和创新能力。\n\n本次比赛分为初赛和复赛两个阶段。初赛采用笔试形式，主要考察基础算法和数据结构知识；复赛采用上机实践形式，考察实际编程和解决问题的能力。\n\n参赛对象为全国中小学在校学生，特别是对计算机科学和编程有浓厚兴趣的学生。', 
    image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=600&h=400&fit=crop', 
    author: '信竞星球官方', 
    publishDate: '2023-09-15', 
    views: 1256 
  },
  { 
    id: 2, 
    title: 'Python入门教程：从零开始学编程', 
    summary: '最新推出的Python入门系列教程，帮助零基础学生快速入门编程世界。', 
    content: 'Python是一种易于学习且功能强大的编程语言。它具有高效的高级数据结构和简单但有效的面向对象编程方法。Python优雅的语法和动态类型及其解释性质，使其成为大多数平台上的脚本编写和快速应用程序开发的理想语言。\n\n本教程将从Python的安装开始，逐步介绍变量、数据类型、控制流、函数、模块等基础知识，最后通过实际项目练习巩固所学内容。\n\n无论您是否有编程经验，本教程都将帮助您快速掌握Python编程的基础知识，为进一步学习打下坚实基础。', 
    image: 'https://images.unsplash.com/photo-1526379879527-8559ecfcb0c8?w=600&h=400&fit=crop', 
    author: '王教授', 
    publishDate: '2023-09-01', 
    views: 2341 
  },
  { 
    id: 3, 
    title: '少儿编程：培养未来的科技创新者', 
    summary: '如何通过编程教育培养孩子的创造力和逻辑思维能力？', 
    content: '在当今数字化时代，编程技能已成为未来人才必备的核心素养之一。少儿编程教育不仅仅是教孩子们写代码，更是培养他们的计算思维、逻辑推理能力和创新精神。\n\n通过编程学习，孩子们可以：\n1. 提升解决问题的能力\n2. 培养逻辑思维\n3. 增强创造力\n4. 学习数学和科学概念\n5. 为未来职业发展打下基础\n\n研究表明，早期接触编程的孩子在数学成绩上表现更好，并且在面对复杂问题时展现出更强的分析能力。', 
    image: 'https://images.unsplash.com/photo-1517457373958-b7bdd4587205?w=600&h=400&fit=crop', 
    author: '李专家', 
    publishDate: '2023-08-20', 
    views: 1890 
  },
];

const basicProblems = [
  {
    id: 1,
    title: '计算1到100的和',
    difficulty: '简单',
    type: '基础算法',
    description: '请编写一个程序，计算从1加到100的结果。',
    hint: '考虑使用循环结构或数学公式。',
    solution: '可以使用等差数列求和公式：S = n(a₁ + aₙ)/2，其中n是项数，a₁是首项，aₙ是末项。代入得：S = 100(1+100)/2 = 100×101/2 = 5050。',
    tags: ['循环', '数学']
  },
  {
    id: 2,
    title: '判断质数',
    difficulty: '中等',
    type: '数论',
    description: '请编写一个程序，判断一个输入的正整数是否为质数。质数是只能被1和自身整除的大于1的整数。',
    hint: '考虑如何高效地判断一个数是否为质数。',
    solution: '一个高效的方法是：对于一个数n，只需要检查它是否能被2到√n之间的数整除。如果不能被这些数整除，那么它就是一个质数。',
    tags: ['数论', '质数']
  },
  {
    id: 3,
    title: '斐波那契数列',
    difficulty: '中等',
    type: '递归与动态规划',
    description: '请计算斐波那契数列的第n项。斐波那契数列的定义如下：F(0) = 0, F(1) = 1, F(n) = F(n-1) + F(n-2) (n >= 2)。',
    hint: '考虑使用递归或动态规划来解决这个问题。注意处理大数的情况。',
    solution: '使用动态规划可以避免递归带来的重复计算问题。创建一个数组存储已计算的值，然后从下到上计算每一项。',
    tags: ['递归', '动态规划', '数列']
  }
];

const codingProblems = [
  {
    id: 1,
    title: '两数之和',
    difficulty: '简单',
    type: '数组',
    description: '给定一个整数数组 nums 和一个目标值 target，请你在该数组中找出和为目标值的那两个整数，并返回他们的数组下标。你可以假设每种输入只会对应一个答案。但是，你不能重复利用这个数组中同样的元素。',
    examples: [
      {
        input: 'nums = [2, 7, 11, 15], target = 9',
        output: '[0, 1]',
        explanation: '因为 nums[0] + nums[1] = 2 + 7 = 9, 所以返回 [0, 1]'
      }
    ],
    solution: '使用哈希表记录每个数和其下标。遍历数组，对于每个数，查找哈希表中是否存在目标值减去当前数的结果，如果存在，则返回两个下标。',
    code: `function twoSum(nums, target) {
  const map = new Map();
  for(let i = 0; i < nums.length; i++) {
    const complement = target - nums[i];
    if(map.has(complement)) {
      return [map.get(complement), i];
    }
    map.set(nums[i], i);
  }
  return null;
}`,
    tags: ['数组', '哈希表']
  },
  {
    id: 2,
    title: '反转链表',
    difficulty: '中等',
    type: '链表',
    description: '反转一个单链表。例如，输入：1->2->3->4->5->NULL，输出：5->4->3->2->1->NULL。',
    examples: [
      {
        input: '1->2->3->4->5->NULL',
        output: '5->4->3->2->1->NULL'
      }
    ],
    solution: '使用迭代方法：使用三个指针（prev、current和next）遍历链表，不断改变指针的指向。',
    code: `function reverseList(head) {
  let prev = null;
  let current = head;
  while (current !== null) {
    let next = current.next;
    current.next = prev;
    prev = current;
    current = next;
  }
  return prev;
}`,
    tags: ['链表', '指针']
  }
];

const exams = [
  {
    id: 1,
    title: '初级编程能力测试',
    description: '测试基础编程概念和简单算法的理解。',
    duration: 60, // 分钟
    questionCount: 10,
    difficulty: '初级',
    coverImage: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=600&h=400&fit=crop'
  },
  {
    id: 2,
    title: '中级算法挑战',
    description: '包含数据结构和算法的实际应用题目。',
    duration: 90, // 分钟
    questionCount: 8,
    difficulty: '中级',
    coverImage: 'https://images.unsplash.com/photo-1503437313881-503a91226402?w=600&h=400&fit=crop'
  }
];

export const handlers = [
  // 用户登录
  http.post('/api/login', async ({ request }) => {
    console.log('Mock: 处理 /api/login 请求');
    const requestData = await request.json();
    const { username, password } = requestData as { username: string; password: string };
    const user = users.find(u => u.username === username);
    
    if (user && password === '123456') { // 简化的密码验证
      return HttpResponse.json({
        code: 200,
        data: {
          token: 'mock-jwt-token',
          user: { ...user, password: undefined }
        },
        message: '登录成功'
      });
    }
    
    return HttpResponse.json({
      code: 401,
      message: '用户名或密码错误'
    }, { status: 401 });
  }),
  
  // 获取用户信息
  http.get('/api/user/info', () => {
    console.log('Mock: 处理 /api/user/info 请求');
    return HttpResponse.json({
      code: 200,
      data: {
        user: users[0]
      },
      message: '获取用户信息成功'
    });
  }),
  
  // 获取新闻列表
  http.get('/api/news', ({ request }) => {
    console.log('Mock: 处理 /api/news 请求');
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10');
    
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const paginatedNews = news.slice(start, end);
    
    return HttpResponse.json({
      code: 200,
      data: {
        items: paginatedNews,
        total: news.length,
        page,
        pageSize
      },
      message: '获取新闻列表成功'
    });
  }),
  
  // 获取新闻详情
  http.get('/api/news/:id', ({ params }) => {
    console.log(`Mock: 处理 /api/news/${params.id} 请求`);
    const { id } = params;
    const newsItem = news.find(n => n.id === Number(id));
    
    if (newsItem) {
      return HttpResponse.json({
        code: 200,
        data: newsItem,
        message: '获取新闻详情成功'
      });
    }
    
    return HttpResponse.json({
      code: 404,
      message: '新闻不存在'
    }, { status: 404 });
  }),

  // 获取基础题目列表
  http.get('/api/basic-problems', ({ request }) => {
    console.log('Mock: 处理 /api/basic-problems 请求');
    
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10');
    const type = url.searchParams.get('type');
    const difficulty = url.searchParams.get('difficulty');
    const search = url.searchParams.get('search');
    
    console.log('Mock: 查询参数', { page, pageSize, type, difficulty, search });
    
    let filteredProblems = [...basicProblems];
    
    if (type) {
      filteredProblems = filteredProblems.filter(p => p.type === type);
    }
    
    if (difficulty) {
      filteredProblems = filteredProblems.filter(p => p.difficulty === difficulty);
    }
    
    if (search) {
      const searchLower = search.toLowerCase();
      filteredProblems = filteredProblems.filter(p => 
        p.title.toLowerCase().includes(searchLower) || 
        p.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }
    
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const paginatedProblems = filteredProblems.slice(start, end);
    
    console.log(`Mock: 返回 ${paginatedProblems.length} 个题目 (总计: ${filteredProblems.length})`);
    
    return HttpResponse.json({
      code: 200,
      data: {
        items: paginatedProblems,
        total: filteredProblems.length,
        page,
        pageSize
      },
      message: '获取基础题目列表成功'
    });
  }),
  
  // 获取基础题目详情
  http.get('/api/basic-problems/:id', ({ params }) => {
    console.log(`Mock: 处理 /api/basic-problems/${params.id} 请求`);
    const { id } = params;
    const problem = basicProblems.find(p => p.id === Number(id));
    
    if (problem) {
      return HttpResponse.json({
        code: 200,
        data: problem,
        message: '获取基础题目详情成功'
      });
    }
    
    return HttpResponse.json({
      code: 404,
      message: '题目不存在'
    }, { status: 404 });
  }),

  // 获取编程题目列表
  http.get('/api/coding-problems', ({ request }) => {
    console.log('Mock: 处理 /api/coding-problems 请求');
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10');
    const type = url.searchParams.get('type');
    const difficulty = url.searchParams.get('difficulty');
    
    let filteredProblems = [...codingProblems];
    if (type) {
      filteredProblems = filteredProblems.filter(p => p.type === type);
    }
    if (difficulty) {
      filteredProblems = filteredProblems.filter(p => p.difficulty === difficulty);
    }
    
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const paginatedProblems = filteredProblems.slice(start, end);
    
    return HttpResponse.json({
      code: 200,
      data: {
        items: paginatedProblems,
        total: filteredProblems.length,
        page,
        pageSize
      },
      message: '获取编程题目列表成功'
    });
  }),
  
  // 获取编程题目详情
  http.get('/api/coding-problems/:id', ({ params }) => {
    console.log(`Mock: 处理 /api/coding-problems/${params.id} 请求`);
    const { id } = params;
    const problem = codingProblems.find(p => p.id === Number(id));
    
    if (problem) {
      return HttpResponse.json({
        code: 200,
        data: problem,
        message: '获取编程题目详情成功'
      });
    }
    
    return HttpResponse.json({
      code: 404,
      message: '题目不存在'
    }, { status: 404 });
  }),

  // 获取考试列表
  http.get('/api/exams', ({ request }) => {
    console.log('Mock: 处理 /api/exams 请求');
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10');
    
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const paginatedExams = exams.slice(start, end);
    
    return HttpResponse.json({
      code: 200,
      data: {
        items: paginatedExams,
        total: exams.length,
        page,
        pageSize
      },
      message: '获取考试列表成功'
    });
  }),
  
  // 获取考试详情
  http.get('/api/exams/:id', ({ params }) => {
    console.log(`Mock: 处理 /api/exams/${params.id} 请求`);
    const { id } = params;
    const exam = exams.find(e => e.id === Number(id));
    
    if (exam) {
      return HttpResponse.json({
        code: 200,
        data: exam,
        message: '获取考试详情成功'
      });
    }
    
    return HttpResponse.json({
      code: 404,
      message: '考试不存在'
    }, { status: 404 });
  })
]; 