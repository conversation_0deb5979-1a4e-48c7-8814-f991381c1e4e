'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCheckCircle, faPercentage, faCalendarCheck, faFire,
  faChevronRight, faInfoCircle, faTag, faClock, faExclamationTriangle,
  faBook, faCode, faLightbulb, faSitemap, faRandom, faCalendarAlt,
  faSpinner
} from '@fortawesome/free-solid-svg-icons';
import { Chart, registerables } from 'chart.js';
import ProgressSkeleton from '../components/ProgressSkeleton';
import ChartSkeleton from '../components/ChartSkeleton';
import KnowledgeChartSkeleton from '../components/KnowledgeChartSkeleton';
import profileService from '../service/profile-service';
import progressService from '../service/progress-service';
import problemListService from '../service/problem-list-service';
import CommonDropdown, { DropdownOption } from '../components/CommonDropdown';
import { useRouter } from 'next/navigation';

// 注册 Chart.js 组件
Chart.register(...registerables);

// 定义接口
interface Progress {
  basicProblemAcceptCount: number;
  basicProblemTotal: number;
  codingProblemAcceptCount: number;
  codingProblemTotal: number;
  easyCount: number;
  mediumCount: number;
  hardCount: number;
  easyTotal: number;
  mediumTotal: number;
  hardTotal: number;
}

interface StudyStat {
  solveProblemCounts: number[];
}

interface HeatmapData {
  time: string;
  count: number;
}

interface KnowledgeProgress {
  label: string;
  percentage: number;
}

export default function Progress() {
  // 添加状态
  const [progress, setProgress] = useState<Progress | null>(null);
  const [studyStat, setStudyStat] = useState<StudyStat | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [heatmapData, setHeatmapData] = useState<Record<string, number>>({});
  const [heatmapLoading, setHeatmapLoading] = useState(true);
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [availableYears, setAvailableYears] = useState<number[]>([]);
  const [knowledgeProgress, setKnowledgeProgress] = useState<{
    basicProblem: KnowledgeProgress[];
    codingProblem: KnowledgeProgress[];
  } | null>(null);
  const [knowledgeLoading, setKnowledgeLoading] = useState(true);
  const [isGeneratingProblemList, setIsGeneratingProblemList] = useState(false);
  const [problemListId, setProblemListId] = useState<number | null>(null);

  const router = useRouter();

  // 获取数据
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        // 获取解题进度
        const progressData = await profileService.getProgress();
        // 添加默认值处理，确保即使API未返回新字段也不会报错
        setProgress({
          ...progressData.data,
          easyTotal: progressData.data.easyTotal || 100,
          mediumTotal: progressData.data.mediumTotal || 100,
          hardTotal: progressData.data.hardTotal || 100
        });

        // 获取学习统计数据
        const studyData = await profileService.getStudyStat({ days: 7 });
        setStudyStat(studyData.data);

        // 获取知识点掌握情况
        setKnowledgeLoading(true);
        const knowledgeData = await progressService.getKnowledgeProgress();
        setKnowledgeProgress(knowledgeData.data);
        setKnowledgeLoading(false);

        setLoading(false);
      } catch (err) {
        console.error('获取数据失败：', err);
        setError('获取数据失败，请稍后重试');
        setLoading(false);
        setKnowledgeLoading(false);
      }
    };

    fetchData();
  }, []);

  // 计算最长连续打卡天数
  const calculateMaxConsecutiveDays = (counts: number[]): number => {
    if (!counts || counts.length === 0) return 0;

    let maxConsecutive = 0;
    let currentConsecutive = 0;

    for (let i = 0; i < counts.length; i++) {
      if (counts[i] > 0) {
        currentConsecutive++;
        maxConsecutive = Math.max(maxConsecutive, currentConsecutive);
      } else {
        currentConsecutive = 0;
      }
    }

    return maxConsecutive;
  };

  // 获取热力图数据
  useEffect(() => {
    const fetchHeatmapData = async () => {
      try {
        setHeatmapLoading(true);

        // 设置开始时间为选定年份的1月1日
        const startTime = `${selectedYear}-01-01`;
        // 设置结束时间为选定年份的12月31日
        const endTime = `${selectedYear}-12-31`;

        const response = await progressService.getHeatmapData({
          startTime,
          endTime
        });

        setHeatmapData(response.data);
        setHeatmapLoading(false);
      } catch (err) {
        console.error('获取热力图数据失败：', err);
        setError('获取热力图数据失败，请稍后重试');
        setHeatmapLoading(false);
      }
    };

    fetchHeatmapData();
  }, [selectedYear]);

  // 初始化可选年份
  useEffect(() => {
    const currentYear = new Date().getFullYear();
    // 从2024年到当前年份
    const years = [];
    for (let year = 2024; year <= currentYear; year++) {
      years.push(year);
    }
    setAvailableYears(years);
  }, []);

  // 初始化热力图
  useEffect(() => {
    if (heatmapLoading) return;

    // 创建热力图
    renderHeatmap(selectedYear);

    return () => {
      // 清理热力图
      const heatmapContainer = document.getElementById('heatmap-container');
      if (heatmapContainer) {
        heatmapContainer.innerHTML = '';
      }
    };
  }, [heatmapLoading, heatmapData, selectedYear]);

  // 渲染热力图的函数
  const renderHeatmap = (year: number) => {
    const container = document.getElementById('heatmap-container');
    if (!container) return;

    // 清空容器
    container.innerHTML = '';

    // 处理数据，创建日期映射
    const dateMap = new Map<string, number>(Object.entries(heatmapData));

    // 获取选择年份的第一天是星期几
    const firstDayOfYear = new Date(year, 0, 1).getDay();

    // 计算总共的周数
    const totalWeeks = Math.ceil((new Date(year, 11, 31).getTime() - new Date(year, 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1;

    // 创建整体容器
    const mainContainer = document.createElement('div');
    mainContainer.className = 'flex flex-col';
    container.appendChild(mainContainer);

    // 创建月份标签容器
    const monthLabelsContainer = document.createElement('div');
    monthLabelsContainer.className = 'flex pl-8 mb-1 text-xs text-gray-500';
    mainContainer.appendChild(monthLabelsContainer);

    // 添加月份标签
    const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
    monthNames.forEach(month => {
      const monthLabel = document.createElement('div');
      monthLabel.className = 'flex-1 text-center';
      monthLabel.textContent = month;
      monthLabelsContainer.appendChild(monthLabel);
    });

    // 创建星期标签和图表容器
    const flexContainer = document.createElement('div');
    flexContainer.className = 'flex';
    mainContainer.appendChild(flexContainer);

    // 创建星期标签
    const weekdayLabels = document.createElement('div');
    weekdayLabels.className = 'flex flex-col justify-between pr-2 text-xs text-gray-500';
    flexContainer.appendChild(weekdayLabels);

    // 添加星期标签
    const weekdayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    for (let i = 0; i < 7; i++) {
      const weekdayLabel = document.createElement('div');
      weekdayLabel.className = 'h-4 leading-4';
      weekdayLabel.textContent = weekdayNames[i];
      weekdayLabels.appendChild(weekdayLabel);
    }

    // 创建热力图网格容器
    const gridContainer = document.createElement('div');
    gridContainer.className = 'grid grid-flow-col gap-1 flex-1';
    flexContainer.appendChild(gridContainer);

    // 创建每周的列
    for (let week = 0; week < totalWeeks; week++) {
      const weekColumn = document.createElement('div');
      weekColumn.className = 'grid grid-rows-7 gap-1';
      gridContainer.appendChild(weekColumn);

      // 添加每周的每一天
      for (let day = 0; day < 7; day++) {
        const date = new Date(year, 0, 1);
        date.setDate(date.getDate() + week * 7 + day - firstDayOfYear);

        // 只显示选择年份内的日期
        if (date.getFullYear() !== year) {
          const emptyCell = document.createElement('div');
          emptyCell.className = 'w-4 h-4 rounded opacity-0';
          weekColumn.appendChild(emptyCell);
          continue;
        }

        const dateString = date.toISOString().split('T')[0];
        const count = dateMap.get(dateString) || 0;

        const cell = document.createElement('div');
        cell.className = 'w-4 h-4 rounded cursor-pointer';

        // 设置颜色，根据提交数量
        if (count === 0) {
          cell.className += ' bg-gray-100';
        } else if (count < 3) {
          cell.className += ' bg-green-200';
        } else if (count < 6) {
          cell.className += ' bg-green-300';
        } else if (count < 9) {
          cell.className += ' bg-green-400';
        } else {
          cell.className += ' bg-green-500';
        }

        // 添加悬停提示
        cell.title = `${dateString}: ${count} 次提交`;

        weekColumn.appendChild(cell);
      }
    }

    // 添加图例
    const legendContainer = document.createElement('div');
    legendContainer.className = 'flex items-center justify-end mt-2 text-xs text-gray-500';

    const legendLabel = document.createElement('span');
    legendLabel.textContent = '提交次数：';
    legendContainer.appendChild(legendLabel);

    const legendItems = [
      { class: 'bg-gray-100', text: '0' },
      { class: 'bg-green-200', text: '1-2' },
      { class: 'bg-green-300', text: '3-5' },
      { class: 'bg-green-400', text: '6-8' },
      { class: 'bg-green-500', text: '9+' }
    ];

    legendItems.forEach(item => {
      const legendItem = document.createElement('div');
      legendItem.className = 'flex items-center ml-2';

      const legendColor = document.createElement('div');
      legendColor.className = `w-3 h-3 rounded ${item.class}`;
      legendItem.appendChild(legendColor);

      const legendText = document.createElement('span');
      legendText.className = 'ml-1';
      legendText.textContent = item.text;
      legendItem.appendChild(legendText);

      legendContainer.appendChild(legendItem);
    });

    container.appendChild(legendContainer);
  };

  // 生成图表
  useEffect(() => {
    if (loading || !studyStat) return;

    // 每日学习记录图表
    const dailyRecordCtx = document.getElementById('dailyRecordChart') as HTMLCanvasElement;
    if (dailyRecordCtx) {
      // 获取最近7天的日期标签
      const dateLabels = Array.from({ length: 7 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - (6 - i));
        return `${date.getMonth() + 1}/${date.getDate()}`;
      });

      const dailyRecordChart = new Chart(dailyRecordCtx, {
        type: 'bar',
        data: {
          labels: dateLabels,
          datasets: [{
            label: '解题数量',
            data: studyStat.solveProblemCounts.slice(-7),
            backgroundColor: 'rgba(99, 102, 241, 0.8)',
            borderColor: 'rgba(99, 102, 241, 1)',
            borderWidth: 1,
            borderRadius: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              grid: {
                display: false
              }
            },
            x: {
              grid: {
                display: false
              }
            }
          },
          plugins: {
            legend: {
              display: false
            }
          }
        }
      });
    }

    // 清理函数
    return () => {
      // 只有当不在加载状态，且有数据时才尝试销毁图表
      if (!loading && studyStat) {
        if (dailyRecordCtx) {
          const chartInstance = Chart.getChart(dailyRecordCtx);
          if (chartInstance) chartInstance.destroy();
        }
      }
    };
  }, [loading, studyStat]);

  // 知识点掌握情况图表
  useEffect(() => {
    if (knowledgeLoading || !knowledgeProgress) return;

    // 基础题知识点掌握情况图表
    const basicKnowledgeCtx = document.getElementById('basicKnowledgeChart') as HTMLCanvasElement;
    if (basicKnowledgeCtx) {
      const basicKnowledgeData = knowledgeProgress.basicProblem;
      const basicKnowledgeChart = new Chart(basicKnowledgeCtx, {
        type: 'radar',
        data: {
          labels: basicKnowledgeData.map(item => item.label),
          datasets: [{
            label: '掌握程度',
            data: basicKnowledgeData.map(item => item.percentage),
            backgroundColor: 'rgba(99, 102, 241, 0.2)',
            borderColor: 'rgba(99, 102, 241, 0.8)',
            borderWidth: 2,
            pointBackgroundColor: 'rgba(99, 102, 241, 1)',
            pointHoverBackgroundColor: 'rgba(99, 102, 241, 1)',
            pointHoverBorderColor: 'rgba(99, 102, 241, 1)',
            pointHoverBorderWidth: 3,
            pointHoverRadius: 7,
            pointRadius: 5
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            r: {
              angleLines: {
                display: true
              },
              suggestedMin: 0,
              suggestedMax: 100,
              pointLabels: {
                font: {
                  size: 12
                },
                callback: function (value: any) {
                  return value;
                }
              },
              ticks: {
                display: true,
                showLabelBackdrop: false,
                stepSize: 20
              }
            }
          },
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#1F2937',
              bodyColor: '#4F46E5',
              borderColor: 'rgba(99, 102, 241, 0.3)',
              borderWidth: 1,
              padding: 10,
              cornerRadius: 4,
              boxPadding: 4,
              usePointStyle: true,
              callbacks: {
                title: function (context: any) {
                  return context[0].label;
                },
                label: function (context: any) {
                  const index = context.dataIndex;
                  const level = getKnowledgeLevelText(context.raw);
                  return [`掌握程度: ${context.raw}%`, `状态: ${level}`];
                }
              }
            }
          },
          interaction: {
            mode: 'nearest',
            intersect: true,
            axis: 'r'
          }
        }
      });
    }

    // 编程题知识点掌握情况图表
    const codingKnowledgeCtx = document.getElementById('codingKnowledgeChart') as HTMLCanvasElement;
    if (codingKnowledgeCtx) {
      const codingKnowledgeData = knowledgeProgress.codingProblem;
      const codingKnowledgeChart = new Chart(codingKnowledgeCtx, {
        type: 'radar',
        data: {
          labels: codingKnowledgeData.map(item => item.label),
          datasets: [{
            label: '掌握程度',
            data: codingKnowledgeData.map(item => item.percentage),
            backgroundColor: 'rgba(16, 185, 129, 0.2)',
            borderColor: 'rgba(16, 185, 129, 0.8)',
            borderWidth: 2,
            pointBackgroundColor: 'rgba(16, 185, 129, 1)',
            pointHoverBackgroundColor: 'rgba(16, 185, 129, 1)',
            pointHoverBorderColor: 'rgba(16, 185, 129, 1)',
            pointHoverBorderWidth: 3,
            pointHoverRadius: 7,
            pointRadius: 5
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            r: {
              angleLines: {
                display: true
              },
              suggestedMin: 0,
              suggestedMax: 100,
              pointLabels: {
                font: {
                  size: 12
                },
                callback: function (value: any) {
                  return value;
                }
              },
              ticks: {
                display: true,
                showLabelBackdrop: false,
                stepSize: 20
              }
            }
          },
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#1F2937',
              bodyColor: '#059669',
              borderColor: 'rgba(16, 185, 129, 0.3)',
              borderWidth: 1,
              padding: 10,
              cornerRadius: 4,
              boxPadding: 4,
              usePointStyle: true,
              callbacks: {
                title: function (context: any) {
                  return context[0].label;
                },
                label: function (context: any) {
                  const index = context.dataIndex;
                  const level = getKnowledgeLevelText(context.raw);
                  return [`掌握程度: ${context.raw}%`, `状态: ${level}`];
                }
              }
            }
          },
          interaction: {
            mode: 'nearest',
            intersect: true,
            axis: 'r'
          }
        }
      });
    }

    // 获取知识点掌握程度对应的文字描述
    const getKnowledgeLevelText = (percentage: number): string => {
      if (percentage >= 90) return '精通';
      if (percentage >= 75) return '熟练';
      if (percentage >= 60) return '掌握';
      if (percentage >= 40) return '了解';
      return '薄弱';
    };

    // 学习时间分布图表
    const timeDistributionCtx = document.getElementById('timeDistributionChart') as HTMLCanvasElement;
    if (timeDistributionCtx) {
      const timeDistributionChart = new Chart(timeDistributionCtx, {
        type: 'doughnut',
        data: {
          labels: ['基础题', '编程题', '模拟考试', '学习资料'],
          datasets: [{
            data: [45, 30, 15, 10],
            backgroundColor: [
              'rgba(79, 70, 229, 0.8)',
              'rgba(16, 185, 129, 0.8)',
              'rgba(245, 158, 11, 0.8)',
              'rgba(239, 68, 68, 0.8)'
            ],
            borderColor: 'transparent',
            borderWidth: 0
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          cutout: '70%',
          plugins: {
            legend: {
              position: 'right',
              labels: {
                boxWidth: 15,
                padding: 15
              }
            }
          }
        }
      });
    }

    // 难度解题分布图表
    const difficultyDistributionCtx = document.getElementById('difficultyDistributionChart') as HTMLCanvasElement;
    if (difficultyDistributionCtx) {
      const difficultyDistributionChart = new Chart(difficultyDistributionCtx, {
        type: 'pie',
        data: {
          labels: ['简单', '中等', '困难'],
          datasets: [{
            data: progress ? [progress.easyCount, progress.mediumCount, progress.hardCount] : [50, 30, 15],
            backgroundColor: [
              'rgba(16, 185, 129, 0.8)',
              'rgba(245, 158, 11, 0.8)',
              'rgba(239, 68, 68, 0.8)'
            ],
            borderColor: 'transparent',
            borderWidth: 0
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'right',
              labels: {
                boxWidth: 15,
                padding: 15
              }
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  const label = context.label || '';
                  const value = context.raw;
                  const total = context.chart.data.datasets[0].data.reduce((a, b) => (a as number) + (b as number), 0);
                  const percentage = Math.round((value as number) / (total as number) * 100);

                  // 添加总量信息
                  let totalText = '';
                  if (progress) {
                    if (label === '简单') {
                      totalText = `${progress.easyCount}/${progress.easyTotal}`;
                    } else if (label === '中等') {
                      totalText = `${progress.mediumCount}/${progress.mediumTotal}`;
                    } else if (label === '困难') {
                      totalText = `${progress.hardCount}/${progress.hardTotal}`;
                    }
                  }

                  return `${label}: ${totalText} (${percentage}%)`;
                }
              }
            }
          }
        }
      });
    }

    return () => {
      if (!knowledgeLoading && knowledgeProgress) {
        if (basicKnowledgeCtx) {
          const chartInstance = Chart.getChart(basicKnowledgeCtx);
          if (chartInstance) chartInstance.destroy();
        }
        if (codingKnowledgeCtx) {
          const chartInstance = Chart.getChart(codingKnowledgeCtx);
          if (chartInstance) chartInstance.destroy();
        }
      }

      if (timeDistributionCtx) {
        const chartInstance = Chart.getChart(timeDistributionCtx);
        if (chartInstance) chartInstance.destroy();
      }
      if (difficultyDistributionCtx) {
        const chartInstance = Chart.getChart(difficultyDistributionCtx);
        if (chartInstance) chartInstance.destroy();
      }
    };
  }, [knowledgeLoading, knowledgeProgress, progress]);

  // 生成个性化题单
  const generatePersonalizedProblemList = async () => {
    setIsGeneratingProblemList(true);

    // 调用生成题单接口
    await problemListService.generateProblemList();

    // 轮询获取题单生成状态
    const pollInterval = setInterval(async () => {
      try {
        const response = await problemListService.getProblemListGenerateStatus();
        if (response.data === null) {
          clearInterval(pollInterval);
          setIsGeneratingProblemList(false);
        }
        // 如果返回值大于0，表示题单已生成，返回的是题单ID
        if (response.data > 0) {
          clearInterval(pollInterval);
          setProblemListId(response.data);
          setIsGeneratingProblemList(false);

          // 生成成功后跳转到题单详情页
          router.push(`/problem-lists/${response.data}`);
        }
      } catch (error) {
        console.error('获取题单生成状态失败:', error);
        clearInterval(pollInterval);
        setIsGeneratingProblemList(false);
      }
    }, 2000); // 每2秒轮询一次
  };

  return (
    <div className="bg-gray-50 min-h-screen py-8">
      <div className="container mx-auto px-4">
        {/* 顶部标题和时间筛选 */}
        <div className="mb-8 flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">学习进度分析</h1>
            <p className="text-gray-600 mt-1">追踪您的学习轨迹，分析成长曲线</p>
          </div>
        </div>

        {/* 提交热力图 */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
          <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100 flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-800">提交热力图</h3>
            <div className="flex items-center">
              <FontAwesomeIcon icon={faCalendarAlt} className="text-indigo-500 mr-2" />
              <CommonDropdown
                label="年份"
                options={availableYears.map(year => ({
                  value: year,
                  label: `${year}年`
                }))}
                value={selectedYear}
                onChange={(value) => setSelectedYear(value as number)}
                className="w-28 h-8 text-sm"
              />
            </div>
          </div>
          <div className="p-6">
            {heatmapLoading ? (
              <div className="animate-pulse flex flex-col space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                <div className="h-48 bg-gray-200 rounded"></div>
              </div>
            ) : error ? (
              <div className="bg-red-50 p-4 rounded-lg text-red-600">
                <p>{error}</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <div id="heatmap-container" className="py-2 min-w-max"></div>
              </div>
            )}
            <div className="mt-6 pt-6 border-t border-gray-100 flex items-start space-x-2">
              <FontAwesomeIcon icon={faFire} className="text-orange-500 mt-0.5" />
              <p className="text-sm text-gray-600">
                连续提交可以增强学习习惯，保持高效学习状态。今年您已经累计提交了
                <span className="text-indigo-600 font-medium mx-1">
                  {Object.values(heatmapData).reduce((total, count) => total + count, 0)}
                </span>
                次，最高纪录是
                <span className="text-indigo-600 font-medium mx-1">
                  {Math.max(...Object.values(heatmapData), 0)}
                </span>
                次/天。
              </p>
            </div>
          </div>
        </div>

        {/* 解题进度和每日学习记录 */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          {loading ? (
            <>
              <ProgressSkeleton />
              <ProgressSkeleton />
            </>
          ) : error ? (
            <div className="col-span-2 bg-red-50 p-4 rounded-lg text-red-600">
              <p>{error}</p>
            </div>
          ) : (
            <>
              <div className="bg-white rounded-xl shadow-md overflow-hidden">
                <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100">
                  <h3 className="text-lg font-semibold text-gray-800">解题进度</h3>
                </div>
                <div className="p-6">
                  {progress && (
                    <>
                      <div className="mb-6">
                        <div className="flex justify-between items-center mb-2">
                          <div>
                            <span className="text-gray-700 font-medium">基础题库</span>
                            <span className="text-gray-500 text-sm ml-2">{progress.basicProblemAcceptCount}/{progress.basicProblemTotal}题</span>
                          </div>
                          <span className="text-indigo-600 font-medium">
                            {Math.round((progress.basicProblemAcceptCount / progress.basicProblemTotal) * 100)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className="bg-indigo-600 h-2.5 rounded-full"
                            style={{ width: `${Math.round((progress.basicProblemAcceptCount / progress.basicProblemTotal) * 100)}%` }}
                          ></div>
                        </div>
                      </div>
                      <div className="mb-6">
                        <div className="flex justify-between items-center mb-2">
                          <div>
                            <span className="text-gray-700 font-medium">编程题库</span>
                            <span className="text-gray-500 text-sm ml-2">{progress.codingProblemAcceptCount}/{progress.codingProblemTotal}题</span>
                          </div>
                          <span className="text-indigo-600 font-medium">
                            {Math.round((progress.codingProblemAcceptCount / progress.codingProblemTotal) * 100)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className="bg-indigo-600 h-2.5 rounded-full"
                            style={{ width: `${Math.round((progress.codingProblemAcceptCount / progress.codingProblemTotal) * 100)}%` }}
                          ></div>
                        </div>
                      </div>
                      <div className="grid grid-cols-3 gap-4 mb-6">
                        <div className="bg-gray-50 rounded-lg p-4">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-indigo-600">{progress.easyCount}</div>
                            <div className="text-xs text-gray-500">简单难度</div>
                          </div>
                          <div className="flex justify-between items-center text-xs text-gray-500 mt-1 mb-1">
                            <span>{progress.easyCount}/{progress.easyTotal}</span>
                            <span>{Math.round((progress.easyCount / progress.easyTotal) * 100)}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-1.5 mt-2">
                            <div
                              className="bg-green-500 h-1.5 rounded-full"
                              style={{ width: `${Math.round((progress.easyCount / progress.easyTotal) * 100)}%` }}
                            ></div>
                          </div>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-4">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-indigo-600">{progress.mediumCount}</div>
                            <div className="text-xs text-gray-500">中等难度</div>
                          </div>
                          <div className="flex justify-between items-center text-xs text-gray-500 mt-1 mb-1">
                            <span>{progress.mediumCount}/{progress.mediumTotal}</span>
                            <span>{Math.round((progress.mediumCount / progress.mediumTotal) * 100)}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-1.5 mt-2">
                            <div
                              className="bg-yellow-500 h-1.5 rounded-full"
                              style={{ width: `${Math.round((progress.mediumCount / progress.mediumTotal) * 100)}%` }}
                            ></div>
                          </div>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-4">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-indigo-600">{progress.hardCount}</div>
                            <div className="text-xs text-gray-500">困难难度</div>
                          </div>
                          <div className="flex justify-between items-center text-xs text-gray-500 mt-1 mb-1">
                            <span>{progress.hardCount}/{progress.hardTotal}</span>
                            <span>{Math.round((progress.hardCount / progress.hardTotal) * 100)}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-1.5 mt-2">
                            <div
                              className="bg-red-500 h-1.5 rounded-full"
                              style={{ width: `${Math.round((progress.hardCount / progress.hardTotal) * 100)}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                  {/* <div className="text-center">
                    <button className="px-4 py-2 bg-indigo-100 text-indigo-700 rounded-lg text-sm font-medium hover:bg-indigo-200 transition">查看详细分类数据</button>
                  </div> */}
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-md overflow-hidden">
                <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100">
                  <h3 className="text-lg font-semibold text-gray-800">每日学习记录</h3>
                </div>
                <div className="p-6">
                  <div className="h-64">
                    <canvas id="dailyRecordChart"></canvas>
                  </div>
                  {studyStat && (
                    <div className="grid grid-cols-3 gap-4 mt-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-indigo-600">
                          {studyStat.solveProblemCounts.reduce((sum, count) => sum + count, 0)}
                        </div>
                        <div className="text-xs text-gray-500">本周解题</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-indigo-600">
                          {calculateMaxConsecutiveDays(studyStat.solveProblemCounts)}
                        </div>
                        <div className="text-xs text-gray-500">连续打卡</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-indigo-600">
                          {(studyStat.solveProblemCounts.reduce((sum, count) => sum + count, 0) / 7).toFixed(1)}
                        </div>
                        <div className="text-xs text-gray-500">日均解题</div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}
        </div>

        {/* 知识点掌握情况和学习时间分布 */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          {knowledgeLoading ? (
            <>
              <KnowledgeChartSkeleton />
              <KnowledgeChartSkeleton />
            </>
          ) : error ? (
            <div className="col-span-2 bg-red-50 p-4 rounded-lg text-red-600">
              <p>{error}</p>
            </div>
          ) : (
            <>
              <div className="bg-white rounded-xl shadow-md overflow-hidden">
                <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100">
                  <h3 className="text-lg font-semibold text-gray-800">基础题知识点掌握情况</h3>
                </div>
                <div className="p-6">
                  <div className="h-64 flex items-center justify-center">
                    <div className="w-64 h-64">
                      <canvas id="basicKnowledgeChart"></canvas>
                    </div>
                  </div>
                  <div className="mt-6 pt-6 border-t border-gray-100">
                    <h4 className="text-sm font-semibold text-gray-700 mb-3">基础题学习建议</h4>
                    <div className="flex items-start space-x-2">
                      <FontAwesomeIcon icon={faInfoCircle} className="text-indigo-500 mt-0.5" />
                      <p className="text-sm text-gray-600">
                        {knowledgeProgress && knowledgeProgress.basicProblem.length > 0 ? (
                          (() => {
                            const weakPoints = knowledgeProgress.basicProblem
                              .sort((a, b) => a.percentage - b.percentage)
                              .slice(0, 2);

                            return (
                              <>
                                根据您目前的学习情况，建议加强
                                {weakPoints.length > 0 && (
                                  <span className="text-indigo-600 font-medium">{weakPoints[0].label}</span>
                                )}
                                {weakPoints.length > 1 && (
                                  <>
                                    和<span className="text-indigo-600 font-medium">{weakPoints[1].label}</span>
                                  </>
                                )}
                                的学习，这些是您当前的短板。
                              </>
                            );
                          })()
                        ) : '暂无学习建议'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-md overflow-hidden">
                <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100">
                  <h3 className="text-lg font-semibold text-gray-800">编程题知识点掌握情况</h3>
                </div>
                <div className="p-6">
                  <div className="h-64 flex items-center justify-center">
                    <div className="w-64 h-64">
                      <canvas id="codingKnowledgeChart"></canvas>
                    </div>
                  </div>
                  <div className="mt-6 pt-6 border-t border-gray-100">
                    <h4 className="text-sm font-semibold text-gray-700 mb-3">编程题学习建议</h4>
                    <div className="flex items-start space-x-2">
                      <FontAwesomeIcon icon={faInfoCircle} className="text-indigo-500 mt-0.5" />
                      <p className="text-sm text-gray-600">
                        {knowledgeProgress && knowledgeProgress.codingProblem.length > 0 ? (
                          (() => {
                            const weakPoints = knowledgeProgress.codingProblem
                              .sort((a, b) => a.percentage - b.percentage)
                              .slice(0, 2);

                            return (
                              <>
                                根据您目前的学习情况，建议加强
                                {weakPoints.length > 0 && (
                                  <span className="text-indigo-600 font-medium">{weakPoints[0].label}</span>
                                )}
                                {weakPoints.length > 1 && (
                                  <>
                                    和<span className="text-indigo-600 font-medium">{weakPoints[1].label}</span>
                                  </>
                                )}
                                的学习，这些是您当前的短板。
                              </>
                            );
                          })()
                        ) : '暂无学习建议'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* 最近错题分析 */}
        {loading ? (
          <ChartSkeleton />
        ) : error ? (
          <div className="bg-red-50 p-4 rounded-lg text-red-600 mb-8">
            <p>{error}</p>
          </div>
        ) : (
          <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
            <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold text-gray-800">最近错题分析</h3>
                <Link href="/wrong-problems" className="text-indigo-600 hover:text-indigo-800 text-sm">查看全部</Link>
              </div>
            </div>
            <div className="p-6">
              <div className="divide-y divide-gray-200">
                <div className="p-4 hover:bg-gray-50">
                  <div className="flex justify-between items-start">
                    <div className="w-2/3">
                      <h4 className="text-md font-medium text-gray-800 line-clamp-1">二叉树的层序遍历</h4>
                      <p className="text-sm text-gray-600 mt-1">
                        树 · 中等 · #1245
                      </p>
                    </div>
                    <span className="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                      做错3次
                    </span>
                  </div>
                  <div className="mt-3 flex justify-between items-center">
                    <div className="text-xs text-gray-500">
                      <FontAwesomeIcon icon={faCalendarCheck} className="mr-1" />上次尝试: 2023-12-05
                    </div>
                    <Link
                      href="/coding-problems/1245"
                      className="text-indigo-600 hover:text-indigo-800 text-sm font-medium"
                    >
                      再次挑战
                    </Link>
                  </div>
                </div>

                <div className="p-4 hover:bg-gray-50">
                  <div className="flex justify-between items-start">
                    <div className="w-2/3">
                      <h4 className="text-md font-medium text-gray-800 line-clamp-1">最长递增子序列</h4>
                      <p className="text-sm text-gray-600 mt-1">
                        动态规划 · 困难 · #985
                      </p>
                    </div>
                    <span className="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                      做错2次
                    </span>
                  </div>
                  <div className="mt-3 flex justify-between items-center">
                    <div className="text-xs text-gray-500">
                      <FontAwesomeIcon icon={faCalendarCheck} className="mr-1" />上次尝试: 2023-11-28
                    </div>
                    <Link
                      href="/coding-problems/985"
                      className="text-indigo-600 hover:text-indigo-800 text-sm font-medium"
                    >
                      再次挑战
                    </Link>
                  </div>
                </div>

                <div className="p-4 hover:bg-gray-50">
                  <div className="flex justify-between items-start">
                    <div className="w-2/3">
                      <h4 className="text-md font-medium text-gray-800 line-clamp-1">字符串匹配算法</h4>
                      <p className="text-sm text-gray-600 mt-1">
                        字符串 · 中等 · #1156
                      </p>
                    </div>
                    <span className="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                      做错2次
                    </span>
                  </div>
                  <div className="mt-3 flex justify-between items-center">
                    <div className="text-xs text-gray-500">
                      <FontAwesomeIcon icon={faCalendarCheck} className="mr-1" />上次尝试: 2023-11-15
                    </div>
                    <Link
                      href="/coding-problems/1156"
                      className="text-indigo-600 hover:text-indigo-800 text-sm font-medium"
                    >
                      再次挑战
                    </Link>
                  </div>
                </div>
              </div>

              <div className="mt-6 pt-6 border-t border-gray-100">
                <h4 className="text-sm font-semibold text-gray-700 mb-3">个性化练习题单</h4>
                <div className="flex items-start space-x-2">
                  <FontAwesomeIcon icon={faInfoCircle} className="text-indigo-500 mt-0.5" />
                  <p className="text-sm text-gray-600">
                    系统可以根据您的错题情况，智能为您生成一份个性化练习题单，
                    涵盖您的薄弱知识点和易错题型，帮助您有针对性地提升。
                  </p>
                </div>
                <div className="mt-4 text-center">
                  <button
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition ${isGeneratingProblemList
                        ? 'bg-gray-300 text-gray-600 cursor-not-allowed'
                        : 'bg-indigo-600 text-white hover:bg-indigo-700'
                      }`}
                    onClick={generatePersonalizedProblemList}
                    disabled={isGeneratingProblemList}
                  >
                    {isGeneratingProblemList ? (
                      <>
                        <FontAwesomeIcon icon={faSpinner} className="animate-spin mr-2" />
                        正在生成题单...
                      </>
                    ) : (
                      '生成个性化题单'
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
