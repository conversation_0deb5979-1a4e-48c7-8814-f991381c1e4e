import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer, FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from 'redux-persist';
import storage from 'redux-persist/lib/storage';

// 导入 reducers
import authReducer from './features/authSlice';
import notificationReducer from './features/notificationSlice';

// 配置持久化存储
const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth', 'notification'], // 持久化auth和notification状态
};

// 合并所有reducers
const rootReducer = combineReducers({
  auth: authReducer,
  notification: notificationReducer,
});

// 创建持久化reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// 创建store
const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // 忽略redux-persist的action类型
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }),
});

// 创建持久化store
export const persistor = persistStore(store);

// 导出store
export { store };

// 导出类型
export type AppDispatch = typeof store.dispatch;
export type RootState = ReturnType<typeof store.getState>; 