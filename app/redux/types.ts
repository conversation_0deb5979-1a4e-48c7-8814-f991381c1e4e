// 用户信息类型
export interface UserInfo {
  id: number;
  username: string;
  nickname?: string;
  avatar?: string;
  email?: string;
  role?: string;
  name?: string;
  sex?: string;
  age?: string;
  currentMembershipLevel?: number;
  membershipExpireTime?: string;
  mobile?: string;
  hasBindMobile?: boolean;
  hasBindEmail?: boolean;
  hasBindWeChat?: boolean;
  // 其他用户信息字段
}

// 认证状态类型
export interface AuthState {
  isAuthenticated: boolean;
  user: UserInfo | null;
  loading: boolean;
  error: string | null;
}

// 通知类型
export interface Notification {
  id: number;
  type: 'system' | 'contest' | 'problem' | 'interaction' | 'vip';
  title: string;
  content: string;
  isRead: boolean;
  date: string;
  link?: string;
}

// 通知状态类型
export interface NotificationState {
  // 页面使用的通知列表
  pageNotifications: Notification[];
  // 页面通知总数
  totalItems: number;
  // 页面当前页码
  currentPage: number;
  // 页面每页显示数量
  pageSize: number;
  
  // header使用的通知列表 (永远保持最新5条)
  headerNotifications: Notification[];
  
  // 加载状态
  loading: boolean;
  headerLoading: boolean;
  // 错误信息
  error: string | null;
  headerError: string | null;
}

// Store 相关类型
export type RootState = {
  auth: AuthState;
  notification: NotificationState;
}; 