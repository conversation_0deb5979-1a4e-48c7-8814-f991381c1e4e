import {createSlice, createAsyncThunk, PayloadAction} from '@reduxjs/toolkit';
import UserService from "@/app/service/user-service";
import { UserInfo, AuthState, RootState } from '../../redux/types';
import { clearAuth, setToken } from '@/app/utils/auth';

// 初始状态
const initialState: AuthState = {
	isAuthenticated: false,
	user: null,
	loading: false,
	error: null
};

// 创建异步 thunk action
export const login = createAsyncThunk(
	'auth/login',
	async (credentials: { username: string; password: string; loginType: number }, { rejectWithValue }) => {
		try {
			const response = await UserService.login(credentials);
			if (response.data?.token) {
				setToken(response.data.token);
			}
			return response.data;
		} catch (error: any) {
			return rejectWithValue(error.msg || '登录失败');
		}
	}
);

export const logout = createAsyncThunk(
	'auth/logout',
	async (_, { rejectWithValue }) => {
		try {
			await UserService.logout();
			clearAuth();
			return null;
		} catch (error: any) {
			return rejectWithValue(error.response?.data?.message || '登出失败');
		}
	}
);

export const fetchUserInfo = createAsyncThunk(
	'auth/fetchUserInfo',
	async (_, { rejectWithValue }) => {
		try {
			const response = await UserService.login({});
			return response.data;
		} catch (error: any) {
			return rejectWithValue(error.response?.data?.message || '获取用户信息失败');
		}
	}
);

// 创建 slice
const authSlice = createSlice({
	name: 'auth',
	initialState,
	reducers: {
		clearError: (state) => {
			state.error = null;
		},
		setUser: (state, action: PayloadAction<UserInfo>) => {
			state.user = action.payload;
			state.isAuthenticated = true;
		},
		clearUser: (state) => {
			state.user = null;
			state.isAuthenticated = false;
		}
	},
	extraReducers: (builder) => {
		builder
			// 登录
			.addCase(login.pending, (state) => {
				state.loading = true;
				state.error = null;
			})
			.addCase(login.fulfilled, (state, action) => {
				state.loading = false;
				state.isAuthenticated = true;
				state.user = action.payload;
			})
			.addCase(login.rejected, (state, action) => {
				state.loading = false;
				state.error = action.payload as string;
			})
			// 登出
			.addCase(logout.pending, (state) => {
				state.loading = true;
			})
			.addCase(logout.fulfilled, (state) => {
				state.loading = false;
				state.isAuthenticated = false;
				state.user = null;
			})
			.addCase(logout.rejected, (state, action) => {
				state.loading = false;
				state.error = action.payload as string;
			})
			// 获取用户信息
			.addCase(fetchUserInfo.pending, (state) => {
				state.loading = true;
				state.error = null;
			})
			.addCase(fetchUserInfo.fulfilled, (state, action) => {
				state.loading = false;
				state.isAuthenticated = true;
				state.user = action.payload;
			})
			.addCase(fetchUserInfo.rejected, (state, action) => {
				state.loading = false;
				state.error = action.payload as string;
				state.isAuthenticated = false;
				state.user = null;
			});
	}
});

export const { clearError, setUser, clearUser } = authSlice.actions;

// 导出选择器
export const selectAuth = (state: RootState) => state.auth;
export const selectIsAuthenticated = (state: RootState) => state.auth.isAuthenticated;
export const selectUser = (state: RootState) => state.auth.user;
export const selectAuthLoading = (state: RootState) => state.auth.loading;
export const selectAuthError = (state: RootState) => state.auth.error;

// 重新导出类型
export type { UserInfo, AuthState };

export default authSlice.reducer;
