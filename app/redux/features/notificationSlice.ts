import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import notificationService, { Notification as ApiNotification } from '@/app/service/notification-service';
import { PageResult } from '@/app/model/models';
import type { NotificationState, Notification } from '../types';

// 初始状态
const initialState: NotificationState = {
  pageNotifications: [],
  totalItems: 0,
  currentPage: 1,
  pageSize: 10,
  headerNotifications: [],
  loading: false,
  headerLoading: false,
  error: null,
  headerError: null
};

// API类型到应用类型的转换函数
const convertApiNotification = (apiNotification: ApiNotification): Notification => {
  // 转换API类型为内部使用的类型
  let type: 'system' | 'contest' | 'problem' | 'interaction' | 'vip';
  
  // 根据API类型设置对应的内部类型
  switch (apiNotification.type) {
    case 1:
      type = 'system';
      break;
    case 2:
      type = 'vip';
      break;
    case 3:
      type = 'contest';
      break;
    default:
      type = 'system';
  }
  
  return {
    id: apiNotification.id,
    type,
    title: apiNotification.title,
    content: apiNotification.content,
    isRead: apiNotification.isRead === 1,
    date: apiNotification.createdTime,
    link: apiNotification.link || undefined
  };
};

// 异步获取通知列表 (用于页面)
export const fetchNotifications = createAsyncThunk(
  'notification/fetchNotifications',
  async ({ pageNum, pageSize, type, isRead }: { pageNum: number; pageSize: number; type?: number; isRead?: number }, { rejectWithValue }) => {
    try {
      const response = await notificationService.getNotifications({ pageNum, pageSize, type, isRead });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.message || '获取通知列表失败');
    }
  }
);

// 异步获取通知列表 (用于header)
export const fetchHeaderNotifications = createAsyncThunk(
  'notification/fetchHeaderNotifications',
  async (_, { rejectWithValue }) => {
    try {
      // header固定获取最新5条通知
      const response = await notificationService.getNotifications({ pageNum: 1, pageSize: 5 });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.message || '获取通知列表失败');
    }
  }
);

// 异步标记通知为已读
export const markNotificationAsRead = createAsyncThunk(
  'notification/markAsRead',
  async (id: number, { rejectWithValue, dispatch }) => {
    try {
      await notificationService.read(id);
      // 标记成功后，重新获取header通知列表
      dispatch(fetchHeaderNotifications());
      return id;
    } catch (error: any) {
      return rejectWithValue(error.message || '标记通知已读失败');
    }
  }
);

// 异步标记所有通知为已读
export const markAllNotificationsAsRead = createAsyncThunk(
  'notification/markAllAsRead',
  async (_, { rejectWithValue, dispatch }) => {
    try {
      await notificationService.readAll();
      // 标记已读后刷新头部通知
      dispatch(fetchHeaderNotifications());
      return true;
    } catch (error: any) {
      return rejectWithValue(error.message || '标记全部已读失败');
    }
  }
);

// 异步清空所有通知
export const clearNotifications = createAsyncThunk(
  'notification/clearNotifications',
  async (_, { rejectWithValue, dispatch }) => {
    try {
      await notificationService.clearAll();
      // 清空后刷新头部通知
      dispatch(fetchHeaderNotifications());
      return true;
    } catch (error: any) {
      return rejectWithValue(error.message || '清空通知失败');
    }
  }
);

// 异步获取通知详情
export const fetchNotificationDetail = createAsyncThunk(
  'notification/fetchDetail',
  async (id: number, { rejectWithValue }) => {
    try {
      const response = await notificationService.getNotificationDetail(id);
      return convertApiNotification(response.data);
    } catch (error: any) {
      return rejectWithValue(error.message || '获取通知详情失败');
    }
  }
);

// 创建slice
const notificationSlice = createSlice({
  name: 'notification',
  initialState,
  reducers: {
    setPageNotifications: (state, action: PayloadAction<NotificationState['pageNotifications']>) => {
      state.pageNotifications = action.payload;
    },
    setTotalItems: (state, action: PayloadAction<number>) => {
      state.totalItems = action.payload;
    },
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
    },
    setHeaderNotifications: (state, action: PayloadAction<NotificationState['headerNotifications']>) => {
      state.headerNotifications = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setHeaderLoading: (state, action: PayloadAction<boolean>) => {
      state.headerLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setHeaderError: (state, action: PayloadAction<string | null>) => {
      state.headerError = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      // 获取页面通知列表
      .addCase(fetchNotifications.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchNotifications.fulfilled, (state, action) => {
        state.loading = false;
        const data = action.payload as PageResult<ApiNotification>;
        state.pageNotifications = data.items.map(convertApiNotification);
        state.totalItems = data.total;
      })
      .addCase(fetchNotifications.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 获取header通知列表
      .addCase(fetchHeaderNotifications.pending, (state) => {
        state.headerLoading = true;
        state.headerError = null;
      })
      .addCase(fetchHeaderNotifications.fulfilled, (state, action) => {
        state.headerLoading = false;
        const data = action.payload as PageResult<ApiNotification>;
        state.headerNotifications = data.items.map(convertApiNotification);
      })
      .addCase(fetchHeaderNotifications.rejected, (state, action) => {
        state.headerLoading = false;
        state.headerError = action.payload as string;
      })
      // 标记通知已读
      .addCase(markNotificationAsRead.pending, (state) => {
        state.loading = true;
      })
      .addCase(markNotificationAsRead.fulfilled, (state, action) => {
        state.loading = false;
        // 更新页面通知列表中的已读状态
        state.pageNotifications = state.pageNotifications.map(notification =>
          notification.id === action.payload
            ? { ...notification, isRead: true }
            : notification
        );
        // 更新header通知列表中的已读状态
        state.headerNotifications = state.headerNotifications.map(notification =>
          notification.id === action.payload
            ? { ...notification, isRead: true }
            : notification
        );
      })
      .addCase(markNotificationAsRead.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 标记所有通知为已读
      .addCase(markAllNotificationsAsRead.fulfilled, (state) => {
        // 更新页面通知列表
        state.pageNotifications.forEach(notification => {
          notification.isRead = true;
        });
        
        // 更新header通知列表
        state.headerNotifications.forEach(notification => {
          notification.isRead = true;
        });
      })
      // 清空所有通知
      .addCase(clearNotifications.pending, (state) => {
        state.loading = true;
      })
      .addCase(clearNotifications.fulfilled, (state) => {
        state.loading = false;
        state.pageNotifications = [];
        state.headerNotifications = [];
        state.totalItems = 0;
      })
      .addCase(clearNotifications.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 获取通知详情
      .addCase(fetchNotificationDetail.fulfilled, (state, action) => {
        // 更新到页面通知列表
        const pageNotificationIndex = state.pageNotifications.findIndex(n => n.id === action.payload.id);
        if (pageNotificationIndex === -1) {
          state.pageNotifications.push(action.payload);
        } else {
          state.pageNotifications[pageNotificationIndex] = action.payload;
        }
        
        // 更新到header通知列表
        const headerNotificationIndex = state.headerNotifications.findIndex(n => n.id === action.payload.id);
        if (headerNotificationIndex !== -1) {
          state.headerNotifications[headerNotificationIndex] = action.payload;
        }
      });
  }
});

export const {
  setPageNotifications,
  setTotalItems,
  setCurrentPage,
  setPageSize,
  setHeaderNotifications,
  setLoading,
  setHeaderLoading,
  setError,
  setHeaderError
} = notificationSlice.actions;

// 导出选择器
export const selectPageNotifications = (state: { notification: NotificationState }) => state.notification?.pageNotifications || [];
export const selectTotalItems = (state: { notification: NotificationState }) => state.notification?.totalItems || 0;
export const selectCurrentPage = (state: { notification: NotificationState }) => state.notification?.currentPage || 1;
export const selectPageSize = (state: { notification: NotificationState }) => state.notification?.pageSize || 10;
export const selectIsLoading = (state: { notification: NotificationState }) => state.notification?.loading || false;
export const selectError = (state: { notification: NotificationState }) => state.notification?.error || null;

export const selectHeaderNotifications = (state: { notification: NotificationState }) => state.notification?.headerNotifications || [];
export const selectHeaderIsLoading = (state: { notification: NotificationState }) => state.notification?.headerLoading || false;
export const selectHeaderError = (state: { notification: NotificationState }) => state.notification?.headerError || null;
export const selectHeaderUnreadCount = (state: { notification: NotificationState }) => 
  state.notification?.headerNotifications?.filter((n: Notification) => !n.isRead)?.length || 0;

export const selectAllNotifications = (state: { notification: NotificationState }) => state.notification?.pageNotifications || [];
export const selectUnreadCount = (state: { notification: NotificationState }) => 
  state.notification?.headerNotifications?.filter((n: Notification) => !n.isRead)?.length || 0;

export default notificationSlice.reducer; 