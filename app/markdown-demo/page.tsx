'use client';

import React, { useState } from 'react';
import Mark<PERSON><PERSON>ender<PERSON> from '../components/MarkdownRenderer';

const initialMarkdown = `# Markdown与LaTeX演示

## 基础Markdown格式

### 文本格式化

这是**粗体文本**，这是*斜体文本*，这是~~删除线文本~~。

### 列表

无序列表:

* 项目1
* 项目2
  * 子项目2.1
  * 子项目2.2
* 项目3

有序列表:

1. 第一步
2. 第二步
3. 第三步

### 链接和图片

[这是一个链接](https://www.example.com)

![这是一个图片占位符](https://via.placeholder.com/150)

### 引用

> 这是一段引用文本。
> 
> 引用可以有多个段落。

### 代码

行内代码: \`const x = 10;\`

代码块:

\`\`\`javascript
function hello() {
  console.log("Hello, world!");
}
\`\`\`

### 表格

| 名称 | 年龄 | 职业 |
|------|-----|------|
| 张三 | 25  | 工程师 |
| 李四 | 30  | 设计师 |
| 王五 | 28  | 教师 |

## LaTeX公式演示

### 行内公式

爱因斯坦质能方程: $E=mc^2$

欧拉公式: $e^{i\\pi} + 1 = 0$

### 块级公式

牛顿第二定律:

$$F = m \\cdot a$$

高斯公式:

$$\\oint_{\\partial \\Omega} \\mathbf{E} \\cdot d\\mathbf{S} = \\frac{1}{\\varepsilon_0} \\int_{\\Omega} \\rho \\, dV$$

麦克斯韦方程组:

$$
\\begin{align}
\\nabla \\cdot \\vec{E} &= \\frac{\\rho}{\\varepsilon_0} \\\\
\\nabla \\cdot \\vec{B} &= 0 \\\\
\\nabla \\times \\vec{E} &= -\\frac{\\partial\\vec{B}}{\\partial t} \\\\
\\nabla \\times \\vec{B} &= \\mu_0 \\vec{J} + \\mu_0\\varepsilon_0\\frac{\\partial\\vec{E}}{\\partial t}
\\end{align}
$$

### 数学符号和公式

求和公式:

$$\\sum_{i=1}^{n} i = \\frac{n(n+1)}{2}$$

积分公式:

$$\\int_{a}^{b} f(x) \\, dx$$

极限:

$$\\lim_{x \\to \\infty} \\frac{1}{x} = 0$$

矩阵:

$$
\\begin{pmatrix}
a & b & c \\\\
d & e & f \\\\
g & h & i
\\end{pmatrix}
$$

### 化学方程式

水的电解:

$$2H_2O \\xrightarrow{\\text{电流}} 2H_2 + O_2$$

## 组合使用

在Markdown中嵌入LaTeX公式是非常有用的，特别是在编写:

1. 科学论文
2. 数学教程
3. 物理解释

例如，我们可以解释二次方程 $ax^2 + bx + c = 0$ 的解为:

$$x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}$$

$|a|,|b| \\le {10}^9$

`;

export default function MarkdownDemoPage() {
  const [markdown, setMarkdown] = useState(initialMarkdown);

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Markdown与LaTeX渲染器演示</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 编辑区 */}
        <div className="border rounded-lg shadow-sm">
          <div className="bg-gray-100 px-4 py-2 border-b">
            <h2 className="font-semibold">编辑Markdown</h2>
          </div>
          <textarea
            className="w-full p-4 h-[600px] font-mono text-sm focus:outline-none"
            value={markdown}
            onChange={(e) => setMarkdown(e.target.value)}
          />
        </div>
        
        {/* 预览区 */}
        <div className="border rounded-lg shadow-sm">
          <div className="bg-gray-100 px-4 py-2 border-b">
            <h2 className="font-semibold">预览</h2>
          </div>
          <div className="p-4 prose max-w-none h-[600px] overflow-auto">
            <MarkdownRenderer content={markdown} />
          </div>
        </div>
      </div>
      
      <div className="mt-8 text-center text-gray-500 text-sm">
        <p>提示: 您可以编辑左侧的Markdown内容，右侧会实时预览渲染效果</p>
      </div>
    </div>
  );
} 