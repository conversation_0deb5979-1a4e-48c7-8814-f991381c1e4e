'use client';

import React, { useState } from 'react';
import Markdown<PERSON>enderer from '../components/MarkdownRenderer';

// 包含"int"关键字的测试Markdown内容
const initialMarkdown = `# 测试关键字渲染问题

## 基本编程关键字

- int (整型)
- return (返回)
- main (主函数)
- float (浮点型)
- double (双精度)

## 行内代码中的关键字

这是一个包含 \`int number = 10;\` 行内代码的句子。

函数返回值 \`return 0;\` 表示程序正常结束。

## 代码块中的关键字

\`\`\`c
int main() {
  int number = 42;
  float pi = 3.14;
  printf("Number: %d\\n", number);
  return 0;
}
\`\`\`

\`\`\`python
def calculate_sum(numbers):
    return sum(numbers)

result = calculate_sum([1, 2, 3, 4, 5])
print(f"Sum: {result}")
\`\`\`

## LaTeX公式中的积分

真正的数学积分符号：

$$\\int_{a}^{b} f(x) \\, dx$$

行内公式：$\\int_{0}^{\\infty} e^{-x} dx = 1$

## 表格中的关键字

| 数据类型 | 描述 | 示例 |
|---------|------|------|
| int | 整型 | int x = 5; |
| float | 浮点型 | float y = 3.14; |
| char | 字符型 | char c = 'A'; |
| return | 返回语句 | return value; |

## 普通文本中的混合内容

在C语言中，main函数通常返回int类型，使用return 0表示正常退出。

## LaTeX公式和关键字混合

$F = ma$ 是牛顿第二定律，其中 $a$ 是加速度，可以用 \`float a = 9.8;\` 来表示。

`;

export default function TestIntPage() {
  const [markdown, setMarkdown] = useState(initialMarkdown);

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">测试关键字渲染问题</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 编辑区 */}
        <div className="border rounded-lg shadow-sm">
          <div className="bg-gray-100 px-4 py-2 border-b">
            <h2 className="font-semibold">编辑Markdown</h2>
          </div>
          <textarea
            className="w-full p-4 h-[600px] font-mono text-sm focus:outline-none"
            value={markdown}
            onChange={(e) => setMarkdown(e.target.value)}
          />
        </div>
        
        {/* 预览区 */}
        <div className="border rounded-lg shadow-sm">
          <div className="bg-gray-100 px-4 py-2 border-b">
            <h2 className="font-semibold">预览</h2>
          </div>
          <div className="p-4 prose max-w-none h-[600px] overflow-auto">
            <MarkdownRenderer content={markdown} />
          </div>
        </div>
      </div>
    </div>
  );
} 