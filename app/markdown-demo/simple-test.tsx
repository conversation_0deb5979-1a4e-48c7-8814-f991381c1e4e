'use client';

import React from 'react';
import MarkdownRenderer from '../components/MarkdownRenderer';

// 简单的测试用例
const testMarkdown = `
# int 关键字测试

普通文本中的int关键字: int x = 5;

代码块中的int关键字:
\`\`\`c
int main() {
  return 0;
}
\`\`\`

行内代码中的int: \`int x = 10;\`

LaTeX公式中的积分符号: 
$$\\int_{0}^{1} x^2 dx$$
`;

export default function SimpleTestPage() {
  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-6">简单关键字渲染测试</h1>
      <div className="bg-white p-6 rounded-lg shadow-md">
        <MarkdownRenderer content={testMarkdown} />
      </div>
    </div>
  );
} 