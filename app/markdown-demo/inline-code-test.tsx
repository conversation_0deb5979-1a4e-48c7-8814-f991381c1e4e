'use client';

import React, { useState } from 'react';
import MarkdownRenderer from '../components/MarkdownRenderer';

// 行内代码测试用例
const initialMarkdown = `
# 行内代码显示测试

## 基本行内代码测试

这是一个普通句子中包含 \`行内代码\` 的例子，它应该正确地显示在行内。

下面是一个包含编程关键字的例子：这个变量声明 \`int number = 42;\` 应该显示在一行内，而不是单独占一行。

这里有多个行内代码 \`第一个代码\` 和 \`第二个代码\` 以及 \`int x = 10;\` 都应该显示在同一行内。

## 行内代码与其他元素混合

* 列表项中的 \`行内代码\` 应该正确显示
* 另一个带有 \`int function()\` 的项目

> 引用块中的 \`行内代码\` 也应该正确显示

## 行内代码与LaTeX公式比较

* 行内代码：\`int\` (应该显示为代码)
* 行内LaTeX：$\\int$ (应该显示为积分符号)

## 长行内代码测试

这是一个非常长的行内代码示例 \`function calculateSomethingComplicated(param1, param2, param3, param4) { return param1 + param2 * param3 / param4; }\` 它应该能够自动换行而不影响布局。
`;

export default function InlineCodeTest() {
  const [markdown, setMarkdown] = useState(initialMarkdown);

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">行内代码显示测试</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 编辑区 */}
        <div className="border rounded-lg shadow-sm">
          <div className="bg-gray-100 px-4 py-2 border-b">
            <h2 className="font-semibold">编辑Markdown</h2>
          </div>
          <textarea
            className="w-full p-4 h-[600px] font-mono text-sm focus:outline-none"
            value={markdown}
            onChange={(e) => setMarkdown(e.target.value)}
          />
        </div>
        
        {/* 预览区 */}
        <div className="border rounded-lg shadow-sm">
          <div className="bg-gray-100 px-4 py-2 border-b">
            <h2 className="font-semibold">预览</h2>
          </div>
          <div className="p-4 prose max-w-none h-[600px] overflow-auto">
            <MarkdownRenderer content={markdown} />
          </div>
        </div>
      </div>
    </div>
  );
} 