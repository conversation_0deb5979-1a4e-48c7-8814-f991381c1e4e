'use client';

import React, { useState } from 'react';
import MarkdownRenderer from '../components/MarkdownRenderer';

// 代码高亮测试用例
const initialMarkdown = `
# 代码高亮测试

## JavaScript/TypeScript

\`\`\`javascript
// JavaScript代码示例
function calculateTotal(items) {
  return items
    .filter(item => item.price > 0)
    .map(item => item.price * item.quantity)
    .reduce((total, price) => total + price, 0);
}

const cart = [
  { name: '苹果', price: 5, quantity: 2 },
  { name: '香蕉', price: 3, quantity: 5 }
];

console.log(\`总价: ¥\${calculateTotal(cart)}\`);
\`\`\`

\`\`\`typescript
// TypeScript代码示例
interface CartItem {
  name: string;
  price: number;
  quantity: number;
}

function calculateTotal(items: CartItem[]): number {
  return items
    .filter(item => item.price > 0)
    .map(item => item.price * item.quantity)
    .reduce((total, price) => total + price, 0);
}

const cart: CartItem[] = [
  { name: '苹果', price: 5, quantity: 2 },
  { name: '香蕉', price: 3, quantity: 5 }
];

console.log(\`总价: ¥\${calculateTotal(cart)}\`);
\`\`\`

## Python

\`\`\`python
# Python代码示例
def calculate_total(items):
    return sum(item['price'] * item['quantity'] for item in items if item['price'] > 0)

cart = [
    {'name': '苹果', 'price': 5, 'quantity': 2},
    {'name': '香蕉', 'price': 3, 'quantity': 5}
]

print(f"总价: ¥{calculate_total(cart)}")
\`\`\`

## C/C++

\`\`\`c
// C语言代码示例
#include <stdio.h>

int main() {
    int numbers[] = {1, 2, 3, 4, 5};
    int sum = 0;
    int count = sizeof(numbers) / sizeof(numbers[0]);
    
    for (int i = 0; i < count; i++) {
        sum += numbers[i];
    }
    
    printf("总和: %d\n", sum);
    return 0;
}
\`\`\`

\`\`\`cpp
// C++代码示例
#include <iostream>
#include <vector>
#include <numeric>

struct CartItem {
    std::string name;
    double price;
    int quantity;
};

double calculateTotal(const std::vector<CartItem>& items) {
    double total = 0.0;
    for (const auto& item : items) {
        if (item.price > 0) {
            total += item.price * item.quantity;
        }
    }
    return total;
}

int main() {
    std::vector<CartItem> cart = {
        {"苹果", 5.0, 2},
        {"香蕉", 3.0, 5}
    };
    
    std::cout << "总价: ¥" << calculateTotal(cart) << std::endl;
    return 0;
}
\`\`\`

## Java

\`\`\`java
// Java代码示例
import java.util.List;
import java.util.Arrays;

class CartItem {
    String name;
    double price;
    int quantity;
    
    CartItem(String name, double price, int quantity) {
        this.name = name;
        this.price = price;
        this.quantity = quantity;
    }
}

public class Main {
    public static double calculateTotal(List<CartItem> items) {
        return items.stream()
            .filter(item -> item.price > 0)
            .mapToDouble(item -> item.price * item.quantity)
            .sum();
    }
    
    public static void main(String[] args) {
        List<CartItem> cart = Arrays.asList(
            new CartItem("苹果", 5.0, 2),
            new CartItem("香蕉", 3.0, 5)
        );
        
        System.out.printf("总价: ¥%.2f%n", calculateTotal(cart));
    }
}
\`\`\`

## SQL

\`\`\`sql
-- SQL查询示例
SELECT 
    products.name,
    products.price,
    order_items.quantity,
    (products.price * order_items.quantity) as subtotal
FROM 
    products
JOIN 
    order_items ON products.id = order_items.product_id
WHERE 
    order_items.order_id = 12345
ORDER BY 
    subtotal DESC;
\`\`\`

## HTML/CSS

\`\`\`html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>购物车示例</title>
    <style>
        .cart-item {
            border-bottom: 1px solid #eee;
            padding: 10px 0;
            display: flex;
            justify-content: space-between;
        }
        .total {
            margin-top: 20px;
            font-weight: bold;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="cart">
        <div class="cart-item">
            <span>苹果 × 2</span>
            <span>¥10.00</span>
        </div>
        <div class="cart-item">
            <span>香蕉 × 5</span>
            <span>¥15.00</span>
        </div>
        <div class="total">
            总计: ¥25.00
        </div>
    </div>
</body>
</html>
\`\`\`

## 支持行内代码

这里有一些行内代码示例：\`const x = 10;\`、\`int main()\` 和 \`print("Hello")\`。

`;

export default function CodeHighlightTest() {
  const [markdown, setMarkdown] = useState(initialMarkdown);
  const [codeTheme, setCodeTheme] = useState<'dark' | 'light'>('dark');

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">代码高亮测试</h1>
      
      <div className="mb-4 flex items-center space-x-4">
        <span className="font-medium">代码主题:</span>
        <label className="inline-flex items-center">
          <input
            type="radio"
            className="form-radio"
            name="theme"
            value="dark"
            checked={codeTheme === 'dark'}
            onChange={() => setCodeTheme('dark')}
          />
          <span className="ml-2">深色</span>
        </label>
        <label className="inline-flex items-center">
          <input
            type="radio"
            className="form-radio"
            name="theme"
            value="light"
            checked={codeTheme === 'light'}
            onChange={() => setCodeTheme('light')}
          />
          <span className="ml-2">浅色</span>
        </label>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 编辑区 */}
        <div className="border rounded-lg shadow-sm">
          <div className="bg-gray-100 px-4 py-2 border-b">
            <h2 className="font-semibold">编辑Markdown</h2>
          </div>
          <textarea
            className="w-full p-4 h-[800px] font-mono text-sm focus:outline-none"
            value={markdown}
            onChange={(e) => setMarkdown(e.target.value)}
          />
        </div>
        
        {/* 预览区 */}
        <div className="border rounded-lg shadow-sm">
          <div className="bg-gray-100 px-4 py-2 border-b">
            <h2 className="font-semibold">预览</h2>
          </div>
          <div className="p-4 prose max-w-none h-[800px] overflow-auto">
            <MarkdownRenderer 
              content={markdown} 
              codeTheme={codeTheme}
            />
          </div>
        </div>
      </div>
    </div>
  );
} 