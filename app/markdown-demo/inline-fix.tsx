'use client';

import React from 'react';
import MarkdownRenderer from '../components/MarkdownRenderer';

// 极简测试用例
const testMarkdown = `
# 行内代码修复测试

这是一段普通文本，其中包含 \`行内代码\` 和更多文本。

这是一段包含 \`int x = 10;\` 关键字的行内代码测试。

多个行内代码测试：这里有 \`第一部分\` 和 \`第二部分\` 以及 \`第三部分\`。

行内代码和其他内容混排：这是文本 \`行内代码\` 后面还有文本。这是更多文本 \`另一个行内代码\` 后续文本。
`;

export default function InlineFixTest() {
  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-6">行内代码修复测试</h1>
      <div className="bg-white p-6 rounded-lg shadow-md">
        <MarkdownRenderer content={testMarkdown} />
      </div>
      
      <div className="mt-8 p-4 bg-gray-100 rounded">
        <h2 className="text-xl font-bold mb-4">原始Markdown</h2>
        <pre className="bg-white p-4 rounded whitespace-pre-wrap">
          {testMarkdown}
        </pre>
      </div>
    </div>
  );
} 