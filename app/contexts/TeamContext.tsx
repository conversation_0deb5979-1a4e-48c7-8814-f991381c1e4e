'use client'

import { createContext, useContext } from 'react'
import { TeamDetailView } from '@/app/service/team-service'

interface TeamContextType {
  team: TeamDetailView | null
}

export const TeamContext = createContext<TeamContextType>({ team: null })

export const useTeam = () => {
  const context = useContext(TeamContext)
  if (!context) {
    throw new Error('useTeam must be used within a TeamProvider')
  }
  return context
} 