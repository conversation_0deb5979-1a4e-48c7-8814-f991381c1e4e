'use client';

import { useState, useEffect, Suspense } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSearch, faFilter, faPlus, faBook, faUsers, faStar,
  faRocket, faSitemap, faRepeat, faFont, faCode, faList,
  faLightbulb, faNetworkWired, faArrowRight
} from '@fortawesome/free-solid-svg-icons';

// 使用React Context在父子组件间共享搜索状态
import { createContext, useContext } from 'react';
import Pagination from '../components/Pagination';
import ProblemListService, {ProblemList} from "@/app/service/problem-list-service";
import {checkAuth} from "@/app/utils/authCheck";
import {PROBLEM_LIST_DIFFICULTY_MAP} from "@/app/utils/constant";

interface PaginationData {
  page: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
}

// 创建一个Context用于共享搜索状态
interface SearchContextType {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  handleSearch: (e?: React.FormEvent) => void;
}

const SearchContext = createContext<SearchContextType | undefined>(undefined);

// 分离出使用URL参数的内容组件
function ProblemListsContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const searchContext = useContext(SearchContext);

  // 状态
  const [problemLists, setProblemLists] = useState<ProblemList[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    pageSize: 10,
    totalCount: 0,
    totalPages: 0
  });

  // 过滤器状态
  const [difficulty, setDifficulty] = useState<string>(searchParams.get('difficulty') || '全部');
  const [category, setCategory] = useState<string>(searchParams.get('category') || '全部');
  const [knowledgePoint, setKnowledgePoint] = useState<string>(searchParams.get('knowledgePoint') || '全部');
  const [sort, setSort] = useState<string>(searchParams.get('sort') || 'popular');
  const [activeTab, setActiveTab] = useState<number>(1);

  // 从SearchContext中读取searchTerm
  const { searchTerm } = searchContext || { searchTerm: '' };

  // 获取题单列表数据
  const fetchProblemLists = async () => {
    setLoading(true);

    ProblemListService.getList({
      tab: activeTab,
      pageNum: pagination.page,
      pageSize: pagination.pageSize
    }).then(resp => {
      const data = resp.data
      setProblemLists(data.items)
      setPagination(prev => ({
        ...prev,
        totalCount: data.total
      }))
    }).finally(() => {
      setLoading(false)
    })
  };

  // 监听过滤条件变化
  useEffect(() => {
    fetchProblemLists();
  }, [difficulty, category, sort, pagination.page, searchTerm, activeTab]);

  // 处理页码变化
  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  function handleClickDetailButton(id: number) {
    if (!checkAuth(2)) {
      return;
    }

    router.push('/problem-lists/' + id)
  }

  return (
    <>
      {/* 筛选器 */}
      {/*<div className="bg-white rounded-xl shadow-sm p-4 mb-8">*/}
      {/*  <div className="flex flex-wrap gap-4">*/}
      {/*    <div>*/}
      {/*      <span className="text-gray-700 mr-2">难度:</span>*/}
      {/*      <select*/}
      {/*        value={difficulty}*/}
      {/*        onChange={(e) => setDifficulty(e.target.value)}*/}
      {/*        className="rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 text-sm px-3 py-1.5"*/}
      {/*      >*/}
      {/*        <option>全部</option>*/}
      {/*        <option>入门</option>*/}
      {/*        <option>基础</option>*/}
      {/*        <option>中等</option>*/}
      {/*        <option>进阶</option>*/}
      {/*        <option>挑战</option>*/}
      {/*      </select>*/}
      {/*    </div>*/}
      {/*    <div>*/}
      {/*      <span className="text-gray-700 mr-2">分类:</span>*/}
      {/*      <select*/}
      {/*        value={category}*/}
      {/*        onChange={(e) => setCategory(e.target.value)}*/}
      {/*        className="rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 text-sm px-3 py-1.5"*/}
      {/*      >*/}
      {/*        <option>全部</option>*/}
      {/*        <option>官方题单</option>*/}
      {/*        <option>热门题单</option>*/}
      {/*        <option>自定义题单</option>*/}
      {/*        <option>AI生成</option>*/}
      {/*      </select>*/}
      {/*    </div>*/}
      {/*    <div>*/}
      {/*      <span className="text-gray-700 mr-2">知识点:</span>*/}
      {/*      <select*/}
      {/*        value={knowledgePoint}*/}
      {/*        onChange={(e) => setKnowledgePoint(e.target.value)}*/}
      {/*        className="rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 text-sm px-3 py-1.5"*/}
      {/*      >*/}
      {/*        <option>全部</option>*/}
      {/*        <option>基础语法</option>*/}
      {/*        <option>数组</option>*/}
      {/*        <option>字符串</option>*/}
      {/*        <option>函数</option>*/}
      {/*        <option>递归</option>*/}
      {/*        <option>算法</option>*/}
      {/*      </select>*/}
      {/*    </div>*/}
      {/*    /!*<div className="ml-auto">*!/*/}
      {/*    /!*  <span className="text-gray-700 mr-2">排序:</span>*!/*/}
      {/*    /!*  <select*!/*/}
      {/*    /!*    value={sort}*!/*/}
      {/*    /!*    onChange={(e) => setSort(e.target.value)}*!/*/}
      {/*    /!*    className="rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 text-sm px-3 py-1.5"*!/*/}
      {/*    /!*  >*!/*/}
      {/*    /!*    <option value="popular">热门程度</option>*!/*/}
      {/*    /!*    <option value="latest">最新发布</option>*!/*/}
      {/*    /!*    <option value="difficultyAsc">难度升序</option>*!/*/}
      {/*    /!*    <option value="difficultyDesc">难度降序</option>*!/*/}
      {/*    /!*    <option value="problemCount">题目数量</option>*!/*/}
      {/*    /!*  </select>*!/*/}
      {/*    /!*</div>*!/*/}
      {/*  </div>*/}
      {/*</div>*/}

      {/* 标签切换 */}
      <div className="mb-6 border-b border-gray-200">
        <ul className="flex flex-wrap -mb-px">
          <li className="mr-2">
            <button
              onClick={() => setActiveTab(1)}
              className={`inline-block py-2 px-4 font-medium ${
                activeTab === 1
                  ? 'text-indigo-600 border-b-2 border-indigo-600'
                  : 'text-gray-600 hover:text-indigo-600'
              }`}
            >
              推荐题单
            </button>
          </li>
          <li className="mr-2">
            <button
              onClick={() => setActiveTab(2)}
              className={`inline-block py-2 px-4 font-medium ${
                activeTab === 2
                  ? 'text-indigo-600 border-b-2 border-indigo-600'
                  : 'text-gray-600 hover:text-indigo-600'
              }`}
            >
              官方题单
            </button>
          </li>
          <li className="mr-2">
            <button
              onClick={() => setActiveTab(5)}
              className={`inline-block py-2 px-4 font-medium ${
                activeTab === 5
                  ? 'text-indigo-600 border-b-2 border-indigo-600'
                  : 'text-gray-600 hover:text-indigo-600'
              }`}
            >
              我的题单
            </button>
          </li>
          <li className="mr-2">
            <button
              onClick={() => {
                if (!checkAuth()) {
                  return;
                }
                setActiveTab(3)
              }}
              className={`inline-block py-2 px-4 font-medium ${
                activeTab === 3
                  ? 'text-indigo-600 border-b-2 border-indigo-600'
                  : 'text-gray-600 hover:text-indigo-600'
              }`}
            >
              进行中
            </button>
          </li>
          <li className="mr-2">
            <button
              onClick={() => {
                if (!checkAuth()) {
                  return;
                }
                setActiveTab(4)
              }}
              className={`inline-block py-2 px-4 font-medium ${
                activeTab === 4
                  ? 'text-indigo-600 border-b-2 border-indigo-600'
                  : 'text-gray-600 hover:text-indigo-600'
              }`}
            >
              已完成
            </button>
          </li>
        </ul>
      </div>

      {/* 加载状态 */}
      {loading ? (
        <div className="flex justify-center items-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
        </div>
      ) : error ? (
        <div className="bg-red-100 text-red-700 p-4 rounded-lg">
          {error}
        </div>
      ) : (
        <>
          {/* 推荐题单 - 特色部分 (仅在第一页且选择推荐标签时显示) */}
          {pagination.page === 1 && activeTab === 1 && (
            <div className="grid md:grid-cols-2 gap-6 mb-8">
              {problemLists.slice(0, 2).map((list, index) => (
                <div
                  key={list.id}
                  className={`bg-gradient-to-r ${index === 0 ? 'from-indigo-500 to-purple-600' : 'from-green-500 to-teal-500'} rounded-xl shadow-lg overflow-hidden`}
                >
                  <div className="p-6 text-white">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="inline-block px-2 py-1 bg-white/30 rounded-lg text-sm mb-2">
                          官方精选
                        </div>
                        <h3 className="text-xl font-bold mb-2">{list.name}</h3>
                        <p className="text-indigo-100 mb-4">{list.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="flex items-center">
                        <FontAwesomeIcon icon={faBook} className="mr-1" />
                        <span>{list.problemCount}题</span>
                      </div>
                      <div className="flex items-center">
                        <FontAwesomeIcon icon={faUsers} className="mr-1" />
                        <span>{list.studyUserAmt?.toLocaleString() || 0}人学习</span>
                      </div>
                    </div>
                    <div className="flex space-x-3">
                      <span
                        onClick={() => handleClickDetailButton(list.id)}
                        className="cursor-pointer px-4 py-2 bg-white text-indigo-700 rounded-lg font-medium hover:bg-opacity-90 transition flex-1 text-center"
                      >
                        开始学习
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* 题单列表 */}
          <div className="grid md:grid-cols-3 gap-6">
            {problemLists.map((list) => (
              <div
                key={list.id}
                className="bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-md transition"
              >
                <div className="p-4 border-b border-gray-100">
                  <div className="flex justify-between items-start">
                    <h3 className="text-lg font-semibold text-gray-800">{list.name}</h3>
                  </div>
                  <p className="text-gray-600 text-sm mt-1 mb-2">{list.description}</p>
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faBook} className="mr-1" />
                      <span>{list.problemCount}题</span>
                    </div>
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faFilter} className="mr-1" />
                      <span>{PROBLEM_LIST_DIFFICULTY_MAP[list.difficulty].text}</span>
                    </div>
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faUsers} className="mr-1" />
                      <span>{list.studyUserAmt?.toLocaleString() || 0}人学习</span>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 flex justify-between items-center">
                  {/*<div className="flex items-center">*/}
                  {/*  <div className="w-6 h-6 rounded-full bg-indigo-600 flex items-center justify-center text-white text-xs">*/}
                  {/*    <FontAwesomeIcon icon={getIcon(list.icon)} />*/}
                  {/*  </div>*/}
                  {/*  <span className="ml-2 text-sm text-gray-500">{list.category}</span>*/}
                  {/*</div>*/}
                  <span
                    onClick={() => handleClickDetailButton(list.id)}
                    className="cursor-pointer px-3 py-1 bg-indigo-600 text-white text-sm rounded hover:bg-indigo-700 transition"
                  >
                    查看详情
                  </span>
                </div>
              </div>
            ))}
          </div>

          {/* 分页 */}
          {pagination.totalCount > 0 && (
            <Pagination
              currentPage={pagination.page}
              totalItems={pagination.totalCount}
              pageSize={pagination.pageSize}
              onPageChange={handlePageChange}
              showInfo={true}
            />
          )}
        </>
      )}
    </>
  );
}

// 加载中状态的占位组件
function LoadingProblemLists() {
  return (
    <div className="text-center py-20">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500 mx-auto mb-4"></div>
      <p className="text-gray-600">正在加载题单列表...</p>
    </div>
  );
}

export default function ProblemLists() {
  // 在父组件中创建搜索状态和处理函数
  const [searchTerm, setSearchTerm] = useState<string>('');

  // 处理搜索请求
  const handleSearch = (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    // 搜索状态已通过Context传递给子组件，会自动触发查询
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 标题和搜索栏 */}
      <div className="mb-8 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">题单广场</h1>
          <p className="text-gray-600 mt-1">精心设计的学习路径，让刷题更有效率</p>
        </div>
        <div className="mt-4 md:mt-0 flex">
          <form onSubmit={handleSearch} className="relative flex-1">
            <input
              type="text"
              placeholder="搜索题单..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            />
            <button type="submit" className="absolute right-2 top-2 text-gray-500">
              <FontAwesomeIcon icon={faSearch} />
            </button>
          </form>
          {/*<button className="ml-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition">*/}
          {/*  <FontAwesomeIcon icon={faPlus} className="mr-1" /> 创建题单*/}
          {/*</button>*/}
        </div>
      </div>

      {/* 使用SearchContext提供搜索状态给子组件 */}
      <SearchContext.Provider value={{ searchTerm, setSearchTerm, handleSearch }}>
        {/* 使用Suspense包装使用useSearchParams的组件 */}
        <Suspense fallback={<LoadingProblemLists />}>
          <ProblemListsContent />
        </Suspense>
      </SearchContext.Provider>
    </div>
  );
}
