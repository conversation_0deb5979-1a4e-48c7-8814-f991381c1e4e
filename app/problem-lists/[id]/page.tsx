'use client';

import {useState, useEffect, cache} from 'react';
import Link from 'next/link';
import Image from 'next/image';
import {FontAwesomeIcon} from '@fortawesome/react-fontawesome';
import {
	faArrowLeft, faHeart, faShare, faUserEdit, faCalendarAlt,
	faClock, faFilter, faCheck, faSpinner, faCircle,
	faCheckCircle, faSyncAlt, faCalendarCheck, faUsers,
	faChevronRight
} from '@fortawesome/free-solid-svg-icons';
import ProblemListService, {
	ProblemListDetail,
	ProblemListProblem,
	UserProblemListProgressDetail
} from "@/app/service/problem-list-service";
import {number} from "property-information/lib/util/types";
import {useParams} from "next/navigation";
import {CODING_PROBLEM_DIFFICULTY_MAP, PROBLEM_LIST_DIFFICULTY_MAP} from "@/app/utils/constant";
import {formatMinutes, formatSeconds} from "@/app/utils/number-utils";


// 创建一个缓存解包函数
const getParams = cache((params: { id: string }) => {
	return {id: params.id};
});

export default function ProblemListDetail() {
	// 使用缓存函数获取解包后的参数
	const {id} = useParams<{ id: string }>()

	const [problemList, setProblemList] = useState<ProblemListDetail>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [isFavorite, setIsFavorite] = useState(false);
	const [problemFilter, setProblemFilter] = useState('all');
	const [sortBy, setSortBy] = useState('default');
	const [problemProgress, setProblemProgress] = useState<Record<number, UserProblemListProgressDetail>>({})
	const [completedCount, setCompletedCount] = useState<number>(0)
	const [inProgressCount, setInProgressCount] = useState<number>(0)
	const [averageTime, setAverageTime] = useState<number>(0)

	const initializeProgress = (detail: ProblemListDetail) => {
		const progressMap = {}
		const progressDetails = detail.progressDetails;
		let completedCount = 0
		let inProgressCount = 0
		let totalUseTime = 0
		progressDetails.forEach(item => {
			progressMap[item.problemListProblemId] = item
			if (item.status == 2) {
				completedCount++
				totalUseTime += item.useTime
			} else if (item.status == 1) {
				inProgressCount++
			}
		})
		setProblemProgress(progressMap)
		setCompletedCount(completedCount)
		setInProgressCount(inProgressCount)
		if (completedCount > 0) {
			setAverageTime(totalUseTime / completedCount)
		}
	}

	// 获取题单详情
	useEffect(() => {
		const fetchProblemListDetail = async () => {
			setLoading(true);
			setError(null);

			ProblemListService.getDetail(id).then(resp => {
				console.log(resp.data)
				setProblemList(resp.data)
				initializeProgress(resp.data)
			}).finally(() => {
				setLoading(false)
			})
		};

		fetchProblemListDetail();
	}, [id]);

	// 收藏/取消收藏
	const toggleFavorite = () => {
		setIsFavorite(!isFavorite);
	};

	// 根据状态获取图标
	const getStatusIcon = (problem: ProblemListProblem) => {
		const status = getStatus(problem.problemListProblemId)
		console.log(problem.problemListProblemId, status)
		switch (status) {
			case 2:
				return <span
					className="flex items-center justify-center w-6 h-6 rounded-full bg-green-100 text-green-600"><FontAwesomeIcon
					icon={faCheck} className="text-xs"/></span>;
			case 1:
				return <span
					className="flex items-center justify-center w-6 h-6 rounded-full bg-yellow-100 text-yellow-600"><FontAwesomeIcon
					icon={faSpinner} className="text-xs"/></span>;
			default:
				return <span
					className="flex items-center justify-center w-6 h-6 rounded-full bg-gray-100 text-gray-600"><FontAwesomeIcon
					icon={faCircle} className="text-xs"/></span>;
		}
	};

	// 根据难度获取样式
	const getDifficultyStyle = (difficulty: number) => {
		switch (difficulty) {
			case 1:
				return 'bg-green-100 text-green-800';
			case 2:
				return 'bg-yellow-100 text-yellow-800';
			case 3:
				return 'bg-red-100 text-red-800';
			default:
				return 'bg-gray-100 text-gray-800';
		}
	};

	const getStatus = (id) => {
		return problemProgress[id]?.status || 0
	}

	// 过滤问题列表
	const getFilteredProblems = () => {
		if (!problemList) return [];

		let filtered = [...problemList.problems];

		// 根据状态过滤
		if (problemFilter !== 'all') {
			filtered = filtered.filter(problem => getStatus(problem.problemListProblemId) == problemFilter);
		}

		// 排序
		if (sortBy === 'difficulty') {
			filtered.sort((a, b) => {
				return a.difficulty - b.difficulty
			});
		} else if (sortBy === 'passRate') {
			filtered.sort((a, b) => {
				return a.passRate - b.passRate;
			});
		}

		return filtered;
	};

	if (loading) {
		return (
			<div className="flex justify-center items-center min-h-screen">
				<div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
			</div>
		);
	}

	if (error || !problemList) {
		return (
			<div className="max-w-7xl mx-auto px-4 py-8">
				<div className="bg-red-100 text-red-700 p-4 rounded-lg">
					{error || '题单不存在'}
				</div>
			</div>
		);
	}

	function getProblemLink(problem: ProblemListProblem) {
		console.log(problem)
		let link;
		if (problem.problemType === 1) {
			link = "/basic-problems/"
		} else {
			link = "/coding-problems/"
		}
		return `${link}${problem.pid}?envType=problem-list&envId=${id}`
	}

	function getStartButtonLink() {
		const list = problemList.problems.filter(p => getStatus(p.problemListProblemId) !== 2)
		if (list.length > 0) {
			return getProblemLink(list[0])
		}
		return null
	}

	return (
		<main className="max-w-7xl mx-auto px-4 py-8">
			{/* 返回按钮 */}
			<div className="mb-6">
				<Link href="/problem-lists" className="flex items-center text-sm text-indigo-600 hover:text-indigo-800">
					<FontAwesomeIcon icon={faArrowLeft} className="mr-2"/> 返回题单列表
				</Link>
			</div>

			{/* 题单基本信息 */}
			<div className="bg-white shadow-md rounded-lg p-6 mb-8">
				<div className="flex items-start">
					{/*<Image*/}
					{/*  src={problemList.ban}*/}
					{/*  alt={problemList.title}*/}
					{/*  width={160}*/}
					{/*  height={160}*/}
					{/*  className="w-40 h-40 object-cover rounded-lg shadow-sm"*/}
					{/*/>*/}
					<div className="ml-6 flex-1">
						<div className="flex justify-between items-start">
							<div>
								<h1 className="text-2xl font-bold text-gray-900 mb-2">{problemList.name}</h1>
								<div className="flex items-center mb-4">
									{/*<span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mr-2">{problemList.category}</span>*/}
									<span
										className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full mr-2">{PROBLEM_LIST_DIFFICULTY_MAP[problemList.difficulty].text}</span>
									<span
										className="text-gray-500 text-sm">已有 {problemList.studyUserAmt?.toLocaleString() || 0} 人学习</span>
								</div>
							</div>
							<div className="flex space-x-2">
								<button
									onClick={toggleFavorite}
									className={`flex items-center ${isFavorite ? 'text-red-500' : 'text-gray-500 hover:text-red-500'} focus:outline-none`}
								>
									<FontAwesomeIcon icon={faHeart} className={isFavorite ? 'fas' : 'far'}/>
									<span className="ml-1">{isFavorite ? '已收藏' : '收藏'}</span>
								</button>
								<button className="flex items-center text-gray-500 hover:text-indigo-600 focus:outline-none">
									<FontAwesomeIcon icon={faShare} className="mr-1"/>
									<span>分享</span>
								</button>
							</div>
						</div>
						<p className="text-gray-600 mb-4">{problemList.description}</p>
						<div className="flex items-center text-gray-500 text-sm">
							<div className="flex items-center mr-4">
								<FontAwesomeIcon icon={faUserEdit} className="mr-1"/>
								<span>出题人: {problemList.isAigc ? 'AI生成' : '官方'}</span>
							</div>
							<div className="flex items-center mr-4">
								<FontAwesomeIcon icon={faCalendarAlt} className="mr-1"/>
								<span>更新时间: {problemList.createdTime}</span>
							</div>
							<div className="flex items-center">
								<FontAwesomeIcon icon={faClock} className="mr-1"/>
								<span>预计用时: {formatMinutes(problemList.estimatedTime)}</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* 进度统计 */}
			{
				<div className="bg-white shadow-md rounded-lg p-6 mb-8">
					<h2 className="text-xl font-semibold text-gray-900 mb-4">我的学习进度</h2>
					<div className="flex items-center justify-between mb-6">
						<div className="w-1/2 pr-4">
							<div className="flex justify-between mb-2">
								<span className="text-gray-700">完成进度</span>
								<span className="text-indigo-600 font-medium">{completedCount}/{problemList.problems.length} 题</span>
							</div>
							<div className="w-full bg-gray-200 rounded-full h-3">
								<div
									className="bg-indigo-600 h-3 rounded-full"
									style={{width: `${(completedCount / problemList.problems.length) * 100}%`}}
								></div>
							</div>
						</div>
						<div className="grid grid-cols-2 gap-4 w-1/2">
							<div className="bg-green-50 p-3 rounded-lg">
								<p className="text-sm text-gray-500">已解决</p>
								<p className="text-xl font-bold text-green-600">{completedCount}题</p>
							</div>
							<div className="bg-yellow-50 p-3 rounded-lg">
								<p className="text-sm text-gray-500">尝试中</p>
								<p className="text-xl font-bold text-yellow-600">{inProgressCount}题</p>
							</div>
							<div className="bg-blue-50 p-3 rounded-lg">
								<p className="text-sm text-gray-500">平均用时</p>
								<p className="text-xl font-bold text-blue-600">{completedCount > 0 ? `${formatSeconds(averageTime)}` : '-'}</p>
							</div>
							<div className="bg-purple-50 p-3 rounded-lg">
								<p className="text-sm text-gray-500">连续学习</p>
								<p className="text-xl font-bold text-purple-600">{problemList.progress?.consecutiveStudyDays || 0}天</p>
							</div>
						</div>
					</div>
				</div>
			}

			{/* 题目列表 */}
			<div className="bg-white shadow-md rounded-lg overflow-hidden">
				<div className="p-6 border-b border-gray-200">
					<div className="flex justify-between items-center mb-4">
						<h2 className="text-xl font-semibold text-gray-900">题目列表 ({problemList.problems.length})</h2>
						<div className="flex space-x-2">
							<select
								value={problemFilter}
								onChange={(e) => setProblemFilter(e.target.value)}
								className="text-gray-500 px-3 py-1 rounded-md border border-gray-200 text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500"
							>
								<option value="all">全部题目</option>
								<option value="2">已完成</option>
								<option value="1">进行中</option>
								<option value="0">未开始</option>
							</select>
							<select
								value={sortBy}
								onChange={(e) => setSortBy(e.target.value)}
								className="text-gray-500 px-3 py-1 rounded-md border border-gray-200 text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500"
							>
								<option value="default">按推荐顺序</option>
								<option value="difficulty">按难度排序</option>
								<option value="passRate">按通过率</option>
							</select>
						</div>
					</div>

					<ul className="divide-y divide-gray-200">
						{getFilteredProblems().map((problem) => (
							<li key={problem.pid} className="py-4">
								<div className="flex items-start">
									<div className="flex-shrink-0 mt-1">
										{getStatusIcon(problem)}
									</div>
									<div className="ml-4 flex-1">
										<div className="flex justify-between">
											<Link href={getProblemLink(problem)}
											      className="text-lg font-medium text-indigo-600 hover:underline">
												{problem.title}
											</Link>
											<div className="flex items-center">
                        <span
	                        className={`${getDifficultyStyle(problem.difficulty)} text-xs px-2 py-1 rounded-full mr-2`}>
                          {CODING_PROBLEM_DIFFICULTY_MAP[problem.difficulty].text}
                        </span>
												<span className="text-gray-500 text-sm">通过率: {problem.passRate ? `${problem.passRate}%` : '-'}</span>
											</div>
										</div>
										<p className="mt-1 text-gray-600">{problem.description}</p>
										<div className="mt-2 flex items-center text-sm text-gray-500">
											{getStatus(problem.problemListProblemId) === 2 && (
												<>
                          <span className="mr-3">
                            <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mr-1"/> 已完成
                          </span>
													<span className="mr-3">
                            <FontAwesomeIcon icon={faClock}
                                             className="mr-1"/> 用时: {formatSeconds(problemProgress[problem.problemListProblemId].useTime)}
                          </span>
													<span>
                            <FontAwesomeIcon icon={faCalendarCheck}
                                             className="mr-1"/> 完成于: {problemProgress[problem.problemListProblemId].finishTime}
                          </span>
												</>
											)}
											{getStatus(problem.problemListProblemId) === 1 && (
												<>
                          <span className="mr-3">
                            <FontAwesomeIcon icon={faSyncAlt} className="text-yellow-500 mr-1"/> 进行中
                          </span>
													<span className="mr-3">
                            <FontAwesomeIcon icon={faCheck}
                                             className="mr-1"/> 已尝试: {problemProgress[problem.problemListProblemId].tryCount}次
                          </span>
													<span>
                            <FontAwesomeIcon icon={faCalendarAlt}
                                             className="mr-1"/> 开始于: {problemProgress[problem.problemListProblemId].startTime}
                          </span>
												</>
											)}
											{getStatus(problem.problemListProblemId) === 0 && (
												<span>
                          <FontAwesomeIcon icon={faUsers} className="mr-1"/> {problem.completeCount}人已完成
                        </span>
											)}
										</div>
									</div>
								</div>
							</li>
						))}
					</ul>
				</div>
			</div>

			{getStartButtonLink() != null &&
				(

					<div className="mt-8 flex justify-center">
						<Link
							href={getStartButtonLink() || ''}
							className="px-8 py-3 bg-indigo-600 text-white text-lg font-medium rounded-lg hover:bg-indigo-700 transition shadow-md"
						>
							开始学习
						</Link>
					</div>
				)
			}
		</main>
	);
}
