export interface Category {
	id: number;
	name: string;
}
export interface Tag {
	id: number;
	name: string;
}

export interface Interact {
	commitAmt: number;
	commitUserAmt: number;
	acceptAmt: number;
	acceptUserAmt: number;
}

export interface PageResult<T> {
	items: T[];
	total: number;
}


export interface UserInfo {
	// ID
	id: number;
	// 用户名
	nickname: string;
	// 头像
	avatar: string;
}

export interface DataInteractStatistics {
	viewAmt: number;
	likeAmt: number;
	commentAmt: number;
	collectAmt: number;
	commitAmt: number;
	acceptAmt: number;
}
