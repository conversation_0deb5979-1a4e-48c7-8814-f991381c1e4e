"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSearch,
  faCalendarAlt,
  faEye,
  faComment,
  faTag,
  faArrowRight,
  faChevronLeft,
  faChevronRight
} from '@fortawesome/free-solid-svg-icons';
import Pagination from '../components/Pagination';
import CommonDropdown, { DropdownOption } from '../components/CommonDropdown';
import categoryService, { Category } from '../service/category-service';
import tagService, { Tag, TagGroup } from '../service/tag-service';
import newsService, { NewsModel } from '../service/news-service';
import { PageResult } from '../model/models';
import getUrl from '../utils/url-utils';

// 定义API响应结构
interface ApiResponse<T> {
  code: number;
  data: T;
  msg?: string;
}

// async function getNewList() {
//   const response = await newsService.getPageList({
//     pageNum: 1,
//     pageSize: 10,
//     categoryId: null,
//     title: null,
//     tagId: null
//   })
//   return response.data
// }

export default function NewsList() {
  const [news, setNews] = useState<NewsModel[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // 筛选和分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [filters, setFilters] = useState({
    category: '',
    tag: '',
    search: '',
    sort: 'latest'
  });

  // const list = await getNewList()
  // setNews(list)

  // 获取分类列表
  useEffect(() => {
    setNews(news)
    fetchCategories();
  }, []);

  // 获取标签列表
  useEffect(() => {
    fetchTags();
  }, []);

  // 获取新闻数据
  useEffect(() => {
    fetchNewsList();
  }, [currentPage, filters.category, filters.tag, filters.sort]);

  const fetchCategories = async () => {
    try {
      const response = await categoryService.getCategoryList(99) as unknown as ApiResponse<Category[]>;
      if (response.data) {
        setCategories(response.data);
      } else {
        console.error('获取分类列表失败:', response?.msg);
      }
    } catch (err: any) {
      console.error('获取分类列表错误:', err);
    }
  };

  const fetchTags = async () => {
    try {
      const response = await tagService.getTagList(99) as unknown as ApiResponse<TagGroup>;
      if (response.data) {
        setTags(response.data.tags || []);
      } else {
        console.error('获取标签列表失败:', response?.msg);
      }
    } catch (err: any) {
      console.error('获取标签列表错误:', err);
    }
  };

  const fetchNewsList = async () => {
    setLoading(true);
    setError('');

    try {
      // 查找选择的分类ID
      let categoryId = null;
      if (filters.category) {
        const selectedCategory = categories.find(c => c.name === filters.category);
        if (selectedCategory) {
          categoryId = selectedCategory.id;
        }
      }

      // 查找选择的标签ID
      let tagId = null;
      if (filters.tag) {
        const selectedTag = tags.find(t => t.name === filters.tag);
        if (selectedTag) {
          tagId = selectedTag.id;
        }
      }

      const response = await newsService.getPageList({
        pageNum: currentPage,
        pageSize: pageSize,
        title: filters.search || null,
        categoryId: categoryId,
        tagId: null
      }) as unknown as ApiResponse<PageResult<NewsModel>>;

      if (response.data) {
        setNews(response.data.items);
        setTotalItems(response.data.total);
      } else {
        throw new Error(response?.msg || '获取资讯列表失败');
      }
    } catch (err: any) {
      console.error('获取资讯列表错误:', err);
      setError(err.message || '获取资讯列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理搜索
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters(prev => ({...prev, search: e.target.value}));
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchNewsList();
  };

  // 处理分类筛选
  const handleCategoryChange = (value: string) => {
    setFilters(prev => ({...prev, category: value}));
    setCurrentPage(1);
  };

  // 处理标签点击
  const handleTagClick = (tag: string) => {
    setFilters(prev => ({...prev, tag}));
    setCurrentPage(1);
  };

  // 处理页码变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // 日期格式化
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">编程资讯</h1>
          <p className="text-gray-600">了解最新编程趋势、比赛信息和学习资源</p>
        </div>

        <div className="flex flex-col md:flex-row">
          {/* 左侧文章列表 */}
          <div className="w-full md:w-2/3 md:pr-8">
            {/* 搜索和过滤器 */}
            <div className="bg-white shadow-md rounded-lg p-4 mb-8">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between">
                <div className="w-full sm:w-1/2 mb-4 sm:mb-0">
                  <form onSubmit={handleSearchSubmit}>
                    <div className="relative">
                      <input
                        type="text"
                        placeholder="搜索文章"
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        value={filters.search}
                        onChange={handleSearchChange}
                      />
                      <button
                        type="submit"
                        className="absolute right-0 top-0 mt-2 mr-3 text-gray-400 hover:text-indigo-600"
                      >
                        <FontAwesomeIcon icon={faSearch}/>
                      </button>
                    </div>
                  </form>
                </div>
                <div className="flex flex-wrap gap-2">
                  <CommonDropdown
                    label="分类"
                    placeholder="全部分类"
                    value={filters.category}
                    onChange={handleCategoryChange}
                    options={[
                      {label: '全部分类', value: ''},
                      ...categories.map(cat => ({label: cat.name, value: cat.name}))
                    ]}
                    className="w-48"
                  />
                </div>
              </div>
            </div>

            {/* 错误信息 */}
            {error && (
              <div className="bg-red-50 p-4 rounded-lg text-red-600 mb-8">
                {error}
              </div>
            )}

            {/* 加载中 */}
            {loading && (
              <div className="bg-white rounded-lg shadow-md p-8 text-center mb-8">
                <div
                  className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600 mb-2"></div>
                <p className="text-gray-600">加载中...</p>
              </div>
            )}

            {/* 文章列表 */}
            {!loading && !error && (
              <div className="space-y-8">
                {/* 置顶文章 */}
                {news.filter(item => item.isTop).map(item => (
                  <div key={item.id} className="bg-white shadow-md rounded-lg overflow-hidden mb-8">
                    <div className="relative">
                      <div className="w-full h-64 relative">
                        <Image
                          src={getUrl(item.coverImage)}
                          alt={item.title}
                          fill
                          className="object-cover"
                          sizes="(max-width: 768px) 100vw, 768px"
                        />
                      </div>
                      <div className="absolute top-0 left-0 bg-red-600 text-white text-xs px-3 py-1 m-4 rounded-full">
                        置顶
                      </div>
                      <div className="absolute top-0 right-0 bg-blue-600 text-white text-xs px-3 py-1 m-4 rounded-full">
                        {item.categoryName}
                      </div>
                    </div>
                    <div className="p-6">
                      <h2 className="text-2xl font-bold text-gray-900 mb-3 hover:text-indigo-600">
                        <Link href={`/news/${item.id}`}>{item.title}</Link>
                      </h2>
                      <p className="text-gray-600 mb-4">
                        {item.subTitle}
                      </p>
                      <div className="flex items-center text-sm text-gray-500">
                        <span className="mr-4">
                          <FontAwesomeIcon icon={faCalendarAlt} className="mr-1"/>
                          {formatDate(item.createdTime)}
                        </span>
                        <span className="mr-4">
                          <FontAwesomeIcon icon={faEye} className="mr-1"/>
                          {item.viewAmt.toLocaleString()} 次阅读
                        </span>
                      </div>
                      <div className="mt-4">
                        <Link
                          href={`/news/${item.id}`}
                          className="text-indigo-600 hover:text-indigo-800 font-medium"
                        >
                          阅读全文 <FontAwesomeIcon icon={faArrowRight} className="ml-1 text-xs"/>
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}

                {/* 普通文章列表 */}
                {news.filter(item => !item.isTop).map(item => (
                  <div key={item.id}
                       className="bg-white shadow-md rounded-lg overflow-hidden flex flex-col md:flex-row">
                    <div className="md:w-1/3 relative">
                      <div className="h-48 w-full relative">
                        <Image
                          src={item.coverImage}
                          alt={item.title}
                          fill
                          className="object-cover"
                          sizes="(max-width: 768px) 100vw, 30vw"
                        />
                      </div>
                    </div>
                    <div className="p-6 md:w-2/3">
                      <div className="flex items-center mb-2">
                        <span className="bg-indigo-100 text-indigo-800 text-xs px-2 py-1 rounded-full mr-2">
                          {item.categoryName}
                        </span>
                        {item.tags && item.tags.length > 0 && (
                          <span className="text-gray-500 text-xs">{item.tags.map(t => t.name).join(', ')}</span>
                        )}
                      </div>
                      <h2 className="text-xl font-bold text-gray-900 mb-2 hover:text-indigo-600">
                        <Link href={`/news/${item.id}`}>{item.title}</Link>
                      </h2>
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                        {item.subTitle}
                      </p>
                      <div className="flex items-center text-xs text-gray-500">
                        <span className="mr-3">{item.author}</span>
                        <span className="mr-3">
                          <FontAwesomeIcon icon={faCalendarAlt} className="mr-1"/>
                          {formatDate(item.createdTime)}
                        </span>
                        <span className="mr-3">
                          <FontAwesomeIcon icon={faEye} className="mr-1"/>
                          {item.viewAmt.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}

                {/* 没有数据时显示 */}
                {news.length === 0 && (
                  <div className="bg-white rounded-lg shadow-md p-8 text-center">
                    <p className="text-gray-600">暂无相关资讯</p>
                  </div>
                )}
              </div>
            )}

            {/* 分页控件 */}
            {!loading && totalItems > 0 && (
              <Pagination
                currentPage={currentPage}
                totalItems={totalItems}
                pageSize={pageSize}
                onPageChange={handlePageChange}
              />
            )}
          </div>

          {/* 右侧边栏 */}
          <div className="w-full md:w-1/3 mt-8 md:mt-0">
            {/* 分类列表 */}
            <div className="bg-white shadow-md rounded-lg p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">资讯分类</h3>
              <ul className="space-y-3">
                {categories.map(category => (
                  <li key={category.id} className="flex justify-between items-center">
                    <button
                      onClick={() => {
                        setFilters(prev => ({...prev, category: category.name}));
                        setCurrentPage(1);
                      }}
                      className="text-gray-700 hover:text-indigo-600 flex items-center"
                    >
                      {category.name}
                    </button>
                  </li>
                ))}
              </ul>
            </div>

            {/* 推荐标签 */}
            {/*<div className="bg-white shadow-md rounded-lg p-6 mb-6">*/}
            {/*  <h3 className="text-lg font-semibold text-gray-900 mb-4">热门标签</h3>*/}
            {/*  <div className="flex flex-wrap gap-2">*/}
            {/*    {tags.map(tag => (*/}
            {/*      <button */}
            {/*        key={tag.id} */}
            {/*        onClick={() => handleTagClick(tag.name)}*/}
            {/*        className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-indigo-100 hover:text-indigo-800"*/}
            {/*      >*/}
            {/*        {tag.name}*/}
            {/*      </button>*/}
            {/*    ))}*/}
            {/*  </div>*/}
            {/*</div>*/}

            {/* 订阅卡片 */}
            {/*<div className="bg-indigo-50 border border-indigo-100 shadow-md rounded-lg p-6">*/}
            {/*  <h3 className="text-lg font-semibold text-indigo-900 mb-2">获取最新资讯</h3>*/}
            {/*  <p className="text-indigo-700 text-sm mb-4">订阅我们的周刊，获取最新编程资讯和学习资源</p>*/}
            {/*  <form className="mb-3">*/}
            {/*    <div className="mb-3">*/}
            {/*      <input */}
            {/*        type="email" */}
            {/*        placeholder="您的邮箱地址" */}
            {/*        className="w-full px-4 py-2 border border-indigo-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"*/}
            {/*      />*/}
            {/*    </div>*/}
            {/*    <button */}
            {/*      type="submit" */}
            {/*      className="w-full bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"*/}
            {/*    >*/}
            {/*      订阅周刊*/}
            {/*    </button>*/}
            {/*  </form>*/}
            {/*  <p className="text-xs text-indigo-600">我们尊重您的隐私，绝不会向第三方分享您的信息</p>*/}
            {/*</div>*/}
          </div>
        </div>
      </main>
    </div>
  );
}
