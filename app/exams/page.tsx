"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faRocket, faSearch, faFilter,
  faChevronDown, faChevronLeft, faChevronRight,
  faThLarge, faList, faUsers, faCalendarAlt, faHeart,
  faChartLine, faStopwatch, faEye
} from '@fortawesome/free-solid-svg-icons';
import Pagination from '../components/Pagination';
import ExamService from "@/app/service/exam-service";
import type {Exam} from '@/app/service/exam-service'
import {EXAM_DIFFICULTY} from "@/app/utils/constant";
import {useRouter} from "next/navigation";
import {checkAuth} from "@/app/utils/authCheck";
import getUrl from "@/app/utils/url-utils";
import CommonDropdown, { DropdownOption } from '../components/CommonDropdown';
import CategoryService, { Category } from '@/app/service/category-service';

export default function ExamLists() {
  const [exams, setExams] = useState<Exam[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [categories, setCategories] = useState<Category[]>([]);
  const [filters, setFilters] = useState({
    categoryId: null as number | null,
    difficulty: null as number | null,
    duration: null as number | null,
    search: ''
  });
  const [user, setUser] = useState<any>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid'); // 'grid' 或 'list'

  const pageSize = 6;

  // 转换分类为下拉选项
  const categoryOptions: DropdownOption<number | null>[] = [
    { label: '全部分类', value: null },
    ...categories.map(category => ({
      label: category.name,
      value: category.id
    }))
  ];

  // 难度级别选项
  const difficultyOptions: DropdownOption<number | null>[] = [
    { label: '全部难度', value: null },
    { label: '入门级', value: 1 },
    { label: '基础级', value: 2 },
    { label: '提高级', value: 3 },
    { label: '竞赛级', value: 4 }
  ];

  // 时长选项
  const durationOptions: DropdownOption<number | null>[] = [
    { label: '全部时长', value: null },
    { label: '1小时内', value: 1 },
    { label: '1-2小时', value: 2 },
    { label: '2-3小时', value: 3 },
    { label: '3小时以上', value: 4 }
  ];

  // 排序选项
  const sortOptions = ['推荐', '最新', '热门', '难度'];
  const [activeSort, setActiveSort] = useState('推荐');

  useEffect(() => {
    fetchExams();
    fetchCategories();
    const storedUser = localStorage.getItem('user');
    // 从localStorage读取展示方式
    const storedViewMode = localStorage.getItem('examViewMode');
    if (storedViewMode && (storedViewMode === 'grid' || storedViewMode === 'list')) {
      setViewMode(storedViewMode);
    }
    
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        setUser(userData);
      } catch (error) {
        console.error('解析用户数据错误:', error);
        localStorage.removeItem('user');
      }
    }
  }, []);

  useEffect(() => {
    fetchExams();
  }, [currentPage, filters.categoryId, filters.difficulty, filters.duration, activeSort]);

  const fetchCategories = async () => {
    try {
      const response = await CategoryService.getCategoryList(3); // 类型3表示试卷分类
      setCategories(response.data);
    } catch (error) {
      console.error('获取分类数据失败:', error);
    }
  };

  const fetchExams = async (retry = 0) => {
    setLoading(true);
    setError('');

    try {
      const response = await ExamService.getExamList({
        pageNum: currentPage, 
        pageSize: pageSize,
        categoryId: filters.categoryId || undefined,
        difficulty: filters.difficulty || undefined,
        duration: filters.duration || undefined,
        keyword: filters.search || undefined
      });
      
      setExams(response.data.items);
      setTotalItems(response.data.total);
    } catch (error) {
      console.error('获取试卷列表失败:', error);
      setError('获取试卷列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (filterType: keyof typeof filters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
    setCurrentPage(1); // 重置为第一页
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters(prev => ({
      ...prev,
      search: e.target.value
    }));
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1); // 重置为第一页
    fetchExams();
  };

  const handleSortChange = (sortType: string) => {
    setActiveSort(sortType);
    setCurrentPage(1); // 重置为第一页
  };

  const handleViewModeChange = (mode: 'grid' | 'list') => {
    setViewMode(mode);
    // 保存展示方式到localStorage
    localStorage.setItem('examViewMode', mode);
  };

  // 处理页码变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const pageCount = Math.ceil(totalItems / pageSize);
  const pageNumbers = [];
  const maxPageButtons = 5;

  let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
  let endPage = Math.min(pageCount, startPage + maxPageButtons - 1);

  if (endPage - startPage + 1 < maxPageButtons) {
    startPage = Math.max(1, endPage - maxPageButtons + 1);
  }

  for (let i = startPage; i <= endPage; i++) {
    pageNumbers.push(i);
  }

  const getTypeColor = () => {
    return 'bg-indigo-600 text-white';
  };

  const getDifficultyColor = (difficulty: number) => {
    switch (difficulty) {
      case 1:
        return 'bg-green-100 text-green-800';
      case 2:
        return 'bg-blue-100 text-blue-800';
      case 3:
        return 'bg-red-100 text-red-800';
      case 4:
        return 'bg-indigo-100 text-indigo-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getDurationColor = (duration: number) => {
    return 'bg-yellow-100 text-yellow-800';
  };

  const getExamTypeColor = (examType: string) => {
    return 'bg-purple-100 text-purple-800';
  };
  const getExamDuration = (duration: number) => {
    let text = `${duration / 60}小时`
    if (duration % 60 !== 0) {
      return `${duration % 60}分钟`
    }
    return text
  }

  const router = useRouter();
  function goDetail(id: number) {
    console.log('goDetail: ', id)
    if (!checkAuth(2)) {
      return;
    }
    router.push(`/exams/${id}`)
  }

  // 渲染考试卡片
  const renderExamCard = (exam: Exam) => (
    <div key={exam.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300">
      {/* <div className="relative">
        <Image
          src={'https:' + exam.banner}
          alt={exam.title}
          width={600}
          height={400}
          className="w-full h-48 object-cover"
        />
        <div className={`absolute top-2 right-2 ${getTypeColor()} text-xs px-2 py-1 rounded-full`}>
          {exam.categoryName}
        </div>
      </div> */}
      <div className="p-5">
        <div className="flex justify-between items-start mb-3">
          <h3 className="text-lg font-semibold text-gray-900 hover:text-indigo-600 h-[3.5rem] flex items-start">
            <Link href={`/exams/${exam.id}`} className="overflow-hidden line-clamp-2 leading-relaxed">{exam.title}</Link>
          </h3>
        </div>
        <div className="flex flex-wrap gap-2 mb-3">
          <span className={`${getDifficultyColor(exam.difficulty)} text-xs px-2 py-1 rounded-full`}>
            {EXAM_DIFFICULTY[(exam.difficulty || 1) as keyof typeof EXAM_DIFFICULTY].text}
          </span>
          <span className={`${getDurationColor(exam.duration)} text-xs px-2 py-1 rounded-full`}>
            {getExamDuration(exam.duration)}
          </span>
          <span className={`bg-indigo-100 text-indigo-800 text-xs px-2 py-1 rounded-full`}>
            {exam.categoryName}
          </span>
        </div>
        <p className="text-gray-600 text-sm mb-4 line-clamp-1 overflow-hidden text-ellipsis">
          {exam.description}
        </p>
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center text-gray-500">
            <span className="mr-3">
              <FontAwesomeIcon icon={faUsers} className="mr-1" /> {exam.participants}人参与
            </span>
            {/*<span>*/}
            {/*  <FontAwesomeIcon icon={faCalendarAlt} className="mr-1" /> {exam.createTime}*/}
            {/*</span>*/}
          </div>
          <span className="text-indigo-600 hover:text-indigo-800 font-medium cursor-pointer" onClick={() => goDetail(exam.id)}>
            开始测试
          </span>
        </div>
      </div>
    </div>
  );

  // 渲染考试列表项
  const renderExamListItem = (exam: Exam) => (
    <div key={exam.id} className="bg-white border border-gray-200 rounded-lg p-4 flex flex-col md:flex-row gap-4 mb-4">
      {/* <div className="relative w-full md:w-48 h-32 flex-shrink-0">
        <Image
          src={getUrl(exam.banner)}
          alt={exam.title}
          width={600}
          height={400}
          className="w-full h-full object-cover rounded-md"
        />
        <div className={`absolute top-2 right-2 ${getTypeColor()} text-xs px-2 py-1 rounded-full`}>
          {exam.categoryName}
        </div>
      </div> */}
      <div className="flex-1">
        <div className="flex justify-between items-start mb-2">
          <h3 className="text-lg font-semibold text-gray-900 hover:text-indigo-600 min-h-[3.5rem] flex items-start flex-1 mr-4">
            <Link href={`/exams/${exam.id}`} className="overflow-hidden line-clamp-2 leading-relaxed">{exam.title}</Link>
          </h3>
          {/*<button className="text-gray-400 hover:text-red-500">*/}
          {/*  <FontAwesomeIcon icon={faHeart} className="far" />*/}
          {/*</button>*/}
        </div>
        <div className="flex flex-wrap gap-2 mb-2">
          <span className={`${getDifficultyColor(exam.difficulty)} text-xs px-2 py-1 rounded-full`}>
            {EXAM_DIFFICULTY[exam.difficulty as keyof typeof EXAM_DIFFICULTY].text}
          </span>
          <span className={`${getDurationColor(exam.duration)} text-xs px-2 py-1 rounded-full`}>
            {getExamDuration(exam.duration)}
          </span>
          <span className={`bg-indigo-100 text-indigo-800 text-xs px-2 py-1 rounded-full`}>
            {exam.categoryName}
          </span>
        </div>
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
          {exam.description}
        </p>
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center text-gray-500">
            <span className="mr-3">
              <FontAwesomeIcon icon={faUsers} className="mr-1" /> {exam.participants}人参与
            </span>
          </div>
          <span className="text-indigo-600 hover:text-indigo-800 font-medium cursor-pointer" onClick={() => goDetail(exam.id)}>
            开始测试
          </span>
        </div>
      </div>
    </div>
  );

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">模拟试卷</h1>
        <p className="text-gray-600">各类竞赛模拟试卷，包含基础选择题和编程题，模拟真实竞赛环境</p>
      </div>

      {/* 搜索和过滤器 */}
      <div className="bg-white shadow-md rounded-lg p-6 mb-8">
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
          <div className="w-full md:w-1/3 mb-4 md:mb-0">
            <form onSubmit={handleSearchSubmit}>
              <div className="relative">
                <input
                  type="text"
                  value={filters.search}
                  onChange={handleSearchChange}
                  placeholder="搜索试卷"
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                />
                <button
                  type="submit"
                  className="absolute right-0 top-0 mt-2 mr-3 text-gray-400 hover:text-indigo-600"
                >
                  <FontAwesomeIcon icon={faSearch} />
                </button>
              </div>
            </form>
          </div>
          <div className="flex flex-wrap gap-4">
            <div>
              <CommonDropdown
                label="分类"
                options={categoryOptions}
                value={filters.categoryId}
                onChange={(value) => handleFilterChange('categoryId', value)}
                placeholder="全部分类"
                className="min-w-40"
              />
            </div>
            <div>
              <CommonDropdown
                label="难度"
                options={difficultyOptions}
                value={filters.difficulty}
                onChange={(value) => handleFilterChange('difficulty', value)}
                placeholder="全部难度"
                className="w-40"
              />
            </div>
            <div>
              <CommonDropdown
                label="时长"
                options={durationOptions}
                value={filters.duration}
                onChange={(value) => handleFilterChange('duration', value)}
                placeholder="全部时长"
                className="w-40"
              />
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between text-sm border-b border-gray-200 pb-4 mb-4">
          <div className="flex items-center">
            {/*<span className="text-gray-600 mr-2">排序方式:</span>*/}
            {/*{sortOptions.map(option => (*/}
            {/*  <button*/}
            {/*    key={option}*/}
            {/*    className={`mr-4 ${activeSort === option ? 'text-indigo-600 font-medium' : 'text-gray-600 hover:text-indigo-600'}`}*/}
            {/*    onClick={() => handleSortChange(option)}*/}
            {/*  >*/}
            {/*    {option} {activeSort === option && <FontAwesomeIcon icon={faChevronDown} className="ml-1 text-xs" />}*/}
            {/*  </button>*/}
            {/*))}*/}
          </div>
          <div className="flex items-center space-x-2">
            <button
              className={`text-gray-600 hover:text-indigo-600 px-2 py-1 ${viewMode === 'grid' ? 'text-indigo-600' : ''}`}
              onClick={() => handleViewModeChange('grid')}
            >
              <FontAwesomeIcon icon={faThLarge} />
            </button>
            <button
              className={`text-gray-600 hover:text-indigo-600 px-2 py-1 ${viewMode === 'list' ? 'text-indigo-600' : ''}`}
              onClick={() => handleViewModeChange('list')}
            >
              <FontAwesomeIcon icon={faList} />
            </button>
          </div>
        </div>

        {/* 加载状态 */}
        {loading && (
          <div className="text-center py-10">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600"></div>
            <p className="mt-2 text-gray-600">加载中...</p>
          </div>
        )}

        {/* 错误信息 */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            <p>{error}</p>
            <button
              onClick={() => fetchExams()}
              className="mt-2 text-sm text-red-700 underline hover:text-red-800"
            >
              重试
            </button>
          </div>
        )}

        {/* 无数据提示 */}
        {!loading && !error && exams.length === 0 && (
          <div className="text-center py-10">
            <p className="text-gray-500">暂无符合条件的考试，请尝试调整筛选条件</p>
          </div>
        )}

        {/* 试卷列表 */}
        {!loading && !error && exams.length > 0 && (
          viewMode === 'grid' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {exams.map(exam => renderExamCard(exam))}
            </div>
          ) : (
            <div className="space-y-4">
              {exams.map(exam => renderExamListItem(exam))}
            </div>
          )
        )}

        {/* 分页控制 */}
        {!loading && exams.length > 0 && (
          <Pagination
            currentPage={currentPage}
            totalItems={totalItems}
            pageSize={pageSize}
            onPageChange={handlePageChange}
          />
        )}
      </div>
    </div>
  );
}
