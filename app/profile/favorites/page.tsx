"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { ClockIcon, TrashIcon, BookOpenIcon, CodeBracketIcon } from '@heroicons/react/24/outline';
import CollectService, {CollectData, CollectProblem} from "@/app/service/collect-service";
import Pagination from "@/app/components/Pagination";
import {SUBJECT_DIFFICULTY_MAP} from "@/app/utils/constant";
import {Tag} from "@/app/model/models";

export default function FavoritesPage() {
  const [favorites, setFavorites] = useState<CollectData[]>([]);
  const [activeTab, setActiveTab] = useState<'basic' | 'coding'>('basic');
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [pageSize, setPageSize] = useState<number>(12)
  const [totalItems, setTotalItems] = useState<number>(0)

  // 用户ID
  const userId = 'user1';

  // 获取用户收藏数据
  const fetchFavorites = async () => {
    let dataType = 2
    if (activeTab == 'basic') {
      dataType = 3
    }
    setIsLoading(true)
    CollectService.getList(dataType, {pageNum: currentPage, pageSize: pageSize}).then(resp => {
      console.log(resp)
      setFavorites(resp.data.items)
      setTotalItems(resp.data.total)
      setIsLoading(false)
    })
  };

  // 切换标签时重新获取数据
  useEffect(() => {
    fetchFavorites();
  }, [activeTab, currentPage]);

  // 取消收藏
  const handleRemoveFavorite = async (dataType: number, dataId: number) => {
    CollectService.cancelCollect(dataType, dataId).then(() => {
      fetchFavorites()
    })
  };

  // 格式化时间
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('zh-CN');
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  function getLink(problem: CollectProblem) {
    return problem.problemType === 1 ? `/basic-problems/${problem.displayId}` : `/coding-problems/${problem.displayId}`;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">我的收藏</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => {
              setActiveTab('basic')
              setCurrentPage(1)
            }}
            className={`px-4 py-2 rounded-lg ${
              activeTab === 'basic'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            基础题目
          </button>
          <button
            onClick={() => {
              setActiveTab('coding')
              setCurrentPage(1)
            }}
            className={`px-4 py-2 rounded-lg ${
              activeTab === 'coding'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            编程题目
          </button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : favorites.length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <div className="text-gray-500 mb-4">
            <svg
              className="w-16 h-16 mx-auto"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              ></path>
            </svg>
          </div>
          <h3 className="text-lg font-semibold mb-2">暂无收藏</h3>
          <p className="text-gray-500 mb-4">
            您还没有收藏任何{activeTab === 'basic' ? '基础题目' : '编程题目'}
          </p>
          <Link
            href={activeTab === 'basic' ? '/basic-problems' : '/coding-problems'}
            className="inline-block px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
          >
            去浏览{activeTab === 'basic' ? '基础题目' : '编程题目'}
          </Link>
        </div>
      ) : (
        <div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {favorites.map((favorite) => {
              return (
                <div
                  key={favorite.id}
                  className="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow"
                >
                  <div className="flex justify-between items-start">
                    <h3 className="text-lg font-semibold mb-2 flex-1 line-clamp-2 h-14 overflow-hidden">
                      {favorite.problem.title}
                    </h3>
                    <button
                      onClick={() => handleRemoveFavorite(favorite.dataType, favorite.dataId)}
                      className="text-red-500 hover:text-red-700"
                      title="取消收藏"
                    >
                      <TrashIcon className="w-5 h-5" />
                    </button>
                  </div>

                  <div className="flex gap-2 mb-3">
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    favorite.problem.difficulty === 1
                      ? 'bg-green-100 text-green-800'
                      : favorite.problem.difficulty === 2
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                  }`}>
                    {SUBJECT_DIFFICULTY_MAP[favorite.problem.difficulty].text}
                  </span>
                    {favorite.problem.tags?.map((tag: Tag) => (
                      <span
                        key={tag.id}
                        className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full"
                      >
                      {tag.name}
                    </span>
                    ))}
                  </div>

                  <div className="flex justify-between items-center mb-3">
                    <div className="flex items-center text-sm text-gray-500">
                      <ClockIcon className="w-4 h-4 mr-1" />
                      <span>收藏于 {formatDate(favorite.createdTime)}</span>
                    </div>
                    <div className="flex items-center text-sm text-indigo-600">
                      {favorite.problem.problemType === 1 ? (
                        <BookOpenIcon className="w-4 h-4 mr-1" />
                      ) : (
                        <CodeBracketIcon className="w-4 h-4 mr-1" />
                      )}
                      <span>{favorite.problem.problemType === 1 ? '基础题' : '编程题'}</span>
                    </div>
                  </div>

                  <Link
                    href={getLink(favorite.problem)}
                    className="inline-block w-full text-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
                  >
                    查看题目
                  </Link>
                </div>

              );
            })}
          </div>
          <Pagination
            currentPage={currentPage}
            totalItems={totalItems}
            pageSize={pageSize}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
}
