'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faEdit, faCrown, faSyncAlt, faChevronRight,
  faCheckCircle, faUser, faBook, faStar, faClock, faHistory, faBookmark, faTimesCircle
} from '@fortawesome/free-solid-svg-icons';
import { Chart, registerables } from 'chart.js';
import EditProfileModal from '../components/EditProfileModal';
import axios from 'axios';
import ProfileService from "@/app/service/profile-service";
import type { Profile } from "@/app/service/profile-service";
import ProfileSkeleton from '../components/ProfileSkeleton';
import ProgressSkeleton from '../components/ProgressSkeleton';
import SubmissionService from "@/app/service/submission-service";
import type { Submission } from "@/app/service/submission-service";
import {setUser} from "@/app/redux/features/authSlice";
import {useAppDispatch} from "@/app/redux/hooks";
import { checkAuth } from '@/app/utils/authCheck';
import { useRouter } from 'next/navigation';

// 注册 Chart.js 组件
Chart.register(...registerables);

export default function Profile() {
  const [updateMessage, setUpdateMessage] = useState<{type: 'success' | 'error', text: string} | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [isProgressLoading, setIsProgressLoading] = useState(true);
  const [progress, setProgress] = useState<{
    basicProblemAcceptCount: number;
    basicProblemTotal: number;
    codingProblemAcceptCount: number;
    codingProblemTotal: number;
  } | null>(null);
  const [studyStat, setStudyStat] = useState<{
    solveProblemCounts: number[];
  } | null>(null);
  const [isStudyStatLoading, setIsStudyStatLoading] = useState(true);
  const [recentSubmissions, setRecentSubmissions] = useState<Submission[]>([]);
  const [isRecentSubmissionsLoading, setIsRecentSubmissionsLoading] = useState(true);

  const dispatch = useAppDispatch();
  // 获取用户资料
  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const response = await ProfileService.getProfile();
        setProfile(response.data);
        dispatch(setUser({
          nickname: response.data.nickname,
          name: response.data.name,
          avatar: response.data.avatar,
          sex: response.data.sex,
          age: response.data.age?.toString() || '',
          currentMembershipLevel: response.data.currentMembershipLevel,
          membershipExpireTime: response.data.membershipExpireTime
        }));
      } catch (error) {
        console.error('获取用户资料失败:', error);
        setUpdateMessage({
          type: 'error',
          text: '获取用户资料失败，请稍后重试'
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchProfile();
  }, []);

  // 获取解题进度
  useEffect(() => {
    const fetchProgress = async () => {
      try {
        const response = await ProfileService.getProgress();
        setProgress(response.data);
      } catch (error) {
        console.error('获取解题进度失败:', error);
      } finally {
        setIsProgressLoading(false);
      }
    };

    fetchProgress();
  }, []);

  // 获取学习记录
  useEffect(() => {
    const fetchStudyStat = async () => {
      try {
        const response = await ProfileService.getStudyStat();
        setStudyStat(response.data);
      } catch (error) {
        console.error('获取学习记录失败:', error);
      } finally {
        setIsStudyStatLoading(false);
      }
    };

    fetchStudyStat();
  }, []);

  // 获取最近提交的题目
  const fetchRecentSubmissions = async () => {
    try {
      const response = await SubmissionService.getSubmissionList({
        type: null,
        status: null,
        difficulty: null,
        timeRange: '',
        keyword: '',
        pageNum: 1,
        pageSize: 5
      });
      console.log(response)
      setRecentSubmissions(response.data.items);
    } catch (error) {
      console.error('获取最近提交记录失败:', error);
    } finally {
      setIsRecentSubmissionsLoading(false);
    }
  };

  // 监听数据加载状态和 DOM 就绪状态，初始化图表
  useEffect(() => {
    // 如果学习记录数据未加载完成，则不执行
    if (isStudyStatLoading || !studyStat) return;

    // 确保在组件挂载后再执行图表初始化
    const initializeCharts = () => {
      // 学习记录图表
      const activityCtx = document.getElementById('activityChart');
      if (!activityCtx) return;

      // 获取最近30天的数据
      const last30DaysData = studyStat.solveProblemCounts;

      // 生成最近30天的日期标签（为了显示效果，每隔几天显示一个日期）
      const dateLabels = Array.from({ length: 30 }, (_, i) => {
        const d = new Date();
        d.setDate(d.getDate() - (29 - i));
        // 只在每5天和最后一天显示日期，其余位置留空以避免标签过多导致拥挤
        return (i % 5 === 0 || i === 29) ? `${d.getMonth() + 1}/${d.getDate()}` : '';
      });

      // 清理现有的图表实例
      const existingActivityChart = Chart.getChart(activityCtx as HTMLCanvasElement);
      if (existingActivityChart) {
        existingActivityChart.destroy();
      }

      // 创建新的图表
      const activityChart = new Chart(activityCtx as HTMLCanvasElement, {
        type: 'line',
        data: {
          labels: dateLabels,
          datasets: [{
            label: '题目完成数',
            data: last30DaysData,
            backgroundColor: 'rgba(99, 102, 241, 0.2)',
            borderColor: 'rgba(99, 102, 241, 1)',
            borderWidth: 2,
            tension: 0.4,
            fill: true
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              grid: {
                display: false
              }
            },
            x: {
              grid: {
                display: false
              }
            }
          },
          plugins: {
            legend: {
              display: false
            }
          }
        }
      });
    };

    // 使用 requestAnimationFrame 确保在浏览器的下一次重绘之前执行
    // 这有助于确保 DOM 元素已经完全渲染
    const rafId = window.requestAnimationFrame(() => {
      // 添加一个小延迟，确保DOM已完全渲染
      setTimeout(initializeCharts, 100);
    });

    // 清理函数
    return () => {
      window.cancelAnimationFrame(rafId);

      // 清理图表实例
      const activityCtx = document.getElementById('activityChart') as HTMLCanvasElement;
      if (activityCtx) {
        const chartInstance = Chart.getChart(activityCtx);
        if (chartInstance) {
          chartInstance.destroy();
        }
      }
    };
  }, [isStudyStatLoading, studyStat]);

  // 添加useEffect
  useEffect(() => {
    fetchRecentSubmissions();
  }, []);

  // 模拟收藏的题单
  const favoriteLists = [
    { id: 1, title: '算法入门必刷题单', problems: 50, completedProblems: 35, category: '基础算法', creator: '官方' },
    { id: 2, title: '字符串处理专题', problems: 30, completedProblems: 12, category: '字符串', creator: '李老师' },
    { id: 3, title: '动态规划入门', problems: 25, completedProblems: 8, category: '动态规划', creator: '官方' }
  ];
  const router = useRouter()

  return (
    <div className="bg-gray-50 min-h-screen py-8">
      <div className="container mx-auto px-4">
        {/* 更新消息提示 */}
        {updateMessage && (
          <div className={`mb-4 p-4 rounded-md ${updateMessage.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            {updateMessage.text}
          </div>
        )}

        {/* 个人信息卡片 */}
        {isLoading ? (
          <ProfileSkeleton />
        ) : profile ? (
          <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
            <div className="md:flex">
              <div className="md:w-1/3 bg-gradient-to-r from-indigo-500 to-purple-600 p-8 text-white">
                <div className="flex flex-col items-center justify-center h-full">
                  <div className="relative">
                    <Image
                      src={'https:' + profile.avatar}
                      alt="用户头像"
                      width={128}
                      height={128}
                      className="w-32 h-32 rounded-full object-cover border-4 border-white"
                    />
                    {profile.currentMembershipLevel > 0 && (
                      <span className="absolute bottom-0 right-0 w-8 h-8 bg-green-500 rounded-full border-4 border-white flex items-center justify-center">
                        <FontAwesomeIcon icon={faCrown} className="text-yellow-300 text-xs" />
                      </span>
                    )}
                  </div>
                  <h2 className="text-2xl font-bold mt-4">{profile.name}</h2>
                  {profile.currentMembershipLevel > 0 && (
                    <span className="bg-white text-indigo-600 px-3 py-1 rounded-full text-sm font-medium mt-2">
                      {profile.currentMembershipLevel === 1 ? '基础会员' : profile.currentMembershipLevel === 2 ? 'Pro会员' : 'Ultra会员'}
                    </span>
                  )}
                  {profile.currentMembershipLevel > 0 && (
                    <p className="mt-4 text-indigo-100 text-center">
                      会员有效期至：{new Date(profile.membershipExpireTime).toLocaleDateString()}
                    </p>
                  )}
                  {profile.currentMembershipLevel > 0 ? (
                    <Link href="/membership" className="mt-6 bg-white text-indigo-600 px-4 py-2 rounded-lg font-medium hover:bg-indigo-50 transition">
                      <FontAwesomeIcon icon={faSyncAlt} className="mr-2" />续费会员
                    </Link>
                  ) : (
                    <Link href="/membership" className="mt-6 bg-white text-indigo-600 px-4 py-2 rounded-lg font-medium hover:bg-indigo-50 transition">
                      <FontAwesomeIcon icon={faCrown} className="mr-2" />开通会员
                    </Link>
                  )}
                </div>
              </div>
              <div className="p-8 md:w-2/3">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-2xl font-bold text-gray-800">个人信息</h3>
                  <Link
                    href="/settings"
                    className="text-indigo-600 hover:text-indigo-800"
                  >
                    <FontAwesomeIcon icon={faEdit} className="mr-1" /> 编辑资料
                  </Link>
                </div>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <p className="text-gray-500 text-sm mb-1">昵称</p>
                    <p className="text-gray-800 font-medium">{profile.nickname}</p>
                  </div>
                  <div>
                    <p className="text-gray-500 text-sm mb-1">姓名</p>
                    <p className="text-gray-800 font-medium">{profile.name}</p>
                  </div>
                  <div>
                    <p className="text-gray-500 text-sm mb-1">手机号</p>
                    <p className="text-gray-800 font-medium">{profile.mobile}</p>
                  </div>
                  <div>
                    <p className="text-gray-500 text-sm mb-1">注册时间</p>
                    <p className="text-gray-800 font-medium">
                      {new Date(profile.registerDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : null}

        {/* 学习数据统计 */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          {/* 解题进度 */}
          {isProgressLoading ? (
            <ProgressSkeleton />
          ) : progress ? (
            <div className="bg-white rounded-xl shadow-md p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-800">解题进度</h3>
              </div>
              <div className="flex items-center space-x-4">
                {/* 计算总体进度百分比 */}
                {(() => {
                  const totalAccepted = progress.basicProblemAcceptCount + progress.codingProblemAcceptCount;
                  const totalProblems = progress.basicProblemTotal + progress.codingProblemTotal;
                  const percentage = totalProblems > 0 ? Math.round((totalAccepted / totalProblems) * 100) : 0;

                  return (
                    <div className="w-20 h-20 relative">
                      <svg className="w-20 h-20" viewBox="0 0 36 36">
                        <path className="stroke-current text-gray-200" strokeWidth="3.8" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                        <path className="stroke-current text-indigo-500" strokeWidth="3.8" fill="none" strokeLinecap="round" strokeDasharray={`${percentage}, 100`} d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                        <text x="18" y="20.5" className="text-indigo-500 font-semibold text-[8px]" textAnchor="middle">{percentage}%</text>
                      </svg>
                    </div>
                  );
                })()}
                <div className="flex-1">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-500 text-sm">基础题</span>
                    <span className="text-gray-800 font-medium">{progress.basicProblemAcceptCount}/{progress.basicProblemTotal}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-1 mb-2">
                    <div
                      className="bg-green-500 h-2 rounded-full"
                      style={{ width: `${progress.basicProblemTotal > 0 ? (progress.basicProblemAcceptCount / progress.basicProblemTotal) * 100 : 0}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-500 text-sm">编程题</span>
                    <span className="text-gray-800 font-medium">{progress.codingProblemAcceptCount}/{progress.codingProblemTotal}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                    <div
                      className="bg-blue-500 h-2 rounded-full"
                      style={{ width: `${progress.codingProblemTotal > 0 ? (progress.codingProblemAcceptCount / progress.codingProblemTotal) * 100 : 0}%` }}
                    ></div>
                  </div>
                </div>
              </div>
              <div className="mt-4 pt-4 border-t border-gray-100">
                <span className="text-indigo-600 hover:text-indigo-800 text-sm font-medium flex items-center justify-center cursor-pointer" onClick={() => {
                  if (checkAuth(3)) {
                    router.push('/progress')
                  }
                }}>
                  查看详细进度分析 <FontAwesomeIcon icon={faChevronRight} className="ml-1 text-xs" />
                </span>
              </div>
            </div>
          ) : null}

          {/* 学习记录 - 占用两格 */}
          {isProgressLoading || isStudyStatLoading ? (
            <div className="md:col-span-2">
              <ProgressSkeleton />
            </div>
          ) : studyStat ? (
            <div className="bg-white rounded-xl shadow-md p-6 md:col-span-2">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-800">学习记录</h3>
                <span className="text-xs text-gray-500">最近30天</span>
              </div>
              <div className="h-40 mb-4">
                <canvas id="activityChart"></canvas>
              </div>
              <div className="flex justify-between">
                <div className="text-center">
                  <div className="text-2xl font-bold text-indigo-600">
                    {studyStat.solveProblemCounts.reduce((sum, count) => sum + count, 0)}
                  </div>
                  <div className="text-xs text-gray-500">近30天解题</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-indigo-600">
                    {studyStat.solveProblemCounts.filter(count => count > 0).length}
                  </div>
                  <div className="text-xs text-gray-500">学习天数</div>
                </div>
                {/* 计算最大连续学习天数 */}
                <div className="text-center">
                  <div className="text-2xl font-bold text-indigo-600">
                    {(() => {
                      let maxStreak = 0;
                      let currentStreak = 0;

                      for (const count of studyStat.solveProblemCounts) {
                        if (count > 0) {
                          currentStreak++;
                          maxStreak = Math.max(maxStreak, currentStreak);
                        } else {
                          currentStreak = 0;
                        }
                      }

                      return maxStreak;
                    })()}
                  </div>
                  <div className="text-xs text-gray-500">最大连续</div>
                </div>
              </div>
            </div>
          ) : null}
        </div>

        {/* 快捷导航卡片 */}
        <div className="mb-6">
          <Link href="/profile/favorites" className="group">
            <div className="bg-white rounded-xl shadow-md p-5 hover:shadow-lg transition flex items-center">
              <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4 group-hover:bg-blue-200 transition">
                <FontAwesomeIcon icon={faBookmark} className="text-blue-600 text-xl" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-800 mb-1">我的收藏</h3>
                <p className="text-sm text-gray-500">查看已收藏的题目</p>
              </div>
              <FontAwesomeIcon icon={faChevronRight} className="text-gray-400 group-hover:text-gray-600 transition" />
            </div>
          </Link>
        </div>

        {/* 最近提交的题目 */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100 flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-800">最近提交的题目</h3>
            <Link href="/profile/submissions" className="text-sm text-indigo-600 hover:text-indigo-800">
              查看全部
            </Link>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {isRecentSubmissionsLoading ? (
                <div className="flex justify-center items-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-indigo-600"></div>
                </div>
              ) : recentSubmissions.length > 0 ? (
                recentSubmissions.map(submission => (
                  <div key={submission.problemId} className="flex items-center">
                    <div className={`w-14 h-14 rounded-lg flex items-center justify-center flex-shrink-0 ${
                      submission.status === 0 ? 'bg-green-100' : 'bg-red-100'
                    }`}>
                      <span className={`font-bold ${
                        submission.status === 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {submission.status === 0 ? '100' : '0'}
                      </span>
                    </div>
                    <div className="ml-4 flex-1">
                      <div className="flex justify-between items-center">
                        <h4 className="text-gray-800 font-medium">{submission.title}</h4>
                        <span className="text-xs text-gray-500">{submission.submitTime}</span>
                      </div>
                      <div className="flex items-center mt-1">
                        <span className={`text-xs rounded-full px-2 py-0.5 mr-2 ${
                          submission.type === 2 ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'
                        }`}>
                          {submission.type === 2 ? '编程题' : '基础题'}
                        </span>
                        <span className={`text-xs rounded-full px-2 py-0.5 ${
                          submission.difficulty === 1 ? 'bg-green-100 text-green-800' :
                          submission.difficulty === 2 ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {submission.difficulty === 1 ? '简单' :
                           submission.difficulty === 2 ? '中等' : '困难'}
                        </span>
                      </div>
                    </div>
                    <div className="ml-4">
                      <FontAwesomeIcon
                        icon={submission.status === 0 ? faCheckCircle : faTimesCircle}
                        className={submission.status === 0 ? 'text-green-500' : 'text-red-500'}
                      />
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center text-gray-500 py-4">
                  暂无提交记录
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 收藏的题单 */}
        {/*<div className="bg-white rounded-xl shadow-md overflow-hidden mt-4">*/}
        {/*  <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100 flex justify-between items-center">*/}
        {/*    <h3 className="text-lg font-semibold text-gray-800">收藏的题单</h3>*/}
        {/*    <Link href="/profile/favorites" className="text-sm text-indigo-600 hover:text-indigo-800">*/}
        {/*      查看全部*/}
        {/*    </Link>*/}
        {/*  </div>*/}
        {/*  <div className="p-6">*/}
        {/*    <div className="space-y-4">*/}
        {/*      {favoriteLists.map(list => (*/}
        {/*        <div key={list.id} className="p-4 border border-gray-100 rounded-lg hover:shadow-md transition">*/}
        {/*          <div className="flex justify-between items-center">*/}
        {/*            <h4 className="text-gray-800 font-medium">{list.title}</h4>*/}
        {/*            <span className="text-xs text-indigo-600 bg-indigo-50 rounded-full px-2 py-0.5">{list.category}</span>*/}
        {/*          </div>*/}
        {/*          <div className="flex justify-between items-center mt-3">*/}
        {/*            <div className="flex items-center text-sm text-gray-500">*/}
        {/*              <FontAwesomeIcon icon={faBook} className="mr-1 text-xs" />*/}
        {/*              <span>{list.problems}题</span>*/}
        {/*              <FontAwesomeIcon icon={faUser} className="ml-3 mr-1 text-xs" />*/}
        {/*              <span>{list.creator}</span>*/}
        {/*            </div>*/}
        {/*            <div className="text-sm">*/}
        {/*              <span className="text-indigo-600 font-medium">{list.completedProblems}/{list.problems}</span>*/}
        {/*            </div>*/}
        {/*          </div>*/}
        {/*          <div className="w-full bg-gray-200 rounded-full h-1.5 mt-2">*/}
        {/*            <div*/}
        {/*              className="bg-indigo-500 h-1.5 rounded-full"*/}
        {/*              style={{ width: `${(list.completedProblems / list.problems) * 100}%` }}*/}
        {/*            ></div>*/}
        {/*          </div>*/}
        {/*        </div>*/}
        {/*      ))}*/}
        {/*    </div>*/}
        {/*  </div>*/}
        {/*</div>*/}
      </div>
    </div>
  );
}

