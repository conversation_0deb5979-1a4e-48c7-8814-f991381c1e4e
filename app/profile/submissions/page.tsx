'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faCheckCircle, faTimesCircle, faSpinner, faInfoCircle, faFilter, faSearch } from '@fortawesome/free-solid-svg-icons'
import Pagination from '../../components/Pagination'
import { JUDGE_STATUS } from '../../utils/constant'
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, ArcElement, Filler } from 'chart.js'
import { Line } from 'react-chartjs-2'
import CommonDropdown, { DropdownOption } from '../../components/CommonDropdown'
import SubmissionDetailModal from '../../components/Modal/SubmissionDetailModal'
import submissionService, { Submission, SubmissionStatistic } from '../../service/submission-service'

// 注册Chart.js组件
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, ArcElement, Filler)

interface SubmissionRecord {
  id: string
  problemId: string
  problemTitle: string
  type: number
  subType: number
  difficulty: number
  submitTime: string
  language: string
  status: number
  score: number
  time: number
  memory: number
  code: string
}

export default function SubmissionsPage() {
  // 筛选器状态
  const [problemType, setProblemType] = useState('all')
  const [statusFilter, setStatusFilter] = useState<number | null>(null)
  const [difficultyFilter, setDifficultyFilter] = useState<number | null>()
  const [timeRange, setTimeRange] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [totalItems, setTotalItems] = useState(0)

  // 数据状态
  const [isLoading, setIsLoading] = useState(false)
  const [submissions, setSubmissions] = useState<SubmissionRecord[]>([])

  // 添加提交详情模态框状态
  const [selectedSubmission, setSelectedSubmission] = useState<SubmissionRecord | null>(null)
  const [showSubmissionModal, setShowSubmissionModal] = useState(false)

  // 添加一个状态用于跟踪模态框内的选项卡
  const [modalActiveTab, setModalActiveTab] = useState('info')

  // 修改状态数据
  const [stats, setStats] = useState<SubmissionStatistic>({
    submissionCount: 0,
    acceptCount: 0,
    solvedProblems: 0,
    submissionData: [],
    acceptData: [],
    chartDates: []
  })

  // 修改趋势图数据
  const [trendData, setTrendData] = useState({
    labels: [] as string[],
    datasets: [
      {
        label: '总提交',
        data: [] as number[],
        borderColor: 'rgb(79, 70, 229)',
        backgroundColor: 'rgba(79, 70, 229, 0.1)',
        borderWidth: 2,
        fill: true,
        tension: 0.3
      },
      {
        label: '通过',
        data: [] as number[],
        borderColor: 'rgb(16, 185, 129)',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        borderWidth: 2,
        fill: true,
        tension: 0.3
      }
    ]
  })

  // 图表配置
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)'
        }
      },
      x: {
        grid: {
          display: false
        }
      }
    }
  }

  // 添加获取统计数据的函数
  const fetchStatistics = async () => {
    try {
      const response = await submissionService.getStatistic(
        problemType === 'all' ? null : (problemType === 'programming' ? 2 : 1)
      )
      setStats(response.data)

      // 更新趋势图数据
      setTrendData(prev => ({
        ...prev,
        labels: response.data.chartDates,
        datasets: [
          {
            ...prev.datasets[0],
            data: response.data.submissionData
          },
          {
            ...prev.datasets[1],
            data: response.data.acceptData
          }
        ]
      }))
    } catch (error) {
      console.error('获取统计数据失败:', error)
    }
  }

  // 修改useEffect，添加统计数据的获取
  useEffect(() => {
    fetchStatistics()
  }, [problemType])

  // 修改提交记录获取逻辑
  useEffect(() => {
    const fetchSubmissions = async () => {
      setIsLoading(true)
      try {
        const response = await submissionService.getSubmissionList({
          type: problemType === 'all' ? null : (problemType === 'programming' ? 2 : 1),
          difficulty: difficultyFilter,
          status: statusFilter,
          timeRange: timeRange === 'all' ? '' : timeRange,
          keyword: searchQuery,
          pageNum: currentPage,
          pageSize: pageSize
        })

        // 转换接口返回的数据为SubmissionRecord类型
        const records: SubmissionRecord[] = response.data.items.map((item: Submission) => ({
          id: item.problemId,
          problemId: item.problemId,
          problemTitle: item.title,
          type: item.type,
          subType: item.subType,
          difficulty: item.difficulty,
          submitTime: item.submitTime,
          language: item.language || item.options,
          status: item.status,
          score: item.status === 0 ? 100 : 0,
          time: item.time,
          memory: item.memory,
          code: item.code
        }))

        setSubmissions(records)
        setTotalItems(response.data.total)
      } catch (error) {
        console.error('获取提交记录失败:', error)
        setSubmissions([])
        setTotalItems(0)
      } finally {
        setIsLoading(false)
      }
    }

    fetchSubmissions()
  }, [currentPage, pageSize, problemType, statusFilter, difficultyFilter, timeRange, searchQuery])

  // 获取状态文本和样式
  const getStatusText = (status: number) => {
    // 确保将数字转换为字符串作为索引，并使用类型断言
    const statusKey = String(status)
    return (JUDGE_STATUS as any)[statusKey]?.name || '未知状态'
  }

  const getStatusStyle = (status: number) => {
    switch (status) {
      case 0:
        return 'bg-green-100 text-green-800'
      case 8:
        return 'bg-blue-100 text-blue-800'
      case 5:
      case 6:
      case 7:
      case 9:
        return 'bg-yellow-100 text-yellow-800'
      case -1:
      case 1:
      case 2:
      case 3:
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-600'
    }
  }

  // 获取题目类型文本和样式
  const getProblemTypeText = (type: number, subType: number) => {
    if (type === 2) {
      return '编程题'
    }

    switch (subType) {
      case 1:
        return '单选题'
      case 2:
        return '多选题'
      case 3:
        return '判断题'
      default:
        return '未知类型'
    }
  }

  const getProblemTypeStyle = (type: number, subType: number) => {
    if (type === 2) {
      return 'bg-blue-100 text-blue-800'
    }
    switch (subType) {
      case 1:
        return 'bg-purple-100 text-purple-800'
      case 2:
        return 'bg-pink-100 text-pink-800'
      case 3:
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // 获取难度文本和样式
  const getDifficultyText = (difficulty: number) => {
    switch (difficulty) {
      case 1:
        return '简单'
      case 2:
        return '中等'
      case 3:
        return '困难'
      default:
        return '未知'
    }
  }

  const getDifficultyStyle = (difficulty: number) => {
    switch (difficulty) {
      case 1:
        return 'bg-green-100 text-green-800'
      case 2:
        return 'bg-yellow-100 text-yellow-800'
      case 3:
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // 格式化显示内存
  const formatMemory = (memory: number) => {
    if (memory === 0) return '-'
    if (memory < 1024) return `${memory}KB`
    return `${(memory / 1024).toFixed(2)}MB`
  }

  // 格式化显示时间
  const formatTime = (time: number) => {
    if (time < 1000) return `${time}ms`
    return `${(time / 1000).toFixed(2)}s`
  }

  // 页面大小变化处理
  const handlePageSizeChange = (size: number) => {
    setPageSize(size)
    setCurrentPage(1)
  }

  // 修改处理函数，区分查看详情和查看代码
  const handleViewDetails = (submission: SubmissionRecord, tab: 'info' | 'code' = 'info') => {
    setSelectedSubmission(submission)
    setModalActiveTab(tab)
    setShowSubmissionModal(true)
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 顶部标题和筛选器 */}
      <div className="mb-8 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">题目提交记录</h1>
          <p className="text-gray-600 mt-1">查看所有编程题和基础题的提交历史</p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-2">
          <button
            className={`px-4 py-2 rounded-lg text-sm ${problemType === 'all' ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700 border border-gray-300'}`}
            onClick={() => setProblemType('all')}
          >
            全部记录
          </button>
          <button
            className={`px-4 py-2 rounded-lg text-sm ${problemType === 'programming' ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700 border border-gray-300'}`}
            onClick={() => setProblemType('programming')}
          >
            编程题
          </button>
          <button
            className={`px-4 py-2 rounded-lg text-sm ${problemType === 'basic' ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700 border border-gray-300'}`}
            onClick={() => setProblemType('basic')}
          >
            基础题
          </button>
        </div>
      </div>

      {/* 提交统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-3xl font-bold text-indigo-600">{stats.submissionCount}</div>
              <div className="text-sm text-gray-500 mt-1">总提交次数</div>
            </div>
            <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center">
              <FontAwesomeIcon icon={faInfoCircle} className="text-indigo-600 text-xl" />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-3xl font-bold text-green-600">{stats.acceptCount}</div>
              <div className="text-sm text-gray-500 mt-1">通过次数</div>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
              <FontAwesomeIcon icon={faCheckCircle} className="text-green-600 text-xl" />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-3xl font-bold text-blue-600">
                {stats.submissionCount > 0 ? Math.round((stats.acceptCount / stats.submissionCount) * 100) : 0}%
              </div>
              <div className="text-sm text-gray-500 mt-1">通过率</div>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <FontAwesomeIcon icon={faCheckCircle} className="text-blue-600 text-xl" />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-3xl font-bold text-yellow-600">{stats.solvedProblems}</div>
              <div className="text-sm text-gray-500 mt-1">已解决题目</div>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
              <FontAwesomeIcon icon={faCheckCircle} className="text-yellow-600 text-xl" />
            </div>
          </div>
        </div>
      </div>

      {/* 提交记录走势图 */}
      <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
        <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100">
          <h3 className="text-lg font-semibold text-gray-800">近期提交情况</h3>
        </div>
        <div className="p-6">
          <div className="h-64">
            <Line options={chartOptions} data={trendData} />
          </div>
        </div>
      </div>

      {/* 筛选选项 */}
      <div className="bg-white rounded-xl shadow-md p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div>
            <label className="block text-gray-700 mb-2">状态</label>
            <CommonDropdown
              label="状态"
              options={[
                { label: '全部状态', value: null },
                { label: '通过', value: 0 },
                { label: '未通过', value: -1 },
                { label: '部分通过', value: 8 },
                { label: '编译错误', value: -2 },
                { label: '运行时错误', value: 3 },
                { label: '超时', value: 1 }
              ]}
              value={statusFilter}
              onChange={(value) => setStatusFilter(value as number)}
              placeholder="请选择状态"
            />
          </div>
          <div>
            <label className="block text-gray-700 mb-2">难度</label>
            <CommonDropdown
              label="难度"
              options={[
                { label: '全部难度', value: null },
                { label: '简单', value: 1 },
                { label: '中等', value: 2 },
                { label: '困难', value: 3 }
              ]}
              value={difficultyFilter}
              onChange={(value) => setDifficultyFilter(value as number)}
              placeholder="请选择难度"
            />
          </div>
          <div>
            <label className="block text-gray-700 mb-2">时间范围</label>
            <CommonDropdown
              label="时间范围"
              options={[
                { label: '全部时间', value: 'all' },
                { label: '今天', value: 'today' },
                { label: '本周', value: 'week' },
                { label: '本月', value: 'month' },
                { label: '最近三个月', value: 'quarter' }
              ]}
              value={timeRange}
              onChange={(value) => setTimeRange(value as string)}
              placeholder="请选择时间范围"
            />
          </div>
          <div>
            <label className="block text-gray-700 mb-2">题目</label>
            <div className="relative">
              <input
                type="text"
                placeholder="搜索题目名称、ID..."
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <button className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-indigo-600">
                <FontAwesomeIcon icon={faSearch} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 标签页切换 */}
      <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
        {/*<div className="border-b border-gray-200">*/}
        {/*  <nav className="-mb-px flex">*/}
        {/*    <a href="#" className="border-indigo-500 text-indigo-600 whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm">*/}
        {/*      全部提交*/}
        {/*    </a>*/}
        {/*    <a href="#" className="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm">*/}
        {/*      编程题提交*/}
        {/*    </a>*/}
        {/*    <a href="#" className="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm">*/}
        {/*      基础题提交*/}
        {/*    </a>*/}
        {/*  </nav>*/}
        {/*</div>*/}

        {/* 提交记录表格 */}
        <div className="overflow-x-auto">
          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <div className="text-center">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600 mb-4"></div>
                <p className="text-gray-700 font-medium">正在加载提交记录...</p>
              </div>
            </div>
          ) : (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">题目ID</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">题目</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">题目类型</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">难度</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">提交时间</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">语言/选项</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {submissions.map((submission) => (
                  <tr key={submission.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {submission.id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Link
                        href={`/${submission.type === 2 ? 'coding-problems' : 'basic-problems'}/${submission.problemId}`}
                        className="text-indigo-600 hover:text-indigo-900 font-medium"
                      >
                        {submission.problemTitle}
                      </Link>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getProblemTypeStyle(submission.type, submission.subType)}`}>
                        {getProblemTypeText(submission.type, submission.subType)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getDifficultyStyle(submission.difficulty)}`}>
                        {getDifficultyText(submission.difficulty)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {submission.submitTime}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {submission.language}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusStyle(submission.status)}`}>
                        {getStatusText(submission.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      {submission.type === 2 && (
                        <button
                          onClick={() => handleViewDetails(submission, 'code')}
                          className="text-gray-600 hover:text-gray-900"
                        >
                          查看代码
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>

        {/* 分页 */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:text-gray-500"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              上一页
            </button>
            <button
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:text-gray-500"
              onClick={() => setCurrentPage(prev => prev + 1)}
              disabled={currentPage * pageSize >= totalItems}
            >
              下一页
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                显示第 <span className="font-medium">{Math.min((currentPage - 1) * pageSize + 1, totalItems)}</span> 到 <span className="font-medium">{Math.min(currentPage * pageSize, totalItems)}</span> 条，共 <span className="font-medium">{totalItems}</span> 条结果
              </p>
            </div>
            <div>
              <Pagination
                currentPage={currentPage}
                totalItems={totalItems}
                pageSize={pageSize}
                onPageChange={setCurrentPage}
              />
            </div>
          </div>
        </div>
      </div>

      {/* 添加模态框组件 */}
      {showSubmissionModal && selectedSubmission && (
        <SubmissionDetailModal
          submission={selectedSubmission as any}
          onClose={() => setShowSubmissionModal(false)}
        />
      )}
    </div>
  )
}
