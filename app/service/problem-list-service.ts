import request from '../utils/axios'
import {PageResult} from "@/app/model/models";

export interface ProblemList {
	id: number;
	/**
	 * 题单名称
	 */
	name: string;
	/**
	 * 题单描述
	 */
	description: string;
	/**
	 * 题目数量
	 */
	problemCount: number;
	/**
	 * 学习人数
	 */
	studyUserAmt: number;
	/**
	 * 难度 1: 入门、2: 基础、3: 中等、4: 进阶、5: 挑战
	 */
	difficulty: number;

	/**
	 * 背景 className
	 */
	backgroundColor: string;

	/**
	 * 是否官方题单
	 */
	isOfficial: number;

	/**
	 * 是否推荐
	 */
	isRecommend: number;

	/**
	 * 是否AI生成
	 */
	isAigc: number;

	/**
	 * 创建时间
	 */
	createdTime: string;

	/**
	 * 预计用时（分钟）
	 */
	estimatedTime: number;
}

export interface UserProblemListProgress {
	startTime: string;
	finishTime: string;
	status: number;
	consecutiveStudyDays: number;
	maxConsecutiveStudyDays: number;
}
export interface UserProblemListProgressDetail {
	problemListProblemId: number;
	status: number;
	useTime: number;
	startTime: string;
	finishTime: string;
	tryCount: number;
}
export interface ProblemListProblem {
	pid: string;
	problemListProblemId: number;
	title: string;
	difficulty: number;
	passRate: number;
	description: string;
	orderNum: number;
	problemType: number;
	completeCount: number;
}

export interface ProblemListDetail extends ProblemList{
	progress: UserProblemListProgress;
	progressDetails: UserProblemListProgressDetail[];
	problems: ProblemListProblem[];
}

class ProblemListService {
	getList(params: any) {
		return request.get<PageResult<ProblemList>>('/problem/list', { params })
	}

	getDetail(id: string) {
		return request.get<ProblemListDetail>('/problem/list/' + id)
	}
	/**
	 * 生成题单
	 * @returns 
	 */
	generateProblemList() {
		return request.get('/problem/list/generate')
	}
	/**
	 * 获取题单生成状态
	 * @returns 0: 正在生成 其他: 题单id
	 */
	getProblemListGenerateStatus() {
		return request.get<number>('/problem/list/generate/status')
	}
}

export default new ProblemListService()
