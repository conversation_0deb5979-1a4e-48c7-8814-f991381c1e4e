import request from '../utils/axios'
import {Tag} from "@/app/model/models";

export interface LearningContextProblem {
	title: string;
	id: string | number;
	type: number;
}

export interface LearningContext {
	previousQuestion?: LearningContextProblem;
	nextQuestion?: LearningContextProblem;
	envType?: string;
	envId?: number;
	envTitle?: string;
}

export interface BaseProblemView {

	/**
	 * 题目id
	 */
	displayId: string;

	/**
	 * 标题
	 */
	title: string;

	/**
	 * 类型 1: 基础题 2: 编程题
	 */
	problemType: number;

	/**
	 * 难度
	 */
	difficulty: number;

	/**
	 * 标签列表
	 */
	tags: Tag[];
}

export interface PanelQuestionListView {
	// 总题数
	total: number;

	// 是否有下一页
	hasMore: boolean;

	// 题目列表
	problems: BaseProblemView[];
}

export interface LearningContextFilter {
	// 题目
	keyword?: string;

	// 难度
	difficulty?: number;

	// 状态
	status?: number;

	// 标签列表
	tags?: number[];

	// 页数
	pageNum: number;

	// 每页条数
	pageSize: number;
}
export interface LearningContextParams {
	// 当前题目id
	currentQuestion: string | number;

	// 当前题目类型 1: 基础题 2: 编程题
	currentQuestionType: number;

	envType?: string;

	envId?: number;

	filter: LearningContextFilter
}

class LearningContextService {
	query(data: any) {
		return request.post<LearningContext>('/learningContext/query', data)
	}

	getPanelQuestionList(data: LearningContextParams) {
		return request.post('/learningContext/problemsetPanelQuestionList', data)
	}
}

export default new LearningContextService()
