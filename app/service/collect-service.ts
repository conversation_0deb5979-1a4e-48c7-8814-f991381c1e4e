import request from '../utils/axios';
import {Tag} from "@/app/model/models";

export interface CollectProblem {
	id: number;
	title: string;
	categoryName: string;
	difficulty: number;
	displayId: number;
	problemType: number;
	tags: Tag[]
}
export interface CollectData {
	id: number;
	dataId: number;
	dataType: number;
	problem: CollectProblem;
}

class CollectService {
	/**
	 * 收藏
	 * @param dataType 类型 2：编程题、3：基础题
	 * @param dataId   id
	 */
	collect(dataType: number, dataId: number) {
		return request.post(`/user/collect/${dataType}/${dataId}`)
	}

	/**
	 * 取消收藏
	 *
	 * @param dataType 类型 2：编程题、3：基础题
	 * @param dataId   id
	 */
	cancelCollect(dataType: number, dataId: number) {
		return request.delete(`/user/collect/${dataType}/${dataId}`)
	}

	/**
	 * 获取收藏列表
	 *
	 * @param dataType  类型 2：编程题、3：基础题
	 * @param params    分页参数
	 */
	getList(dataType: number, params: {
		pageNum: number,
		pageSize: number
	}) {
		return request.get<CollectData[]>(`/user/collect/list/${dataType}`, { params })
	}
}

export default new CollectService();
