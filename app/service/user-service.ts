import request from '../utils/axios'
import {UserInfo} from "@/app/redux/features/authSlice";

export interface WechatLoginTicket {
    /**
     * 凭证，用于获取扫码状态
     */
    ticket: string;

    /**
     * 有效期秒数
     */
    expire_seconds: number;

    /**
     * 二维码URL
     */
    url: string;
}

export interface WechatLoginStatus {
    /**
     * 状态 0: 待扫码 1: 登录成功
     */
    status: number;

    /**
     * 登录成功的用户信息 status=1时不为空
     */
    user?: UserInfo;

    /**
     * 是否已绑定手机号 status=1时不为空
     */
    hasBindPhone?: boolean;
}

class UserService {


    login(data: any) {
        return request.post<UserInfo>('/user/login', data)
    }

    logout() {
        return request.post('/user/logout')
    }

    register(data: any) {
        return request.post('/user/register', data)
    }

    /**
     * 获取微信登录二维码
     */
    getWechatLoginTicket() {
        return request.get<WechatLoginTicket>('/user/wechat/login/ticket')
    }

    /**
     * 获取微信登录扫码状态
     *
     * @param ticket getWechatLoginTicket接口返回的ticket
     */
    getWechatLoginStatus(ticket: string) {
        return request.get<WechatLoginStatus>('/user/wechat/login/status', { params: { ticket }})
    }
}

export default new UserService()
