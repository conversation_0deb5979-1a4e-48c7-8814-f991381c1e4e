import request from '../utils/axios'

class MessageService {

	/**
	 * 发送验证码（未登录）
	 * @param phone
	 */
	sendVerifyCode(phone: string, template: 0 | 1 = 0) {
		return request.get('/message/verifyCode', { params: { cellPhone: phone, template: template }});
	}

	/**
	 * 发送邮箱验证码（未登录）
	 * @param email
	 */
	sendEmailVerifyCodeNotLogin(email: string) {
		return request.get('/message/email/verifyCode', { params: { email: email }});
	}

	/**
	 * 发送验证码（已登录）
	 * @param phone 手机号（验证当前手机号时不传）
	 */
	sendAuthVerifyCode(phone?: string) {
		return request.get('/message/verifyCode/auth', { params: { cellPhone: phone }});
	}

	/**
	 * 发送邮箱验证码（已登录）
	 * @param email 邮箱
	 */
	sendEmailVerifyCode(email: string) {
		return request.get('/message/email/verifyCode/auth', { params: { email: email }});
	}
}

export default new MessageService();
