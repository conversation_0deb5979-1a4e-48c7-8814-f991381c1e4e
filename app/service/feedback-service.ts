import request from "../utils/axios";

class FeedbackService {
    feedback(data: {
        path: string;
        type: string;
        content: string;
    }) {
        return request.post('/feedback', data);
    }

    /**
     * 发送消息
     * 
     * @param data 
     * @returns 
     */
    sendMessage(data: {
        name: string;
        contact: string;
        type: string;
        title: string;
        content: string;
    }) {
        return request.post('/feedback/message', data);
    }
}

export default new FeedbackService();