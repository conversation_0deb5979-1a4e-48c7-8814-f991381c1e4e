import request from '../utils/axios'
import {Tag} from "@/app/model/models";

export interface WrongProblemView {
	// ID
	id: number;
	// 题目ID
	displayId: string;
	// 题目类型
	problemType: number;
	// 题目
	title: string;
	// 上次错误时间
	lastWrongTime: string;
	// 难度 1: 简单 2: 中等 3: 困难
	difficulty: number;
	// 状态 1: 待巩固 2: 已巩固
	status: number;
	// 标签
	tags: Tag[];
	// 错误次数
	wrongCount?: number;
}

// 错题单列表响应结构
export interface WrongProblemListResponse {
	items: WrongProblemView[];
	total: number;
	pendingCount?: number;
	masteredCount?: number;
	thisMonthCount?: number;
}

export interface StatisticData {
	total: number;
	pending: number;
	mastered: number;
	thisMonth: number;
	tagCounts: TagCount[];
}

export interface TagCount {
	name: string;
	count: number;
}

class WrongProblemService {

	/**
	 * 获取错题单列表
	 *
	 * @param data
	 */
	fetchList(data: {
		// 页数
		pageNum: number;
		// 每页条数
		pageSize: number;
		// 题目类型 1：基础题 2：编程题 不传查全部
		problemType?: number;
		// 题目标题
		title?: number;
		// 状态 1：待巩固 2：已巩固
		status?: number;
		// 开始时间
		startTime?: Date;
		// 结束时间
		endTime?: Date;
		// 排序 error_count_desc: 错误次数降序 last_wrong_time_desc: 最近错误
		sort?: string;
	}) {
		return request.post<WrongProblemListResponse>('/wrong-problem/list', data)
	}

	/**
	 * 更新状态
	 *
	 * @param id 错题ID
	 * @param status 状态 1：待巩固 2：已巩固
	 */
	updateStatus(id: number, status: 1 | 2) {
		return request.put(`/wrong-problem/${id}/status/${status}`)
	}

	/**
	 * 获取统计数据
	 *
	 * @returns
	 */
	getStatisticData(params: { problemType?: number } = {}) {
		return request.get<StatisticData>('/wrong-problem/statistic', { params })
	}
}

export default new WrongProblemService();
