import request from '../utils/axios';
import {ProblemDetail} from "@/app/service/coding-problem-service";
import {PageResult} from "@/app/model/models";
import {JudgeResult} from "@/app/service/judge-service";


export interface Exam {
	id: number;
	banner: string;
	title: string;
	duration: number;
	difficulty: number;
	categoryName: string;
	participants: number;
	description: string;
}
export interface Option {
	id: string;
	type?: number;
	content?: string;
	img?: string;
	text?: string;
}

export interface ExamProblem {
	id: number;
	recordId: number;
	problemId: number;
	title: string;
	description: string;
	input: string;
	output: string;
	examples: string;
	hint: string;
	options: string;
	optionArray: Option[];
	problemType: number;
	orderNum: number;
	languages: string[];
	language: string;
	value: string;
	score: number;
	userScore: number;
	rightAnswer: string;
	isAc: number;
	judgeStatus: number;
	explanations: string;
	no: number;
	judge: JudgeResult;
	analysisStatus?: number;
	tags?: string[];
}

export interface ExamDetail {
	id: number;
	title: string;
	duration: number;
	difficulty: number;
	categoryName: string;
	singleChoiceScore: number;
	multipleChoiceScore: number;
	trueOrFalseScore: number;
	ojScore: number;
	multipleChoiceList: ExamProblem[];
	singleChoiceList: ExamProblem[];
	trueOrFalseList: ExamProblem[];
	ojList: ExamProblem[];
	status: number;
	userTestPaperRecordId: number;
	startTime: number;
	endTime: string;
	banner: string;
	totalScore: number;
}

export interface ExamRecord {
	id: number;
	testPaperId: number;
	banner: string;
	title: string;
	categoryName: string;
	difficulty: number;
	score: number;
	totalScore: number;
	startTime: string;
	submitTime: string;
	useTime: number;
	status: number;
}

export interface RecordDetail extends ExamRecord{
	detail: ExamDetail;
}

export interface ExamStat {

	/**
	 * 考试总数
	 */
	examCount: number;

	/**
	 * 平均得分率
	 */
	scoringRate: number;

	/**
	 * 最高分数
	 */
	maxScore: number;

	/**
	 * 累计学时
	 */
	useHours: number;

	/**
	 * 考试成绩走势
	 */
	recentlyExam: RecentlyExam[];
}

export interface RecentlyExam {

	/**
	 * 日期
	 */
	date: Date;

	/**
	 * 得分率
	 */
	scoringRate: number;
}
export interface ErrorAnalysis {
	/**
	 * 错误类型
	 */
	error_type: string;
	/**
	 * 错误位置
	 */
	error_location: string;
	/**
	 * 原因
	 */
	root_cause: string;
}

/**
 * 修改建议
 */
export interface ModificationSuggestion {
	/**
	 * 行号
	 */
	line_number: number;
	/**
	 * 原代码
	 */
	original_code: string;
	/**
	 * 修改后代码
	 */
	modified_code: string;
	/**
	 * 解释
	 */
	explanation: string;
}
export interface Analysis {
	/**
	 * 错误分析
	 */
	error_analysis: ErrorAnalysis;
	/**
	 * 修改建议
	 */
	modification_suggestions: ModificationSuggestion[];
	/**
	 * 预防提示
	 */
	prevention_tips: string[];
	/**
	 * Python代码
	 */
	pythonCode: string;
	/**
	 * C++代码
	 */
	cppCode: string;
}

export interface WrongAnalysisView {
	// 题目标题
	title: string;
	// 题目描述
	description: string;
	// 输入描述
	input: string;
	// 输出描述
	output: string;
	// 输如输出样例
	examples: string;
	// 提示
	hint: string;
	// 用户提交的语言
	language: string;
	// 用户提交的代码
	code: string;
	// 错误分析
	analysis: Analysis;
}

class ExamService {
	/**
	 * 获取考试列表
	 * @param data 请求参数
	 * @returns 考试列表
	 */
	getExamList(data: {
		// 页码
		pageNum: number;
		// 每页条数
		pageSize: number;
		// 分类ID
		categoryId?: number;
		// 难度 1: 入门级 2: 基础级 3: 提高级 4: 竞赛级
		difficulty?: number;
		// 时长 1: 1小时以内 2: 1-2小时 3: 2-3小时 4: 3小时以上
		duration?: number;
		// 关键字
		keyword?: string;
	}) {
		return request.post('/exam/list', data)
	}

	getExamDetail(id: string) {
		return request.get<ExamDetail>('/exam/' + id)
	}

	saveAnswer(data: any) {
		return request.post('/exam/problem/submit', data)
	}

	submit(id: any) {
		return request.post('/exam/' + id)
	}

	getRecords(params: any) {
		return request.get<PageResult<ExamRecord>>('/exam/record/list', { params })
	}

	getRecordDetail(id: any) {
		return request.get<RecordDetail>(`/exam/record/${id}`)
	}

	getExamStat() {
		return request.get<ExamStat>('/exam/stat')
	}

	/**
	 * 获取错题分析数据
	 *
	 * @param id
	 */
	getWrongAnalysis(id: number) {
		return request.get<WrongAnalysisView>(`/exam/analysis/${id}`)
	}
}

export default new ExamService()
