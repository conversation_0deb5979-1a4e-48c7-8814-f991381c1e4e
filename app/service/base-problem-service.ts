import request from '../utils/axios'

interface Category {
    id: number;
    name: string;
}
interface Tag {
    id: number;
    name: string;
}
export interface Option {
    id: string;
    type?: number;
    content?: string;
    text?: string;
    img?: string;
}
export interface Interact {
    commitAmt: number;
    commitUserAmt: number;
    acceptAmt: number;
    acceptUserAmt: number;
}
export interface Problem {
    id: number;
    question: string;
    description: string;
    difficulty: number;
    categoryView: Category;
    categoryName: string;
    tagViews: Tag[];
    type: number;
    optionsArray: Option[];
    options: string;
    correctAnswer: string | string[];
    interact: Interact;
    isCollected: boolean;
    status: number; // 题目状态：-1 未开始，0 尝试中，1 已解答
}
interface ProblemListResponse {
    items: Problem[];
    total: number;
}
class BaseProblemService {

    getProblemList(data: any) {
        return request.post<ProblemListResponse>('/subject/list', data)
    }

    getProblemDetail(id: string, params: any) {
        return request.get<Problem>(`/subject/${id}`, { params })
    }

    submit(data: any) {
        return request.post<any>('/user/commit/record', data)
    }
}

export default new BaseProblemService()
