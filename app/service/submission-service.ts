import request from '../utils/axios'

export interface Submission {
	/**
	 * 类型 1：编程题 2：基础题
	 */
	type: number;

	/**
	 * 子类型 type=2时不为空 1：单选 2：多选 3：判断
	 */
	subType: number;

	/**
	 * 题目id
	 */
	problemId: string;

	/**
	 * 题目
	 */
	title: string;

	/**
	 * 难度
	 */
	difficulty: number;

	/**
	 * 提交时间
	 */
	submitTime: string;

	/**
	 * 语言，type=2时不为空
	 */
	language: string;

	/**
	 * 选项，type=1时不为空
	 */
	options: string;

	/**
	 * 状态
	 */
	status: number;

	/**
	 * 运行时间
	 */
	time: number;

	/**
	 * 内存占用
	 */
	memory: number;

	/**
	 * 代码
	 */
	code: string;
}

export interface SubmissionStatistic {

	/**
	 * 总提交次数
	 */
	submissionCount: number;

	/**
	 * 通过次数
	 */
	acceptCount: number;

	/**
	 * 已解决题目
	 */
	solvedProblems: number;

	/**
	 * 趋势图数据 - 总提交
	 */
	submissionData: number[];

	/**
	 * 趋势图数据 - 通过
	 */
	acceptData: number[];

	/**
	 * 趋势图x轴数据（日期）
	 */
	chartDates: string[];
}

class SubmissionService {
	getSubmissionList(data: {
		// 1: 基础题 2: 编程题
		type: number | null | undefined;

		// 状态
		status: number | null | undefined;

		// 难度 1: 简单 2: 中等 3: 困难
		difficulty: number | null | undefined;

		// 时间范围
		timeRange: string;

		// 题目名称、ID
		keyword: string;

		pageNum: number;

		pageSize: number;
	}) {
		return request.post<Submission[]>('/user/commit/record/list/v3', data)
	}


	/**
	 * 获取提交统计
	 *
	 * @param type 1：基础题 2：编程题 null: 全部
	 */
	getStatistic(type: number | null) {
		return request.get<SubmissionStatistic>('/user/commit/record/stat', { params: {type}})
	}
}

export default new SubmissionService()
