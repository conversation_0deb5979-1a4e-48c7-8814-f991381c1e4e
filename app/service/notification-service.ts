import request from '../utils/axios'
import {PageResult, UserInfo} from "@/app/model/models";


export interface Notification {
	/**
	 * ID
	 */
	id: number;
	/**
	 * 标题
	 */
	title: string;
	/**
	 * 内容
	 */
	content: string;
	/**
	 * 发布时间
	 */
	createdTime: string;
	/**
	 * 发布者
	 */
	publisher: UserInfo;
	/**
	 * 落地页，为空时打开消息详情页
	 */
	link: string;
	/**
	 * 类型 1: 系统通知 2: 会员通知 3: 竞赛通知
	 */
	type: number;
	/**
	 * 是否已读 0: 未读 1: 已读
	 */
	isRead: number;
}

class NotificationService {

	/**
	 * 获取通知列表
	 *
	 * @param params
	 */
	getNotifications(params: {
		// 页数
		pageNum: number;
		// 每页条数
		pageSize: number;
		// 类型 1: 系统通知 2: 会员通知 3: 竞赛通知
		type?: number;
		// 是否已读 0: 未读 1: 已读
		isRead?: number;
	}) {
		return request.get<PageResult<{}>>('/message/inside', { params })
	}

	/**
	 * 标记为已读
	 *
	 * @param id
	 */
	read(id: number) {
		return request.put(`/message/inside/read/${id}`)
	}

	/**
	 * 全部标记为已读
	 */
	readAll() {
		return request.put(`/message/inside/readAll`)
	}

	/**
	 * 清空所有通知
	 */
	clearAll() {
		return request.delete(`/message/inside/clearAll`)
	}

	/**
	 * 获取通知详情
	 *
	 * @param id 通知id
	 */
	getNotificationDetail(id: number) {
		return request.get<Notification>(`/message/inside/${id}`)
	}
}

export default new NotificationService()
