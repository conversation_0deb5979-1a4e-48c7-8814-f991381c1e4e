import request from '../utils/axios'
import {PageResult} from "@/app/model/models";
import { Tag } from './tag-service';

export interface NewsModel {
	id: number;
	// 标题
	title: string;

	// 副标题
	subTitle: string;

	// 封面
	coverImage: string;

	// 发布时间
	createdTime: string;

	// 阅读量
	viewAmt: number;

	// 作者
	author: string;

	// 是否置顶
	isTop: boolean;

	// 分类
	categoryName: string;

	// 标签
	tags: Tag[];
}

export interface NewsDetailModel extends NewsModel {
	content: string;
}

class NewsService {
	getPageList(data: {
		pageNum: number;
		pageSize: number;
		title: string | null;
		categoryId: number | null;
		tagId: number | null;
	}) {
		return request.post<PageResult<NewsModel>>('/news/list', data)
	}

	getNewsDetail(id: number) {
		return request.get<NewsDetailModel>(`/news/${id}`)
	}
}

export default new NewsService();
