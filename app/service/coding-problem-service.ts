import request from '../utils/axios'
import type {Tag, Category, Interact} from '../model/models';
import {DataInteractStatistics} from "../model/models";

export interface Problem {
    id: number;
    pid: number;
    problemId: string;
    title: string;
    description: string;
    difficulty: number;
    categoryName: string;
    tags: Tag[];
    type: number;
    examples: string;
    statistics: DataInteractStatistics;
    // 题目状态：-1 未开始，0 尝试中，1 已解答
    status: number;
}

export interface ProblemDetail {
    problem: Problem
    tags: Tag[];
    statistics: DataInteractStatistics;
}

interface ProblemListResponse {
    items: Problem[];
    total: number;
}

interface NavigationResponse {
    prevId: string | null;
    nextId: string | null;
}

class CodingProblemService {

    getProblemList(data: any) {
        return request.post<ProblemListResponse>('/problem/list', data)
    }

    getProblemDetail(params: any) {
        return request.get<ProblemDetail>(`/problem/detail`, { params: params })
    }

    getNavigation(params: any) {
        return request.get<NavigationResponse>('/problem/navigation', { params })
    }
}

export default new CodingProblemService()
