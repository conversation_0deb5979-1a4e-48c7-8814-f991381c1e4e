import request from '../utils/axios';

export interface CompetitionView {
	/**
	 * 标题
	 */
	title: string;

	/**
	 * 子标题
	 */
	subTitle: string;

	/**
	 * 比赛开始时间
	 */
	competitionStartTime: string;

	/**
	 * 比赛结束时间
	 */
	competitionEndTime: string;

	/**
	 * 比赛地点
	 */
	location: string;

	/**
	 * 比赛类型
	 */
	type: string;
}

class CompetitionService {
	getList(params: {
		// 年份
		year: number;
		// 月份
		month: number;
	}) {
		return request.get<CompetitionView>('/competition/list', { params })
	}
}
export default new CompetitionService()
