import request from '../utils/axios'

export interface TradeInfo {

	/**
	 * 商品名称
	 */
	snapshotName: string;

	/**
	 * 支付宝支付表单html
	 */
	aliPayBody: string;

	/**
	 * 是否为会员升级订单
	 */
	isUpgrade: number;

	/**
	 * 升级天数
	 */
	upgradeDays: number;

	/**
	 * 订单过期时间
	 */
	expireAt: string;

	/**
	 * 订单金额（分）
	 */
	tradeFee: number;

	/**
	 * 订单号
	 */
	tradeNo: string;

	/**
	 * 会员时长（月）
	 */
	months?: number;

	/**
	 * 是否为连续包月订阅
	 * 0: 否
	 * 1: 是
	 */
	isContinuousMonthlySubscription?: number;

	/**
	 * 支付二维码URL（微信支付使用）
	 */
	payUrl?: string;
}

class TradeService {
	getPrepayTradeNo(params: {
		// 支付平台
		platform: 10 | 23;
		// 订阅计划ID
		planId: number;
		// 是否为升级会员
		upgrade: number | null;
		// 订阅时长
		months?: number;
		// 是否为连续月订阅
		isContinuousMonthlySubscription?: 0 | 1;
	}) {
		return request.post('/trade/native/prepay', null, { params })
	}

	/**
	 * 获取订单状态
	 * -10 已取消
	 * -11 支付超时自动取消
	 * 0 待支付
	 * 1 支付失败
	 * 5 支付确认中
	 * 10 已支付
	 *
	 * @param tradeNo 订单号
	 */
	getTradeStatus(tradeNo: string) {
		return request.get<{ data: number }>(`/trade/${tradeNo}/status`)
	}
}

export default new TradeService()
