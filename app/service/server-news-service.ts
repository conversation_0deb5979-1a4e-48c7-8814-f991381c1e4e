import { NewsDetailModel } from './news-service';

/**
 * 服务端获取新闻详情
 * @param id 新闻ID
 */
export async function getNewsDetail(id: number) {
  try {
    // 模拟数据响应
    // 在实际项目中，这里应该是一个真实的API调用
    return {
      code: 200,
      data: {
        id: id,
        title: `资讯详情 ${id}`,
        subTitle: `这是资讯 ${id} 的副标题`,
        content: `# 这是资讯 ${id} 的内容

这是一个测试段落。

## 小标题

* 列表项 1
* 列表项 2
* 列表项 3

代码示例:

\`\`\`javascript
console.log('你好，世界');
\`\`\`
`,
        createdTime: new Date().toISOString(),
        viewAmt: 1000,
        author: '编辑部',
        categoryName: '技术资讯',
        coverImage: 'https://picsum.photos/800/400',
        isTop: false,
        tags: [
          { id: 1, name: 'JavaScript' },
          { id: 2, name: 'React' },
          { id: 3, name: 'Next.js' }
        ]
      },
      msg: '获取成功'
    };
  } catch (error) {
    console.error('获取资讯详情错误:', error);
    throw error;
  }
}
