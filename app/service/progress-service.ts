import request from '../utils/axios'



export interface KnowledgeProgress {
    // 知识点名称
    label: string;
    // 知识点完成度
    percentage: number;
}

class ProgressService {

    /**
     * 获取提交热力图数据
     * 
     * @param data 
     * @returns 
     */
    getHeatmapData(data: {
        startTime: string;
        endTime: string;
    }) {
        return request.post<Record<string, number>>('/stat/heatmap', data)
    }

    /**
     * 获取知识点完成度
     * 
     * @returns 
     */
    getKnowledgeProgress() {
        return request.get<{
            basicProblem: KnowledgeProgress[];
            codingProblem: KnowledgeProgress[];
        }>('/stat/knowledge')
    }
}

export default new ProgressService();