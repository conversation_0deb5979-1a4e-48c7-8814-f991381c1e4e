import request from '../utils/axios'

export interface TestJudgeResult {
	expectedOutput: string | null;
	memory: number;
	problemJudgeMode: string;
	status: number;
	stderr: string;
	time: number;
	userInput: string;
	userOutput: string;
}

export interface JudgeResult {
	id: number;
	status: number;
	memory: number;
	time: number;
	language: string;
	score: number;
	code: string;
	submitTime: number;
	errorMessage?: string;
	// 0/null 未分析 1 分析中 2 分析完成 3 分析失败
	analysisStatus?: number;
	// 分析结果json字符串
	analysisResult?: string;
}

export interface SubmitDetail {
	submission: JudgeResult;
}

export interface SubmitListResponse {
	items: JudgeResult[];
	total: number;
}

class JudgeService {
	submitTest(data: any) {
		return request.post<string>('/judge/submit-problem-test-judge', data)
	}
	getTestResult(testJudgeKey: string) {
		return request.get<TestJudgeResult>(`/judge/get-test-judge-result?testJudgeKey=${testJudgeKey}`)
	}

	judge(data: any) {
		return request.post<JudgeResult>('/judge/submit-problem-judge', data)
	}

	getSubmitDetail(id: number) {
		return request.get<SubmitDetail>(`/judge/get-submission-detail?submitId=${id}`)
	}

	getSubmitList(params: any) {
		return request.get<SubmitListResponse>(`/judge/list`, { params })
	}

	aiAnalysis(id: number) {
		return request.get(`/judge/analysis/${id}`)
	}

	getJudgeDetail(id: number) {
		return request.get<JudgeResult>(`/judge/${id}`)
	}
}

export default new JudgeService()
