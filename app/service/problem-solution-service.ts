import { UserInfo, PageResult } from '../model/models';
import request from '../utils/axios'

export interface ProblemSolution {
    // id
    id: number;
    // 标题
    title: string;
    // 内容
    content: string;
    // 作者
    author: UserInfo;
    // 创建时间
    createdTime: string;
}

class ProblemSolutionService {
    getProblemSolution(params: {
        pageNum: number;
        pageSize: number;
        pid: number;
    }) {
        return request.get<PageResult<ProblemSolution>>(`/problem/solution/list`, { params })
    }
}

export default new ProblemSolutionService()