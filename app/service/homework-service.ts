import request from "../utils/axios"

export interface HomeworkProblem {
    // 题目id
    problemId: string;
    // 1: 基础题 2: 编程题
    problemType: number;
    // 题目分数
    score: number;
}

class HomeworkService {
  async createHomework(data: {
    // 作业标题
    title: string;
    // 截止时间
    endTime: string;
    // 作业说明
    description: string;
    // 作业题目
    problems: HomeworkProblem[];
    // 是否允许多次提交
    enableMultipleSubmit: 0 | 1;
    // 状态 0: 草稿 1: 发布
    status: 0 | 1;
}) {
    return request.post("/homework", data)
  }
}

export default new HomeworkService()

