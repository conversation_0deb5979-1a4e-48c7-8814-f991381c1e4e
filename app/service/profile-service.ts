import request from '../utils/axios'
import { selectUser, setUser } from '@/app/redux/features/authSlice';
import {dispatch} from "react-hot-toast/src/core/store";

export interface Profile {
	name: string;
	nickname: string;
	mobile: string;
	registerDate: Date;
	avatar: string;
	currentMembershipLevel: number;
	membershipExpireTime: Date;
	hasBindMobile: boolean;
	hasBindEmail: boolean;
	hasBindWeChat: boolean;
	// 性别 1：男  2：女
	sex: number;
	// 年龄
	age: number;
}

class ProfileService {
	getProfile() {
		return request.get<Profile>('/profile')
	}

	/**
	 * 获取学习进度
	 * 
	 * @returns 
	 */
	getProgress() {
		return request.get<{
			basicProblemAcceptCount: number;
			basicProblemTotal: number;
			codingProblemAcceptCount: number;
			codingProblemTotal: number;
			easyCount: number;
			mediumCount: number;
			hardCount: number;
			easyTotal: number;
			mediumTotal: number;
			hardTotal: number;
		}>('/profile/progress')
	}

	/**
	 * 获取最近几天的解题数量
	 * 
	 * @param params 
	 * @returns 
	 */
	getStudyStat(params: {
		days?: number;
	}) {
		return request.get<{
			// 最近30天解题数量
			solveProblemCounts: number[];
		}>('/profile/study', { params })
	}

	/**
	 * 绑定手机号
	 * @param data
	 */
	bindMobile(data: {
		mobile: string,
		verifyCode: string
	}) {
		return request.post('/user/mobile/bind', data)
	}

	/**
	 * 更新用户信息
	 *
	 * @param avatar      头像
	 * @param nickname    昵称
	 * @param name        姓名
	 * @param sex         性别 1：男  2：女
	 * @param age         年龄
	 */
	updateUserInfo(avatar: File | null, nickname: string, name: string | '', sex: 1 | 2 | null, age: number | null) {
		const formData = new FormData()
		if (avatar) {
			formData.set("avatar", avatar)
		}
		formData.set("nickname", nickname)
		formData.set("name", name)
		if (sex !== null) {
			formData.set("sex", String(sex))
		}
		if (age !== null) {
			formData.set("age", String(age))
		}
		return request.post('/user/update', formData, { headers: {"Content-Type": "multipart/form-data" }})
	}

	/**
	 * 校验当前手机号验证码
	 *
	 * @param data
	 * @returns
	 */
	validatePhoneCode(data: {
		// 1: 更换手机号 2：重置密码
		type: number;
		// 验证码
		code: string;
	}) {
		return request.post<boolean>('/profile/validatePhoneCode', data)
	}

	/**
	 * 验证当前邮箱验证码
	 *
	 * @param data
	 * @returns
	 */
	validateEmailCode(data: {
		// 1: 更换邮箱 2: 解绑邮箱
		type: number;
		// 验证码
		code: string;
	}) {
		return request.post<boolean>('/profile/validateEmailCode', data)
	}

	/**
	 * 更新绑定手机号
	 *
	 * @param data
	 * @returns
	 */
	updatePhone(data: {
		phone: string;
		code: string;
	}) {
		return request.post('/profile/updatePhone', data)
	}

	/**
	 * 绑定邮箱
	 *
	 * @param data
	 * @returns
	 */
	bindEmail(data: {
		// 邮箱
		email: string;

		code: string;
	}) {
		return request.post('/profile/bindEmail', data)
	}

	/**
	 * 更新邮箱
	 *
	 * @param data
	 * @returns
	 */
	updateEmail(data: {
		// 新邮箱
		email: string;
		// 验证码
		code: string;
	}) {
		return request.post('/profile/updateEmail', data)
	}

	/**
	 * 解绑邮箱
	 *
	 * @param data
	 * @returns
	 */
	unbindEmail(data: {
		// 验证码
		code: string;
	}) {
		return request.post('/profile/unbindEmail', data)
	}

	/**
	 * 解绑微信
	 */
	unbindWeChat() {
		return request.post('/profile/unbindWeChat')
	}

	/**
	 * 重置密码
	 *
	 * @param data
	 * @returns
	 */
	updatePassword(data: {
		password: string;
	}) {
		return request.post('/profile/updatePassword', data)
	}

	/**
	 * 获取微信绑定二维码票据
	 *
	 * @returns
	 */
	getBindWeChatTicket() {
		return request.get<{
			// 票据
			ticket: string;
			// 有效期（秒）
			expire_seconds: number;
			// 二维码内容
			url: string;
		}>('/profile/wechat/bind/ticket')
	}

	/**
	 * 获取绑定结果
	 *
	 * @param ticket 票据
	 * @returns
	 * -2：该微信已被其他账号绑定
	 * -1：二维码已失效
	 * 0：未绑定
	 * 1：已绑定
	 */
	getWeChatBindStatus(ticket: string) {
		return request.get<number>('/profile/wechat/bind/status', { params: { ticket }})
	}

	/**
	 * 重置密码
	 *
	 * @param data
	 * @returns
	 */
	resetPassword(data: {
		password: string;
		verifyCode: string;
		email?: string;
		phone?: string;
	}) {
		return request.post('/profile/resetPassword', data)
	}

}

export default new ProfileService()
