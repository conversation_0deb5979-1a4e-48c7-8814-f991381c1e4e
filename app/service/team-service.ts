import request from '../utils/axios'
import { PageResult } from '../model/models'


export interface TeamView {
    // 团队ID
    visibleTeamId: number;
    // 团队名称
    name: string;
    // 团队描述
    description: string;
    // 团队类型
    type: number;
    // 是否是自己的团队
    isCreator: boolean;
    // 是否归档
    isClosed: number;
    // 团队人数
    teamUserCount: number;
    // 创建时间
    createdTime: string;
}

export interface TeamDetailView extends TeamView {
    // 团队公告
    notice: string;

    // 团队角色 1: 管理员 2: 成员
    role: number;

    // 作业数量
    homeworkCount: number;
    
    // 题目数量
    problemListCount: number;
}

class TeamService {
  getTeamList(params: {
    pageNum: number;
    pageSize: number;
    // 是否是自己的团队
    isMy: 0 | 1;
    // 是否归档
    isClosed: 0 | 1;
  }) {
    return request.get<PageResult<TeamView>>('/team/list', { params })
  }

  create(data: {
    // 团队名称
    name: string;
    // 团队描述
    description: string;
    // 团队类型
    type: number;
    // 加入方式
    joinType: number;
    // 密码
    password: string;
  }) {
    return request.post('/team', data)
  }

  // 获取团队详情
  getTeamDetail(teamId: number) {
    return request.get<TeamDetailView>(`/team/${teamId}`)
  }

  getNotice(teamId: number) {
    return request.get<string>(`/team/notice/${teamId}`)
  }

  updateNotice(data: {
    teamId: number;
    notice: string;
  }) {
    return request.post('/team/notice', data)
  }
}

export default new TeamService()
