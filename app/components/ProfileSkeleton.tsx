import React from 'react';

export default function ProfileSkeleton() {
  return (
    <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8 animate-pulse">
      <div className="md:flex">
        {/* 左侧头像区域 */}
        <div className="md:w-1/3 bg-gradient-to-r from-indigo-500 to-purple-600 p-8">
          <div className="flex flex-col items-center justify-center h-full">
            <div className="w-32 h-32 rounded-full bg-white/20"></div>
            <div className="h-8 w-48 bg-white/20 rounded mt-4"></div>
            <div className="h-6 w-32 bg-white/20 rounded mt-2"></div>
            <div className="h-10 w-32 bg-white/20 rounded mt-6"></div>
          </div>
        </div>

        {/* 右侧信息区域 */}
        <div className="p-8 md:w-2/3">
          <div className="flex justify-between items-center mb-6">
            <div className="h-8 w-32 bg-gray-200 rounded"></div>
            <div className="h-8 w-24 bg-gray-200 rounded"></div>
          </div>
          <div className="grid md:grid-cols-2 gap-6">
            {[...Array(6)].map((_, index) => (
              <div key={index}>
                <div className="h-4 w-16 bg-gray-200 rounded mb-2"></div>
                <div className="h-6 w-32 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
} 