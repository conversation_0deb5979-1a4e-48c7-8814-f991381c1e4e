import React, { useState, useRef, useEffect, ReactNode } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronUp, faChevronDown, faCheck, faTimes } from '@fortawesome/free-solid-svg-icons';

export interface DropdownOption<T = string | number | null> {
  label: string;
  value: T;
  prefix?: ReactNode; // 允许在选项前添加图标或emoji
}

interface CommonDropdownProps<T = string | number | null> {
  label: string;                                  // 下拉框标签
  options: DropdownOption<T>[];                      // 选项数组
  value?: T;
  onChange: (value: T) => void;     // 选择变更回调
  className?: string;                             // 自定义类名
  placeholder?: string;                           // 占位文本
  canClear?: boolean;                             // 是否可以清除选择
  disabled?: boolean;                             // 是否禁用下拉框
}

// 自定义滚动条样式
const scrollbarStyles = `
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
  
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #ffffff;
  }
`;

const CommonDropdown = <T extends string | number | null>({
  label,
  options,
  value,
  onChange,
  className = '',
  placeholder = '请选择',
  canClear = true,
  disabled = false
}: CommonDropdownProps<T>) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement | null>(null);

  // 关闭下拉框的点击外部区域事件处理
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current?.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 获取当前选中选项的标签
  const getSelectedLabel = (): string => {
    if (value === undefined || value === null) return placeholder;
    const selectedOption = options.find(opt => opt.value === value);
    return selectedOption ? selectedOption.label : placeholder;
  };

  // 切换下拉框的打开/关闭状态
  const toggleDropdown = () => {
    if (disabled) return;
    setIsOpen(!isOpen);
  };

  // 选择选项
  const handleSelect = (optionValue: T) => {
    if (value === optionValue) {
      onChange(null as T);
    } else {
      onChange(optionValue);
    }
    setIsOpen(false);
  };

  // 清除选择
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡，防止触发下拉框的切换
    onChange(null as T);
  };

  const isSelected = value !== undefined && value !== null;
  
  // 获取高度相关的类名，从className中提取
  const getHeightClass = () => {
    const heightClasses = ['h-6', 'h-7', 'h-8', 'h-9', 'h-10', 'h-11', 'h-12'];
    for (const cls of heightClasses) {
      if (className.includes(cls)) {
        return cls;
      }
    }
    return '';
  };
  
  const heightClass = getHeightClass();
  // 移除className中的高度相关类，避免重复
  const cleanClassName = className.replace(/\bh-\d+\b/g, '').trim();

  return (
    <>
      <style jsx>{scrollbarStyles}</style>
      <div className={`relative ${cleanClassName}`} ref={dropdownRef}>
        {/* 下拉框按钮 */}
        <button
          type="button"
          onClick={toggleDropdown}
          className={`flex items-center justify-between w-full px-4 ${heightClass || 'py-2'} bg-gray-100 border border-gray-200 rounded-lg focus:outline-none transition-colors ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-200'}`}
          disabled={disabled}
          style={!heightClass ? {} : { paddingTop: '0', paddingBottom: '0' }}
        >
          <span className={`${isSelected ? 'text-gray-800' : 'text-gray-500'}`}>{getSelectedLabel()}</span>
          <FontAwesomeIcon 
            icon={isOpen ? faChevronUp : faChevronDown} 
            className="text-gray-500 ml-2" 
          />
        </button>

        {/* 下拉选项列表 */}
        {isOpen && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-[400px] overflow-y-auto custom-scrollbar">
            <div className="py-1">
              {options.map((option) => (
                <div
                  key={typeof option.value === 'number' ? option.value.toString() : option.value}
                  className="flex items-center px-4 py-3 hover:bg-gray-50 cursor-pointer"
                  onClick={() => handleSelect(option.value)}
                >
                  {/* 前缀图标或emoji */}
                  {option.prefix && (
                    <span className="mr-2">{option.prefix}</span>
                  )}

                  {/* 选项内容 */}
                  <span title={option.label} className="flex-1 overflow-hidden text-ellipsis line-clamp-1">{option.label}</span>

                  {/* 选中状态指示器 */}
                  {value === option.value && (
                    <FontAwesomeIcon icon={faCheck} className="text-blue-500 ml-2" />
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default CommonDropdown;
