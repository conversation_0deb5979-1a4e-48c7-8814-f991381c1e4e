import React from 'react';

export default function KnowledgeChartSkeleton() {
  return (
    <div className="bg-white rounded-xl shadow-md overflow-hidden animate-pulse">
      <div className="px-6 py-4 bg-gray-200 border-b border-gray-100">
        <div className="h-6 w-32 bg-gray-300 rounded"></div>
      </div>
      <div className="p-6">
        <div className="h-64 bg-gray-200 rounded-full"></div>
        <div className="mt-6 pt-6 border-t border-gray-100">
          <div className="h-5 w-28 bg-gray-300 rounded mb-3"></div>
          <div className="flex items-start space-x-2">
            <div className="w-4 h-4 rounded-full bg-gray-300 mt-0.5"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded w-full"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 