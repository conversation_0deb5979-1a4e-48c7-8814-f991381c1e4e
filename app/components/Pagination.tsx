"use client";

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronLeft, faChevronRight } from '@fortawesome/free-solid-svg-icons';

export interface PaginationProps {
  currentPage: number;
  totalItems: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  showInfo?: boolean;
  maxPageButtons?: number;
  className?: string;
}

export default function Pagination({
  currentPage,
  totalItems,
  pageSize,
  onPageChange,
  showInfo = true,
  maxPageButtons = 5,
  className = "",
}: PaginationProps) {
  // 计算总页数
  const pageCount = Math.ceil(totalItems / pageSize);
  
  // 如果只有一页或没有数据，不显示分页
  if (pageCount <= 1) {
    return null;
  }
  
  // 计算要显示的页码范围
  let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
  let endPage = Math.min(pageCount, startPage + maxPageButtons - 1);
  
  // 调整开始页码，确保显示正确数量的页码按钮
  if (endPage - startPage + 1 < maxPageButtons) {
    startPage = Math.max(1, endPage - maxPageButtons + 1);
  }
  
  // 生成页码按钮数组
  const pageNumbers = Array.from(
    { length: endPage - startPage + 1 },
    (_, i) => startPage + i
  );

  return (
    <div className={`flex flex-wrap items-center justify-center md:justify-between mt-8 ${className}`}>
      {showInfo && (
        <div className="hidden md:block text-sm text-gray-700 mb-4 md:mb-0">
          显示 <span className="font-medium">{Math.min(totalItems, (currentPage - 1) * pageSize + 1)}</span> 到{' '}
          <span className="font-medium">{Math.min(currentPage * pageSize, totalItems)}</span> 条，共{' '}
          <span className="font-medium">{totalItems}</span> 条结果
        </div>
      )}
      <div className="flex flex-wrap items-center justify-center gap-1 sm:gap-2">
        {/* 上一页按钮 */}
        <button
          onClick={() => onPageChange(Math.max(1, currentPage - 1))}
          disabled={currentPage === 1}
          className={`px-2 sm:px-3 py-1 rounded-md text-sm ${
            currentPage === 1
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-indigo-600 text-white hover:bg-indigo-700'
          }`}
          aria-label="上一页"
        >
          <FontAwesomeIcon icon={faChevronLeft} />
        </button>

        {/* 第一页和省略号 - 在小屏幕上隐藏 */}
        {startPage > 2 && (
          <button
            onClick={() => onPageChange(1)}
            className="hidden sm:block px-2 sm:px-3 py-1 rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200 text-sm"
          >
            1
          </button>
        )}
        {startPage > 3 && (
          <span className="hidden sm:block text-gray-500">...</span>
        )}
        
        {/* 当前页左侧一页 - 在小屏幕上隐藏 */}
        {currentPage > 1 && startPage > 1 && currentPage === startPage && (
          <button
            onClick={() => onPageChange(currentPage - 1)}
            className="hidden sm:block px-2 sm:px-3 py-1 rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200 text-sm"
          >
            {currentPage - 1}
          </button>
        )}

        {/* 页码按钮 */}
        {pageNumbers.map(number => {
          // 在小屏幕上只显示当前页
          const shouldHide = (number !== currentPage);
          
          return (
            <button
              key={number}
              onClick={() => onPageChange(number)}
              className={`${shouldHide ? 'hidden sm:block' : ''} px-2 sm:px-3 py-1 rounded-md text-sm ${
                currentPage === number
                  ? 'bg-indigo-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {number}
            </button>
          );
        })}
        
        {/* 当前页右侧一页 - 在小屏幕上隐藏 */}
        {currentPage < pageCount && endPage < pageCount && currentPage === endPage && (
          <button
            onClick={() => onPageChange(currentPage + 1)}
            className="hidden sm:block px-2 sm:px-3 py-1 rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200 text-sm"
          >
            {currentPage + 1}
          </button>
        )}

        {/* 最后一页和省略号 - 在小屏幕上隐藏 */}
        {endPage < pageCount - 1 && (
          <span className="hidden sm:block text-gray-500">...</span>
        )}
        {endPage < pageCount && (
          <button
            onClick={() => onPageChange(pageCount)}
            className="hidden sm:block px-2 sm:px-3 py-1 rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200 text-sm"
          >
            {pageCount}
          </button>
        )}

        {/* 下一页按钮 */}
        <button
          onClick={() => onPageChange(Math.min(pageCount, currentPage + 1))}
          disabled={currentPage === pageCount}
          className={`px-2 sm:px-3 py-1 rounded-md text-sm ${
            currentPage === pageCount
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-indigo-600 text-white hover:bg-indigo-700'
          }`}
          aria-label="下一页"
        >
          <FontAwesomeIcon icon={faChevronRight} />
        </button>
      </div>
    </div>
  );
} 