'use client';

import React, { createContext, useContext, useReducer, useState, ReactNode, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import Modal, { ModalButton, ModalSize } from './Modal';
import modalService, { ModalEvent } from './modalService';

// 预定义的模态框类型
export type ModalType = 'default' | 'confirm' | 'alert' | 'form';

// 模态框配置选项
export interface ModalOptions {
  title?: string;
  content?: React.ReactNode;
  size?: ModalSize;
  buttons?: ModalButton[];
  onClose?: () => void;
  onConfirm?: () => void;
  onCancel?: () => void;
  showCloseButton?: boolean;
  closeOnEsc?: boolean;
  closeOnOutsideClick?: boolean;
  preventScroll?: boolean;
  confirmText?: string; // 确认按钮文本
  cancelText?: string;  // 取消按钮文本
  okText?: string;      // 确定按钮文本（用于alert类型）
  htmlContent?: string; // HTML富文本内容
  dangerouslyUseHTML?: boolean; // 是否将content作为HTML渲染（不安全，谨慎使用）
}

// 模态框实例，包含唯一ID和配置
export interface ModalInstance {
  id: string;
  type: ModalType;
  options: ModalOptions;
  isOpen: boolean;
  Component?: React.ComponentType<any>; // 自定义组件
  props?: any; // 自定义组件的属性
}

// 模态框上下文接口
interface ModalContextType {
  openModal: (type: ModalType, options: ModalOptions) => string;
  openCustomModal: <T = any>(Component: React.ComponentType<T>, props?: T) => string;
  closeModal: (id: string) => void;
  closeAllModals: () => void;
}

// 默认上下文值
const defaultModalContext: ModalContextType = {
  openModal: () => '',
  openCustomModal: () => '',
  closeModal: () => {},
  closeAllModals: () => {},
};

// 模态框状态
interface ModalState {
  modals: ModalInstance[];
}

// 模态框操作类型
type ModalAction =
  | { type: 'OPEN_MODAL'; payload: ModalInstance }
  | { type: 'CLOSE_MODAL'; payload: { id: string } }
  | { type: 'CLOSE_ALL_MODALS' };

// 模态框状态reducer
const modalReducer = (state: ModalState, action: ModalAction): ModalState => {
  switch (action.type) {
    case 'OPEN_MODAL':
      return {
        ...state,
        modals: [...state.modals, action.payload],
      };
    case 'CLOSE_MODAL':
      return {
        ...state,
        modals: state.modals.map(modal => 
          modal.id === action.payload.id 
            ? { ...modal, isOpen: false } 
            : modal
        ),
      };
    case 'CLOSE_ALL_MODALS':
      return {
        ...state,
        modals: state.modals.map(modal => ({ ...modal, isOpen: false })),
      };
    default:
      return state;
  }
};

// 创建模态框上下文
const ModalContext = createContext<ModalContextType>(defaultModalContext);

// 自定义钩子，用于获取模态框上下文
export const useModal = () => {
  const context = useContext(ModalContext);
  if (!context) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
};

// 模态框提供者组件
interface ModalProviderProps {
  children: ReactNode;
}

export const ModalProvider: React.FC<ModalProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(modalReducer, { modals: [] });
  const [cleanupList, setCleanupList] = useState<string[]>([]);

  // 打开模态框
  const openModal = (type: ModalType, options: ModalOptions): string => {
    const id = uuidv4();
    const modalInstance: ModalInstance = {
      id,
      type,
      options,
      isOpen: true,
    };

    dispatch({ type: 'OPEN_MODAL', payload: modalInstance });
    return id;
  };

  // 打开自定义组件模态框
  const openCustomModal = <T,>(
    Component: React.ComponentType<T>,
    props?: T
  ): string => {
    const id = uuidv4();
    const modalInstance: ModalInstance = {
      id,
      type: 'default',
      options: {
        showCloseButton: true,
      },
      isOpen: true,
      Component,
      props,
    };

    dispatch({ type: 'OPEN_MODAL', payload: modalInstance });
    return id;
  };

  // 关闭模态框
  const closeModal = (id: string) => {
    dispatch({ type: 'CLOSE_MODAL', payload: { id } });
    // 标记为即将清理
    setCleanupList(prev => [...prev, id]);
  };

  // 关闭所有模态框
  const closeAllModals = () => {
    dispatch({ type: 'CLOSE_ALL_MODALS' });
    // 标记所有为即将清理
    setCleanupList(state.modals.map(modal => modal.id));
  };

  // 完全删除已关闭的模态框
  const cleanupModals = () => {
    if (cleanupList.length > 0) {
      // 实际场景可以在这里清理模态框
      setCleanupList([]);
    }
  };

  const handleModalClose = (id: string, onClose?: () => void) => {
    if (onClose) onClose();
    closeModal(id);
  };

  // 监听modalService发出的事件
  useEffect(() => {
    const handleModalEvent = (event: ModalEvent) => {
      switch (event.type) {
        case 'OPEN_MODAL':
          const { id, modalType, options } = event.payload;
          const modalInstance: ModalInstance = {
            id,
            type: modalType,
            options,
            isOpen: true,
          };
          dispatch({ type: 'OPEN_MODAL', payload: modalInstance });
          break;
        case 'CLOSE_MODAL':
          closeModal(event.payload.id);
          break;
        case 'CLOSE_ALL_MODALS':
          closeAllModals();
          break;
      }
    };

    // 注册事件监听
    const emitter = modalService.getEmitter();
    emitter.onModalEvent(handleModalEvent);

    // 清理函数
    return () => {
      emitter.offModalEvent(handleModalEvent);
    };
  }, []);

  return (
    <ModalContext.Provider value={{ openModal, openCustomModal, closeModal, closeAllModals }}>
      {children}
      
      {/* 渲染当前打开的所有模态框 */}
      {state.modals.map((modal) => {
        // 如果是自定义组件模态框
        if (modal.Component) {
          const CustomComponent = modal.Component;
          return (
            <Modal
              key={modal.id}
              isOpen={modal.isOpen}
              title={modal.options.title}
              onClose={() => handleModalClose(modal.id, modal.options.onClose)}
              size={modal.options.size || 'md'}
              buttons={modal.options.buttons}
              showCloseButton={modal.options.showCloseButton}
              closeOnEsc={modal.options.closeOnEsc}
              closeOnOutsideClick={modal.options.closeOnOutsideClick}
              preventScroll={modal.options.preventScroll}
              htmlContent={modal.options.htmlContent}
              dangerouslyUseHTML={modal.options.dangerouslyUseHTML}
            >
              <CustomComponent {...modal.props} closeModal={() => closeModal(modal.id)} />
            </Modal>
          );
        }

        // 如果是预定义类型的模态框
        let content: React.ReactNode = modal.options.content;
        let buttons: ModalButton[] = modal.options.buttons || [];

        // 根据模态框类型生成默认配置
        if (modal.type === 'confirm' && !buttons.length) {
          buttons = [
            {
              text: modal.options.cancelText || '取消',
              variant: 'secondary',
              onClick: () => {
                if (modal.options.onCancel) modal.options.onCancel();
                closeModal(modal.id);
              },
            },
            {
              text: modal.options.confirmText || '确定',
              variant: 'primary',
              onClick: () => {
                if (modal.options.onConfirm) modal.options.onConfirm();
                closeModal(modal.id);
              },
            },
          ];
        } else if (modal.type === 'alert' && !buttons.length) {
          buttons = [
            {
              text: modal.options.okText || modal.options.confirmText || '确定',
              variant: 'primary',
              onClick: () => {
                if (modal.options.onConfirm) modal.options.onConfirm();
                closeModal(modal.id);
              },
            },
          ];
        }

        return (
          <Modal
            key={modal.id}
            isOpen={modal.isOpen}
            title={modal.options.title}
            onClose={() => handleModalClose(modal.id, modal.options.onClose)}
            size={modal.options.size || 'md'}
            buttons={buttons}
            showCloseButton={modal.options.showCloseButton !== undefined ? modal.options.showCloseButton : true}
            closeOnEsc={modal.options.closeOnEsc !== undefined ? modal.options.closeOnEsc : true}
            closeOnOutsideClick={modal.options.closeOnOutsideClick !== undefined ? modal.options.closeOnOutsideClick : true}
            preventScroll={modal.options.preventScroll !== undefined ? modal.options.preventScroll : true}
            htmlContent={modal.options.htmlContent}
            dangerouslyUseHTML={modal.options.dangerouslyUseHTML}
          >
            {content}
          </Modal>
        );
      })}
    </ModalContext.Provider>
  );
};

export default ModalProvider; 