'use client';

import React, { useRef, useEffect } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';

// 定义Modal尺寸类型
export type ModalSize = 'sm' | 'md' | 'lg' | 'xl' | 'full';

// 定义按钮类型
export interface ModalButton {
  text: string;
  variant?: 'primary' | 'secondary' | 'danger' | 'warning' | 'success' | 'info';
  onClick: () => void;
}

// 定义Modal属性
export interface ModalProps {
  isOpen: boolean;
  title?: string;
  children: React.ReactNode;
  onClose: () => void;
  size?: ModalSize;
  buttons?: ModalButton[];
  showCloseButton?: boolean;
  closeOnEsc?: boolean;
  closeOnOutsideClick?: boolean;
  preventScroll?: boolean;
  htmlContent?: string; // HTML富文本内容
  dangerouslyUseHTML?: boolean; // 是否使用dangerouslySetInnerHTML
}

// 尺寸配置
const sizeClasses: Record<ModalSize, string> = {
  sm: 'max-w-sm',
  md: 'max-w-md',
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  full: 'max-w-full w-11/12 h-5/6',
};

// 按钮样式配置
const buttonVariants: Record<string, string> = {
  primary: 'bg-blue-600 hover:bg-blue-700 text-white',
  secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-800',
  danger: 'bg-red-600 hover:bg-red-700 text-white',
  warning: 'bg-yellow-500 hover:bg-yellow-600 text-white',
  success: 'bg-green-600 hover:bg-green-700 text-white',
  info: 'bg-indigo-600 hover:bg-indigo-700 text-white',
};

const Modal: React.FC<ModalProps> = ({
  isOpen,
  title,
  children,
  onClose,
  size = 'md',
  buttons = [],
  showCloseButton = true,
  closeOnEsc = true,
  closeOnOutsideClick = true,
  preventScroll = true,
  htmlContent,
  dangerouslyUseHTML = false,
}) => {
  const modalRef = useRef<HTMLDivElement>(null);

  // 处理ESC关闭
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && closeOnEsc) {
        onClose();
      }
    };

    if (isOpen) {
      window.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose, closeOnEsc]);

  // 处理点击外部关闭
  const handleOutsideClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (closeOnOutsideClick && modalRef.current && !modalRef.current.contains(e.target as Node)) {
      onClose();
    }
  };

  // 控制滚动
  useEffect(() => {
    if (preventScroll) {
      if (isOpen) {
        document.body.style.overflow = 'hidden';
      } else {
        document.body.style.overflow = '';
      }
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen, preventScroll]);

  // 渲染富文本内容
  const renderContent = () => {
    // 如果提供了HTML内容且允许使用dangerouslySetInnerHTML
    if (htmlContent && dangerouslyUseHTML) {
      return (
        <div 
          className="prose dark:prose-invert max-w-none" 
          dangerouslySetInnerHTML={{ __html: htmlContent }} 
        />
      );
    }

    // 否则正常渲染子内容
    return children;
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50 bg-black/50 transition-opacity"
      onClick={handleOutsideClick}
      aria-modal="true"
      role="dialog"
    >
      <div
        ref={modalRef}
        className={`bg-white dark:bg-gray-800 rounded-lg shadow-xl transform transition-all mx-2 ${sizeClasses[size]}`}
      >
        {/* 头部区域 */}
        {(title || showCloseButton) && (
          <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            {title && <h3 className="text-lg font-medium text-gray-900 dark:text-white">{title}</h3>}
            {showCloseButton && (
              <button
                type="button"
                className="text-gray-400 hover:text-gray-500 focus:outline-none"
                onClick={onClose}
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            )}
          </div>
        )}

        {/* 内容区域 */}
        <div className="px-6 py-4 overflow-auto max-h-[60vh]">
          {renderContent()}
        </div>

        {/* 按钮区域 */}
        {buttons.length > 0 && (
          <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-2">
            {buttons.map((button, index) => (
              <button
                key={index}
                type="button"
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${buttonVariants[button.variant || 'secondary']}`}
                onClick={button.onClick}
              >
                {button.text}
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Modal;
