import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import { ModalType, ModalOptions } from './ModalContext';

// 声明事件类型
export interface ModalEvent {
  type: 'OPEN_MODAL' | 'CLOSE_MODAL' | 'CLOSE_ALL_MODALS';
  payload?: any;
}

// 创建单例事件发射器
class ModalEventEmitter extends EventEmitter {
  private static instance: ModalEventEmitter;

  private constructor() {
    super();
    // 设置最大监听器数量，避免警告
    this.setMaxListeners(20);
  }

  public static getInstance(): ModalEventEmitter {
    if (!ModalEventEmitter.instance) {
      ModalEventEmitter.instance = new ModalEventEmitter();
    }
    return ModalEventEmitter.instance;
  }

  // 发射Modal事件
  public emitModalEvent(event: ModalEvent): void {
    this.emit('modal', event);
  }

  // 监听Modal事件
  public onModalEvent(listener: (event: ModalEvent) => void): void {
    this.on('modal', listener);
  }

  // 移除Modal事件监听
  public offModalEvent(listener: (event: ModalEvent) => void): void {
    this.off('modal', listener);
  }
}

// 全局Modal服务
export const modalService = {
  // 打开模态框
  openModal: (type: ModalType, options: ModalOptions): string => {
    const id = uuidv4();
    
    // 处理字符串内容，如果看起来像HTML且没有明确指定是否使用HTML
    if (typeof options.content === 'string' && 
        options.content.trim().startsWith('<') && 
        options.content.includes('</') && 
        options.dangerouslyUseHTML === undefined) {
      // 看起来像HTML内容，自动使用htmlContent
      options.htmlContent = options.content as string;
      options.dangerouslyUseHTML = true;
      options.content = null; // 清空content，避免重复渲染
    }
    
    ModalEventEmitter.getInstance().emitModalEvent({
      type: 'OPEN_MODAL',
      payload: {
        id,
        modalType: type,
        options
      }
    });
    return id;
  },

  // 打开确认对话框的快捷方法
  confirm: (
    content: React.ReactNode, 
    options?: Omit<ModalOptions, 'content'>
  ): string => {
    return modalService.openModal('confirm', {
      content,
      ...options
    });
  },

  // 打开警告对话框的快捷方法
  alert: (
    content: React.ReactNode, 
    options?: Omit<ModalOptions, 'content'>
  ): string => {
    return modalService.openModal('alert', {
      content,
      ...options
    });
  },
  
  // 打开带HTML内容的对话框
  html: (
    htmlContent: string,
    options?: Omit<ModalOptions, 'htmlContent' | 'dangerouslyUseHTML'>
  ): string => {
    return modalService.openModal('default', {
      htmlContent,
      dangerouslyUseHTML: true,
      ...options
    });
  },

  // 关闭特定模态框
  closeModal: (id: string): void => {
    ModalEventEmitter.getInstance().emitModalEvent({
      type: 'CLOSE_MODAL',
      payload: { id }
    });
  },

  // 关闭所有模态框
  closeAllModals: (): void => {
    ModalEventEmitter.getInstance().emitModalEvent({
      type: 'CLOSE_ALL_MODALS'
    });
  },

  // 获取事件发射器实例（用于Provider连接）
  getEmitter: (): ModalEventEmitter => {
    return ModalEventEmitter.getInstance();
  }
};

export default modalService; 