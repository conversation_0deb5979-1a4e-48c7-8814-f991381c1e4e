'use client';

import { FC, useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { JudgeResult } from '@/app/service/judge-service';
import judgeService from '@/app/service/judge-service';
import { UserInfo } from '@/app/model/models';
import { formatKb } from '@/app/utils/number-utils';
import {useAppSelector} from "@/app/redux/hooks";
import {selectUser} from "@/app/redux/features/authSlice";
import {JUDGE_STATUS} from "@/app/utils/constant";
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { dracula } from 'react-syntax-highlighter/dist/cjs/styles/prism';
import { modalService } from '@/app/components/Modal';

// AI分析结果接口
interface AIAnalysisResult {
  overview: string;
  timeComplexity: string;
  spaceComplexity: string;
  suggestions: string[];
  codeQuality: number; // 1-10分
}

interface SubmissionDetailModalProps {
  submission: JudgeResult;
  onClose: () => void;
}

// AI分析结果展示组件
const AIAnalysisResultView: FC<{ result: AIAnalysisResult }> = ({ result }) => {
  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-medium mb-2">总体评价</h3>
        <p className="text-gray-700">{result.overview}</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="p-3 bg-gray-50 rounded-md">
          <div className="text-sm text-gray-500 mb-1">时间复杂度</div>
          <div className="font-medium">{result.timeComplexity}</div>
        </div>
        <div className="p-3 bg-gray-50 rounded-md">
          <div className="text-sm text-gray-500 mb-1">空间复杂度</div>
          <div className="font-medium">{result.spaceComplexity}</div>
        </div>
      </div>
      
      <div>
        <h3 className="text-lg font-medium mb-2">代码质量评分</h3>
        <div className="flex items-center">
          <div className="w-full bg-gray-200 rounded-full h-4 mr-2">
            <div 
              className={`h-4 rounded-full ${
                result.codeQuality >= 8 ? 'bg-green-600' : 
                result.codeQuality >= 6 ? 'bg-blue-600' : 
                result.codeQuality >= 4 ? 'bg-yellow-500' : 'bg-red-600'
              }`}
              style={{ width: `${result.codeQuality * 10}%` }}
            ></div>
          </div>
          <span className="text-lg font-bold">{result.codeQuality}/10</span>
        </div>
      </div>
      
      <div>
        <h3 className="text-lg font-medium mb-2">优化建议</h3>
        {result.suggestions.length > 0 ? (
          <ul className="list-disc pl-5 space-y-1">
            {result.suggestions.map((suggestion, index) => (
              <li key={index} className="text-gray-700">{suggestion}</li>
            ))}
          </ul>
        ) : (
          <p className="text-gray-500 italic">没有发现需要优化的地方</p>
        )}
      </div>
    </div>
  );
};

const SubmissionDetailModal: FC<SubmissionDetailModalProps> = ({
  submission: initialSubmission,
  onClose
}) => {
  if (!initialSubmission) return null;
  
  const [submission, setSubmission] = useState<JudgeResult>(initialSubmission);
  const [copyStatus, setCopyStatus] = useState<'ready' | 'copied'>('ready');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const user = useAppSelector(selectUser);
  
  // 用于轮询的间隔ID
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  
  // 清理轮询
  const clearPolling = () => {
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
      pollingRef.current = null;
    }
  };
  
  // 组件卸载时清理轮询
  useEffect(() => {
    return () => clearPolling();
  }, []);

  // 获取状态文本
  const getStatusText = (status: number) => {
    const statusKey = String(status);
    return (JUDGE_STATUS as any)[statusKey]?.name || '未知状态';
  };
// 获取状态样式
  const getStatusStyle = (status: number) => {
    switch (status) {
      case 0:
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-red-100 text-red-800';
    }
  };
  
  // 根据语言获取语法高亮的语言标识符
  const getLanguageIdentifier = (language: string): string => {
    // 转换为小写
    const lang = language?.toLowerCase() || '';
    
    // 映射常见语言到语法高亮标识符
    switch (lang) {
      case 'c++':
      case 'cpp':
        return 'cpp';
      case 'c#':
      case 'csharp':
        return 'csharp';
      case 'javascript':
      case 'js':
        return 'javascript';
      case 'typescript':
      case 'ts':
        return 'typescript';
      case 'python':
      case 'py':
      case 'python3':
        return 'python';
      case 'java':
        return 'java';
      case 'golang':
      case 'go':
        return 'go';
      case 'ruby':
      case 'rb':
        return 'ruby';
      case 'php':
        return 'php';
      case 'rust':
        return 'rust';
      case 'kotlin':
        return 'kotlin';
      case 'swift':
        return 'swift';
      default:
        return lang; // 返回原始值
    }
  };
  
  // 解析分析结果JSON字符串
  const parseAnalysisResult = (resultStr?: string): AIAnalysisResult | null => {
    if (!resultStr) return null;
    
    try {
      return JSON.parse(resultStr) as AIAnalysisResult;
    } catch (error) {
      console.error('分析结果解析失败:', error);
      return null;
    }
  };
  
  // 显示分析结果
  const showAnalysisResult = (result: AIAnalysisResult) => {
    const modal = modalService.openModal('default', {
      title: 'AI代码分析结果',
      content: <AIAnalysisResultView result={result} />,
      size: 'lg',
      buttons: [
        {
          text: '我知道了',
          variant: 'primary',
          onClick: () => {
            modalService.closeModal(modal);
          }
        }
      ]
    });
  };
  
  // 开始轮询获取分析结果
  const startPolling = () => {
    // 清理之前的轮询
    clearPolling();
    
    // 开始新的轮询
    pollingRef.current = setInterval(async () => {
      try {
        const response = await judgeService.getJudgeDetail(submission.id);
        if (response && response.data) {
          setSubmission(response.data);
          
          // 分析成功
          if (response.data.analysisStatus === 2) {
            clearPolling();
            setIsAnalyzing(false);
            
            const result = parseAnalysisResult(response.data.analysisResult);
            if (result) {
              showAnalysisResult(result);
            } else {
              modalService.alert('分析结果解析失败，请稍后再试。', {
                title: '错误'
              });
            }
          } 
          // 分析失败
          else if (response.data.analysisStatus === 3) {
            clearPolling();
            setIsAnalyzing(false);
            modalService.alert('代码分析失败，请稍后再试。', {
              title: '分析失败'
            });
          }
        }
      } catch (error) {
        console.error('获取分析结果失败:', error);
        clearPolling();
        setIsAnalyzing(false);
      }
    }, 2000); // 每2秒轮询一次
  };
  
  const handleCopy = () => {
    navigator.clipboard.writeText(submission.code);
    setCopyStatus('copied');
    
    setTimeout(() => {
      setCopyStatus('ready');
    }, 2000);
  };
  
  const handleAIAnalyze = async () => {
    // 根据不同分析状态处理
    const status = submission.analysisStatus;
    
    // 已经分析完成，直接显示结果
    if (status === 2) {
      const result = parseAnalysisResult(submission.analysisResult);
      if (result) {
        showAnalysisResult(result);
      } else {
        modalService.alert('分析结果解析失败，请稍后再试。', {
          title: '错误'
        });
      }
      return;
    }
    
    // 正在分析中，开始轮询
    if (status === 1) {
      setIsAnalyzing(true);
      startPolling();
      return;
    }
    
    // 未分析或分析失败，开始新的分析
    if (status === null || status === 0 || status === 3) {
      setIsAnalyzing(true);
      
      try {
        // 调用分析API
        await judgeService.aiAnalysis(submission.id);
        
        // 开始轮询获取结果
        startPolling();
      } catch (error) {
        console.error('启动分析失败:', error);
        setIsAnalyzing(false);
        modalService.alert('启动代码分析失败，请稍后再试。', {
          title: '错误'
        });
      }
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* 头部 */}
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-full bg-gray-200 overflow-hidden mr-3 flex items-center justify-center">
              {user?.avatar ? (
                <Image
                  src={'https:' + user.avatar}
                  alt={user?.username || '用户'}
                  width={40}
                  height={40}
                />
              ) : (
                <div
                  className="w-full h-full flex items-center justify-center bg-indigo-100 text-indigo-800 font-bold">
                  {user?.username ? user.username.charAt(0).toUpperCase() : '?'}
                </div>
              )}
            </div>
            <div>
              <h3 className="font-semibold text-gray-800">{user?.nickname || '用户'}的提交</h3>
              <div className="text-sm text-gray-500">{submission.submitTime}</div>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
            aria-label="关闭"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24"
                 stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-auto p-6">
          {/* 提交信息标签页 */}
            <>
              {/* 提交信息 */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="p-4 rounded-lg bg-gray-50">
                  <div className="text-sm text-gray-500 mb-1">状态</div>
                  <div
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium ${getStatusStyle(submission.status)}`}>
                    {getStatusText(submission.status)}
                  </div>
                </div>
                <div className="p-4 rounded-lg bg-gray-50">
                  <div className="text-sm text-gray-500 mb-1">语言</div>
                  <div className="font-medium">{submission.language}</div>
                </div>
                <div className="p-4 rounded-lg bg-gray-50">
                  <div className="text-sm text-gray-500 mb-1">执行时间</div>
                  <div className="font-medium">{submission.time}ms</div>
                </div>
                <div className="p-4 rounded-lg bg-gray-50">
                  <div className="text-sm text-gray-500 mb-1">内存占用</div>
                  <div className="font-medium">{formatKb(submission.memory)}</div>
                </div>
              </div>

              {/* 错误信息（如果有） */}
              {submission.errorMessage && submission.errorMessage != 'The error message does not support viewing.' && (
                <div className="mb-6">
                  <h4 className="text-lg font-semibold text-gray-800 mb-2">错误信息</h4>
                  <div className="p-4 bg-red-50 text-red-700 rounded-lg">
                    <pre>
                      {submission.errorMessage}
                    </pre>
                  </div>

                </div>
              )}
          </>

          {/* 代码标签页 */}
            <div>
              <h4 className="text-lg font-semibold text-gray-800 mb-2">提交代码</h4>
              <div className="relative code-block-wrapper">
                <div className="absolute right-2 top-2 z-10 flex space-x-2">
                  <button
                    className="px-2 py-1 bg-indigo-600 hover:bg-indigo-700 text-white rounded text-xs flex items-center"
                    onClick={handleAIAnalyze}
                    disabled={isAnalyzing}
                  >
                    {isAnalyzing ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        分析中
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 16C14.2091 16 16 14.2091 16 12C16 9.79086 14.2091 8 12 8C9.79086 8 8 9.79086 8 12C8 14.2091 9.79086 16 12 16Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M12 3V2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M12 22V21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M20 12H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M3 12H4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M18.364 5.63599L19.071 4.92899" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M4.93 19.071L5.637 18.364" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M18.364 18.364L19.071 19.071" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M4.93 4.92901L5.637 5.63601" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                        AI分析
                      </>
                    )}
                  </button>
                  <button
                    className="px-2 py-1 bg-gray-800 hover:bg-gray-700 text-gray-200 rounded text-xs"
                    onClick={handleCopy}
                  >
                    {copyStatus === 'ready' ? '复制' : '已复制!'}
                  </button>
                </div>
                <SyntaxHighlighter
                  style={dracula}
                  language={getLanguageIdentifier(submission.language)}
                  showLineNumbers={true}
                  wrapLines={true}
                  PreTag="div"
                  className="code-highlight-block"
                  customStyle={{
                    backgroundColor: '#282a36',
                    color: '#f8f8f2',
                    paddingTop: '1.5rem',
                    paddingLeft: '1rem',
                    paddingRight: '1rem',
                    paddingBottom: '1rem',
                    margin: '0',
                    borderRadius: '0.375rem',
                    fontSize: '0.875rem',
                    overflow: 'hidden',
                  }}
                  lineNumberStyle={{
                    minWidth: '2.5em',
                    paddingRight: '0.8em',
                    color: '#6272a4',
                    textAlign: 'right',
                    userSelect: 'none',
                    borderRight: '1px solid #44475a',
                    marginRight: '1em',
                  }}
                  codeTagProps={{
                    style: {
                      fontSize: '0.875rem',
                      fontFamily: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
                    }
                  }}
                >
                  {submission.code}
                </SyntaxHighlighter>
              </div>
            </div>
        </div>

        {/* 底部按钮 */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};

export default SubmissionDetailModal;
