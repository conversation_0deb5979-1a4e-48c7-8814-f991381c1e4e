import { useState, useEffect } from 'react';
import { StarIcon } from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid';
import CollectService from "@/app/service/collect-service";

interface FavoriteButtonProps {
  dataType: number;
  dataId: number;
  isCollected: boolean;
  className?: string;
}

export default function FavoriteButton({
  dataType,
  dataId,
  isCollected,
  className = '',
}: FavoriteButtonProps) {
  const [isFavorite, setIsFavorite] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // 检查是否已收藏


  // 初始化时检查收藏状态
  useEffect(() => {
    if (dataType && dataId) {
      setIsFavorite(isCollected)
    }
  }, [dataType, dataId, isCollected]);

  // 切换收藏状态
  const toggleFavorite = async () => {

    setIsLoading(true);
    if (isFavorite) {
      await CollectService.cancelCollect(dataType, dataId)
    } else {
      await CollectService.collect(dataType, dataId)
    }
    setIsFavorite(!isFavorite)
    setIsLoading(false)
  };

  return (
    <button
      onClick={toggleFavorite}
      disabled={isLoading}
      className={`text-yellow-500 hover:text-yellow-600 focus:outline-none transition-all ${className}`}
      title={isFavorite ? '取消收藏' : '添加收藏'}
    >
      {isFavorite ? (
        <StarSolidIcon className="w-6 h-6" />
      ) : (
        <StarIcon className="w-6 h-6" />
      )}
    </button>
  );
}
