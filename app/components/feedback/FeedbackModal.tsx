'use client'

import { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faTimes, faPaperPlane, faSpinner } from '@fortawesome/free-solid-svg-icons'
import CommonDropdown, { DropdownOption } from '../CommonDropdown'
import feedbackService from '@/app/service/feedback-service'
import { usePathname, useSearchParams } from 'next/navigation'

interface FeedbackModalProps {
  isOpen: boolean
  onClose: () => void
}

const feedbackTypes: DropdownOption[] = [
  { label: '功能建议', value: '功能建议' },
  { label: '问题反馈', value: '问题反馈' },
  { label: '使用困难', value: '使用困难' },
  { label: '内容纠错', value: '内容纠错' },
  { label: '其他', value: '其他' }
]

export default function FeedbackModal({ isOpen, onClose }: FeedbackModalProps) {
  const [feedbackType, setFeedbackType] = useState<string | null>(null)
  const [feedbackContent, setFeedbackContent] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [success, setSuccess] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const pathname = usePathname()
  const searchParams = useSearchParams()
  
  // 控制页面滚动
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = ''
    }
    
    // 清理函数，确保组件卸载时恢复滚动
    return () => {
      document.body.style.overflow = ''
    }
  }, [isOpen])
  
  // 获取当前完整URL路径（包括查询参数）
  const getCurrentPath = () => {
    const path = pathname
    const params = searchParams.toString()
    return params ? `${path}?${params}` : path
  }
  
  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!feedbackType) {
      setError('请选择反馈类型')
      return
    }
    
    if (!feedbackContent.trim()) {
      setError('请填写反馈内容')
      return
    }
    
    setIsSubmitting(true)
    setError(null)
    
    try {
      await feedbackService.feedback({
        path: getCurrentPath(),
        type: feedbackType,
        content: feedbackContent
      })
      
      setSuccess(true)
      setFeedbackType(null)
      setFeedbackContent('')
      
      // 3秒后关闭模态框
      setTimeout(() => {
        onClose()
        setSuccess(false)
      }, 3000)
    } catch (err) {
      console.error('提交反馈失败:', err)
      setError('提交失败，请稍后重试')
    } finally {
      setIsSubmitting(false)
    }
  }
  
  // 当模态框关闭时重置状态
  useEffect(() => {
    if (!isOpen) {
      setError(null)
      setSuccess(false)
    }
  }, [isOpen])
  
  // 如果模态框未打开，不渲染内容
  if (!isOpen) return null
  
  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 relative overflow-hidden">
        {/* 关闭按钮 */}
        <button 
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-200 hover:text-gray-400"
          disabled={isSubmitting}
        >
          <FontAwesomeIcon icon={faTimes} />
        </button>
        
        {/* 模态框标题 */}
        <div className="bg-indigo-600 text-white py-4 px-6">
          <h2 className="text-lg font-medium">意见反馈</h2>
          <p className="text-sm text-indigo-100">我们非常重视您的意见和建议</p>
        </div>
        
        {/* 反馈表单 */}
        <form onSubmit={handleSubmit} className="p-6">
          {success ? (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4 text-center">
              <p>感谢您的反馈！我们会认真对待每一条建议。</p>
            </div>
          ) : (
            <>
              {/* 当前页面路径信息 */}
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-medium mb-2">
                  页面路径
                </label>
                <div className="bg-gray-50 px-3 py-2 rounded-md border border-gray-200 text-sm text-gray-600 break-all">
                  {getCurrentPath()}
                </div>
              </div>
              
              {/* 反馈类型 */}
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-medium mb-2">
                  反馈类型 <span className="text-red-500">*</span>
                </label>
                <CommonDropdown
                  label="反馈类型"
                  options={feedbackTypes}
                  value={feedbackType}
                  onChange={(value) => setFeedbackType(value as string)}
                  placeholder="请选择反馈类型"
                  className="w-full"
                />
              </div>
              
              {/* 反馈内容 */}
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-medium mb-2">
                  反馈内容 <span className="text-red-500">*</span>
                </label>
                <textarea
                  value={feedbackContent}
                  onChange={(e) => setFeedbackContent(e.target.value)}
                  placeholder="请详细描述您的问题或建议..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  rows={5}
                  disabled={isSubmitting}
                ></textarea>
              </div>
              
              {/* 错误信息 */}
              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                  {error}
                </div>
              )}
              
              {/* 提交按钮 */}
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={onClose}
                  className="mr-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  disabled={isSubmitting}
                >
                  取消
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 flex items-center"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <FontAwesomeIcon icon={faSpinner} className="animate-spin mr-2" />
                      提交中...
                    </>
                  ) : (
                    <>
                      <FontAwesomeIcon icon={faPaperPlane} className="mr-2" />
                      提交反馈
                    </>
                  )}
                </button>
              </div>
            </>
          )}
        </form>
      </div>
    </div>
  )
} 