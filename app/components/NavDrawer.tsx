'use client';

import React, { useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faUser, 
  faSignOutAlt, 
  faCog, 
  faClipboardList, 
  faChartLine, 
  faHome,
  faBook,
  faCode,
  faListAlt,
  faNewspaper,
  faCrown,
  faTimes
} from '@fortawesome/free-solid-svg-icons';
import { useAppSelector, useAppDispatch } from '@/app/redux/hooks';
import { selectUser, selectIsAuthenticated, logout } from '@/app/redux/features/authSlice';

interface NavDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

const NavDrawer: React.FC<NavDrawerProps> = ({ isOpen, onClose }) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const pathname = usePathname();
  const user = useAppSelector(selectUser);
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const drawerRef = useRef<HTMLDivElement>(null);

  // 判断链接是否为当前页面
  const isActive = (path: string) => {
    if (path === '/') {
      return pathname === path;
    }
    return pathname.startsWith(path);
  };

  // 获取链接的样式类
  const getLinkClass = (path: string) => {
    return isActive(path)
      ? "flex items-center py-3 px-4 border-l-4 border-indigo-600 bg-indigo-50 text-indigo-600 font-medium"
      : "flex items-center py-3 px-4 border-l-4 border-transparent text-gray-600 hover:text-indigo-600 hover:bg-gray-50";
  };

  const handleLogout = async () => {
    try {
      await dispatch(logout());
      onClose();
      router.push('/login');
    } catch (error) {
      console.error('登出失败:', error);
    }
  };

  // 点击抽屉外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (drawerRef.current && !drawerRef.current.contains(event.target as Node) && isOpen) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // 阻止滚动
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    
    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-50">
      <div 
        ref={drawerRef}
        className="fixed top-0 right-0 w-full max-w-full sm:max-w-sm h-screen bg-white shadow-xl transform transition-transform duration-300 overflow-y-auto"
        style={{ transform: isOpen ? 'translateX(0)' : 'translateX(100%)' }}
      >
        <div className="flex justify-between items-center p-4 border-b border-gray-200">
          <h2 className="text-xl font-bold text-indigo-600">信竞星球</h2>
          <button 
            onClick={onClose}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
            aria-label="关闭导航菜单"
          >
            <FontAwesomeIcon icon={faTimes} className="text-gray-600" />
          </button>
        </div>

        <div className="p-4">
          {isAuthenticated && user ? (
            <div className="flex items-center space-x-3 mb-6 pb-4 border-b border-gray-200">
              <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-200">
                {user.avatar ? (
                  <Image
                    src={'https:' + user.avatar}
                    alt={user.nickname}
                    width={48}
                    height={48}
                    className="object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-indigo-100 text-indigo-600">
                    <FontAwesomeIcon icon={faUser} />
                  </div>
                )}
              </div>
              <div>
                <div className="font-medium text-gray-900">{user.nickname}</div>
                <div className="text-sm text-gray-500">{user.email}</div>
              </div>
            </div>
          ) : (
            <div className="flex justify-between items-center mb-6 pb-4 border-b border-gray-200">
              <Link
                href="/login"
                className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                onClick={onClose}
              >
                登录
              </Link>
              <Link
                href="/login"
                className="px-4 py-2 border border-indigo-600 text-indigo-600 rounded-md hover:bg-indigo-50"
                onClick={onClose}
              >
                注册
              </Link>
            </div>
          )}
          
          {/* 主菜单导航 */}
          <nav className="mb-6">
            <Link href="/" className={getLinkClass('/')} onClick={onClose}>
              <FontAwesomeIcon icon={faHome} className="mr-3 w-5" />
              首页
            </Link>
            <Link href="/basic-problems" className={getLinkClass('/basic-problems')} onClick={onClose}>
              <FontAwesomeIcon icon={faBook} className="mr-3 w-5" />
              基础题库
            </Link>
            <Link href="/coding-problems" className={getLinkClass('/coding-problems')} onClick={onClose}>
              <FontAwesomeIcon icon={faCode} className="mr-3 w-5" />
              编程题库
            </Link>
            <Link href="/problem-lists" className={getLinkClass('/problem-lists')} onClick={onClose}>
              <FontAwesomeIcon icon={faListAlt} className="mr-3 w-5" />
              题单
            </Link>
            <Link href="/exams" className={getLinkClass('/exams')} onClick={onClose}>
              <FontAwesomeIcon icon={faClipboardList} className="mr-3 w-5" />
              模拟考试
            </Link>
            <Link href="/news-list" className={getLinkClass('/news-list')} onClick={onClose}>
              <FontAwesomeIcon icon={faNewspaper} className="mr-3 w-5" />
              编程资讯
            </Link>
            <Link href="/membership" className={getLinkClass('/membership')} onClick={onClose}>
              <FontAwesomeIcon icon={faCrown} className="mr-3 w-5" />
              会员服务
            </Link>
          </nav>
          
          {/* 账户相关链接（仅登录用户可见） */}
          {isAuthenticated && (
            <div className="border-t border-gray-200 pt-4">
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2 px-4">
                个人中心
              </h3>
              <Link href="/profile" className="flex items-center py-3 px-4 text-gray-600 hover:text-indigo-600 hover:bg-gray-50" onClick={onClose}>
                <FontAwesomeIcon icon={faUser} className="mr-3 w-5" />
                个人资料
              </Link>
              <Link href="/progress" className="flex items-center py-3 px-4 text-gray-600 hover:text-indigo-600 hover:bg-gray-50" onClick={onClose}>
                <FontAwesomeIcon icon={faChartLine} className="mr-3 w-5" />
                学习进度
              </Link>
              <Link href="/exam-records" className="flex items-center py-3 px-4 text-gray-600 hover:text-indigo-600 hover:bg-gray-50" onClick={onClose}>
                <FontAwesomeIcon icon={faClipboardList} className="mr-3 w-5" />
                考试记录
              </Link>
              <Link href="/settings" className="flex items-center py-3 px-4 text-gray-600 hover:text-indigo-600 hover:bg-gray-50" onClick={onClose}>
                <FontAwesomeIcon icon={faCog} className="mr-3 w-5" />
                账号设置
              </Link>
              <button
                onClick={handleLogout}
                className="w-full flex items-center py-3 px-4 text-red-600 hover:bg-red-50 text-left"
              >
                <FontAwesomeIcon icon={faSignOutAlt} className="mr-3 w-5" />
                退出登录
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NavDrawer; 