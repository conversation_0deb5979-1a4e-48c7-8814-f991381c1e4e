'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBell, faCheckDouble, faCheck, faSpinner } from '@fortawesome/free-solid-svg-icons';
import { useAppSelector, useAppDispatch } from '@/app/redux/hooks';
import { selectIsAuthenticated } from '@/app/redux/features/authSlice';
import { 
  selectHeaderNotifications, 
  selectHeaderUnreadCount,
  selectHeaderIsLoading,
  markNotificationAsRead, 
  markAllNotificationsAsRead,
  fetchHeaderNotifications
} from '@/app/redux/features/notificationSlice';

const NotificationDropdown: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const dispatch = useAppDispatch();
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const notifications = useAppSelector(selectHeaderNotifications);
  const unreadCount = useAppSelector(selectHeaderUnreadCount);
  const isLoading = useAppSelector(selectHeaderIsLoading);
  
  const recentNotifications = notifications.slice(0, 5);

  // 首次加载获取最新通知
  useEffect(() => {
    if (isAuthenticated) {
      dispatch(fetchHeaderNotifications());
    }
  }, [isAuthenticated, dispatch]);

  // 打开下拉菜单时刷新通知
  useEffect(() => {
    if (isOpen && isAuthenticated) {
      dispatch(fetchHeaderNotifications());
    }
  }, [isOpen, isAuthenticated, dispatch]);

  useEffect(() => {
    // 点击外部关闭下拉框
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 将所有通知标记为已读
  const handleMarkAllAsRead = (e: React.MouseEvent) => {
    e.stopPropagation();
    dispatch(markAllNotificationsAsRead())
      .unwrap()
      .then(() => {
        // 标记全部已读后会自动刷新头部通知
      });
  };

  // 将单个通知标记为已读
  const handleMarkAsRead = (id: number, e: React.MouseEvent) => {
    e.stopPropagation();
    dispatch(markNotificationAsRead(id));
  };

  if (!isAuthenticated) {
    return null;
  }

  // 获取通知类型对应的样式
  const getNotificationTypeStyle = (type: string) => {
    switch (type) {
      case 'system':
        return 'bg-indigo-100 text-indigo-600';
      case 'contest':
        return 'bg-green-100 text-green-600';
      case 'problem':
        return 'bg-blue-100 text-blue-600';
      case 'interaction':
        return 'bg-purple-100 text-purple-600';
      case 'vip':
        return 'bg-yellow-100 text-yellow-600';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  };

  // 获取通知类型对应的图标
  const getNotificationTypeIcon = (type: string) => {
    switch (type) {
      case 'system':
        return <i className="fas fa-bell"></i>;
      case 'contest':
        return <i className="fas fa-trophy"></i>;
      case 'problem':
        return <i className="fas fa-code"></i>;
      case 'interaction':
        return <i className="fas fa-comment-alt"></i>;
      case 'vip':
        return <i className="fas fa-star"></i>;
      default:
        return <i className="fas fa-info-circle"></i>;
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* 通知图标按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-center w-10 h-10 rounded-full hover:bg-indigo-50 transition-colors duration-200 relative"
        aria-label="通知"
      >
        <FontAwesomeIcon icon={faBell} className="text-gray-600" />
        {unreadCount > 0 && (
          <span className="absolute top-1 right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center text-white text-xs">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </button>

      {/* 通知下拉菜单 */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-xl overflow-hidden z-50">
          <div className="p-4 border-b border-gray-100">
            <div className="flex justify-between items-center">
              <h3 className="font-semibold text-gray-800">通知</h3>
              {unreadCount > 0 && (
                <button
                  onClick={handleMarkAllAsRead}
                  className="text-xs text-indigo-600 hover:text-indigo-800 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <FontAwesomeIcon icon={faSpinner} className="mr-1 animate-spin" />
                  ) : (
                    <FontAwesomeIcon icon={faCheckDouble} className="mr-1" />
                  )}
                  全部已读
                </button>
              )}
            </div>
          </div>

          <div className="overflow-y-auto max-h-96">
            {isLoading ? (
              <div className="py-8 text-center text-gray-500">
                <FontAwesomeIcon icon={faSpinner} spin className="text-indigo-600 text-2xl mb-2" />
                <p>加载中...</p>
              </div>
            ) : recentNotifications.length > 0 ? (
              recentNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 border-b border-gray-100 ${!notification.isRead ? 'bg-indigo-50' : ''}`}
                >
                  <div className="flex">
                    <div className={`flex-shrink-0 mr-3 w-8 h-8 rounded-full ${getNotificationTypeStyle(notification.type)} flex items-center justify-center`}>
                      {getNotificationTypeIcon(notification.type)}
                    </div>
                    <div className="flex-grow">
                      <div className="flex justify-between">
                        <h4 className="font-medium text-sm text-gray-800">
                          {notification.title}
                          {!notification.isRead && (
                            <span className="ml-2 px-1.5 py-0.5 bg-red-100 text-red-800 text-xs rounded-full">
                              新
                            </span>
                          )}
                        </h4>
                        <span className="text-xs text-gray-500">
                          {notification.date.split(' ')[0]}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 line-clamp-2 mt-1">
                        {notification.content}
                      </p>
                      <div className="mt-2 flex justify-between items-center">
                        {notification.link ? (
                          <a 
                            href={notification.link} 
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-xs text-indigo-600 hover:text-indigo-800"
                          >
                            查看详情
                          </a>
                        ) : (
                          <Link
                            href={`/notifications/${notification.id}`}
                            className="text-xs text-indigo-600 hover:text-indigo-800"
                          >
                            查看详情
                          </Link>
                        )}
                        {!notification.isRead && (
                          <button
                            onClick={(e) => handleMarkAsRead(notification.id, e)}
                            className="text-xs text-gray-500 hover:text-indigo-600 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled={isLoading}
                          >
                            {isLoading ? (
                              <FontAwesomeIcon icon={faSpinner} className="mr-1 animate-spin" />
                            ) : (
                              <FontAwesomeIcon icon={faCheck} className="mr-1" />
                            )}
                            标为已读
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="py-8 text-center text-gray-500">
                暂无通知
              </div>
            )}
          </div>

          <div className="p-3 bg-gray-50 border-t border-gray-100 text-center">
            <Link
              href="/notifications"
              className="text-sm text-indigo-600 font-medium hover:text-indigo-800"
            >
              查看全部通知
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationDropdown; 