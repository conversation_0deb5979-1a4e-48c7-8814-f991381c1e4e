import React, { useEffect, useRef, useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheckCircle, faSearch, faFilter, faTimes, faChevronUp, faChevronDown } from '@fortawesome/free-solid-svg-icons';
import { useRouter } from 'next/navigation';
import { Problem } from '@/app/coding-problems/[id]/layout';
import { BaseProblemView, LearningContextFilter, LearningContextParams } from '@/app/service/learning-context-service';
import LearningContextService from '@/app/service/learning-context-service';
import TagSelector from './TagSelector';
import TagService, { TagGroup } from '@/app/service/tag-service';
import { useAppSelector } from '@/app/redux/hooks';
import { selectIsAuthenticated } from '@/app/redux/features/authSlice';

// 扩展 Problem 类型，添加 isCompleted 属性
interface ExtendedProblem extends Problem {
  isCompleted?: boolean;
  problemType: number;
}

interface ProblemDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onChange: (problemType: number, problemId: string) => void;
  currentProblemId: string;
  currentProblemType: number;
  envTitle?: string;
  envId?: number;
  envType?: string;
}

// 获取难度对应的样式
const getDifficultyStyle = (difficulty: number) => {
  switch (difficulty) {
    case 1:
      return 'bg-green-100 text-green-800';
    case 2:
      return 'bg-yellow-100 text-yellow-800';
    case 3:
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// 获取难度文本
const getDifficultyText = (difficulty: number) => {
  switch (difficulty) {
    case 1:
      return '简单';
    case 2:
      return '中等';
    case 3:
      return '困难';
    default:
      return '未知';
  }
};

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 1:
      return '未开始';
    case 2:
      return '已解答';
    case 3:
      return '尝试中';
    default:
      return '全部';
  }
};

const ProblemDrawer: React.FC<ProblemDrawerProps> = ({
  isOpen,
  onClose,
  onChange,
  currentProblemId,
  currentProblemType,
  envTitle = '题库',
  envId,
  envType
}) => {
  const router = useRouter();
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const [problems, setProblems] = useState<ExtendedProblem[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadMore, setLoadMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [pageNum, setPageNum] = useState(1);
  const [filter, setFilter] = useState<LearningContextFilter>({
    pageNum: 1,
    pageSize: 100
  });
  const drawerRef = useRef<HTMLDivElement>(null);
  const prevEnvRef = useRef({ envId, envType });

  // 筛选相关状态
  const [searchKeyword, setSearchKeyword] = useState('');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [selectedTags, setSelectedTags] = useState<number[]>([]);
  const [selectedDifficulty, setSelectedDifficulty] = useState<number | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<number | null>(null);
  const [tagOptions, setTagOptions] = useState<TagGroup[]>([]);
  const [isTagSelectorOpen, setIsTagSelectorOpen] = useState(false);
  const [selectedTagNames, setSelectedTagNames] = useState<string[]>([]);
  const tagSelectorRef = useRef<HTMLDivElement>(null);
  const filterRef = useRef<HTMLDivElement>(null);
  const [isLoaded, setIsLoaded] = useState<boolean>(false)

  // 从 sessionStorage 加载筛选条件
  useEffect(() => {
    if (isOpen && !isLoaded) {
      const savedFilters = sessionStorage.getItem(`problemFilters_${currentProblemType}`);
      let filterKeyword = ''
      let filterTags = []
      let filterDifficulty = null
      let filterStatus = null
      let filterTagNames = []
      if (savedFilters) {
        const { keyword, tags, difficulty, status, tagNames } = JSON.parse(savedFilters);
        filterKeyword = keyword || ''
        filterTags = tags || []
        filterDifficulty = difficulty || null
        filterStatus = status || null
        filterTagNames = tagNames || []
      }

      setSearchKeyword(filterKeyword || '');
      setSelectedTags(filterTags || []);
      setSelectedDifficulty(filterDifficulty);
      setSelectedStatus(filterStatus);
      setSelectedTagNames(filterTagNames || []);

      setFilter(prev => ({
        ...prev,
        keyword: filterKeyword,
        tags: filterTags,
        difficulty: filterDifficulty,
        status: filterStatus,
        pageNum: 1
      }));
    }
  }, [isOpen, isLoaded, envId, currentProblemType]);

  // 保存筛选条件到 sessionStorage
  useEffect(() => {
    if (isOpen && !envId) {
      const filters = {
        keyword: searchKeyword,
        tags: selectedTags,
        difficulty: selectedDifficulty,
        status: selectedStatus,
        tagNames: selectedTagNames
      };
      sessionStorage.setItem(`problemFilters_${currentProblemType}`, JSON.stringify(filters));
    }
  }, [searchKeyword, selectedTags, selectedDifficulty, selectedStatus, selectedTagNames, isOpen, envId, currentProblemType]);

  // 加载题目列表
  const loadProblems = async (isLoadMore = false, isFilter = false) => {
    if (loading || (!isLoadMore && !hasMore && !isFilter)) return;

    setLoading(true);
    setIsLoaded(true);
    setLoadMore(isLoadMore);
    try {
      const params: LearningContextParams = {
        currentQuestion: currentProblemId,
        currentQuestionType: currentProblemType, // 编程题
        envType: envType,
        envId: envId,
        filter: {
          ...filter,
          pageNum: isLoadMore ? pageNum + 1 : 1
        }
      };

      const response = await LearningContextService.getPanelQuestionList(params);
      const newProblems = response.data.problems.map((problem: BaseProblemView) => ({
        id: problem.displayId,
        title: problem.title,
        difficulty: problem.difficulty,
        categoryName: 1, // 默认值，实际应该从接口获取
        tags: problem.tags,
        problemType: problem.problemType,
        isCompleted: false // 默认值，实际应该从接口获取
      }));

      if (isLoadMore) {
        setProblems(prev => [...prev, ...newProblems]);
        setPageNum(prev => prev + 1);
      } else {
        setProblems(newProblems);
        setPageNum(1);
      }

      setHasMore(response.data.hasMore);
    } catch (error) {
      console.error('加载题目列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 监听滚动事件，实现滚动加载
  useEffect(() => {
    const handleScroll = () => {
      if (!drawerRef.current) return;

      const { scrollTop, scrollHeight, clientHeight } = drawerRef.current;
      // 当滚动到距离底部 50px 时加载更多
      if (scrollHeight - scrollTop - clientHeight < 50 && hasMore && !loading) {
        loadProblems(true);
      }
    };

    const drawerElement = drawerRef.current;
    if (drawerElement) {
      drawerElement.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (drawerElement) {
        drawerElement.removeEventListener('scroll', handleScroll);
      }
    };
  }, [hasMore, loading, pageNum]);

  // 只在抽屉打开且环境变化时加载题目列表
  // useEffect(() => {
  //   const envChanged = prevEnvRef.current.envId !== envId || prevEnvRef.current.envType !== envType;
  //   console.log(isOpen, envChanged)
  //   if (isOpen && !isLoaded) {
  //     setIsLoaded(true)
  //     loadProblems();
  //     prevEnvRef.current = { envId, envType };
  //   }
  // }, [isOpen, envId, envType]);

  // 加载标签数据
  useEffect(() => {
    if (isOpen && !isLoaded && !envId) {
      TagService.getTagList(currentProblemType).then(resp => {
        setTagOptions(resp.data as unknown as TagGroup[]);
      });
    }
  }, [isOpen]);

  // 处理题目点击
  const handleProblemClick = (problem: ExtendedProblem) => {
    const problemId = problem.id
    const problemType = problem.problemType
    let url = `${problemType === 1 ? '/basic-problems' : '/coding-problems'}/${problemId}`
    if (envId && envType) {
      url += `?envType=${envType}&envId=${envId}`
    }
    if (currentProblemType === problemType) {
      // 使用 window.history.pushState 更新 URL
      window.history.pushState({}, '', url);

      // 触发一个自定义事件，通知其他组件 URL 已更新
      window.dispatchEvent(new CustomEvent('urlChange', {detail: {problemId, problemType}}));

      // 不关闭抽屉，保持状态
      onClose()
      onChange(2, problemId)
    } else {
      router.push(url)
    }
  };

  // 重置筛选条件
  const resetFilters = () => {
    setSearchKeyword('');
    setSelectedTags([]);
    setSelectedTagNames([]);
    setSelectedDifficulty(null);
    setSelectedStatus(null);
    setFilter(prev => ({
      ...prev,
      keyword: undefined,
      tags: undefined,
      difficulty: undefined,
      status: undefined,
      pageNum: 1
    }));
    setPageNum(1);
    loadProblems();
    setIsFilterOpen(false);
    setIsTagSelectorOpen(false);
    // 清除保存的筛选条件
    sessionStorage.removeItem(`problemFilters_${currentProblemType}`);
  };

  // 处理搜索
  const handleSearch = () => {
    setFilter(prev => ({
      ...prev,
      keyword: searchKeyword,
      tags: selectedTags.length > 0 ? selectedTags : undefined,
      difficulty: selectedDifficulty !== null ? selectedDifficulty : undefined,
      status: selectedStatus !== null ? selectedStatus : undefined,
      pageNum: 1
    }));
    setPageNum(1);
    setIsFilterOpen(false);
    setIsTagSelectorOpen(false);
  };

  // 监听 filter 变化，当 filter 更新后加载数据
  useEffect(() => {
    console.log(isOpen)
    if (isOpen) {
      loadProblems(false, true);
    }
  }, [filter]);

  // 处理标签变化
  const handleTagsChange = (tags: number[]) => {
    setSelectedTags(tags);

    // 更新选中的标签名称
    const tagNames: string[] = [];
    tagOptions.forEach(group => {
      group.tags.forEach(tag => {
        if (tags.includes(tag.id)) {
          tagNames.push(tag.name);
        }
      });
    });
    setSelectedTagNames(tagNames);
  };

  // 处理筛选弹窗关闭
  const handleFilterClose = () => {
    setIsFilterOpen(false);
    setIsTagSelectorOpen(false);
  };

  // 点击外部关闭标签选择器
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (tagSelectorRef.current && !tagSelectorRef.current.contains(event.target as Node)) {
        setIsTagSelectorOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 点击外部关闭筛选窗口
  useEffect(() => {
    const handleFilterClickOutside = (event: MouseEvent) => {
      if (filterRef.current && !filterRef.current.contains(event.target as Node)) {
        setIsFilterOpen(false);
      }
    };

    if (isFilterOpen) {
      document.addEventListener('mousedown', handleFilterClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleFilterClickOutside);
    };
  }, [isFilterOpen]);

  return (
    <>
      {/* 遮罩层 */}
      <div
        className={`fixed inset-0 bg-black/50 z-40 transition-opacity duration-300 ${
          isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
        onClick={onClose}
      ></div>

      {/* 抽屉内容 */}
      <div
        className={`fixed left-0 top-0 h-full w-128 bg-white shadow-xl z-50 flex flex-col transform transition-transform duration-300 ease-in-out ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        {/* 抽屉头部 */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold text-gray-800">{envTitle}</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* 搜索和筛选区域 */}
          {
            !envId && (<div className="flex items-center space-x-2">
              <div className="relative flex-1">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FontAwesomeIcon icon={faSearch} className="text-gray-400 text-sm" />
                </div>
                <input
                  type="text"
                  placeholder="搜索题目"
                  className="w-full py-1.5 pl-8 pr-3 text-sm bg-gray-100 rounded-full focus:outline-none focus:ring-1 focus:ring-blue-300"
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
              <button
                onClick={() => setIsFilterOpen(!isFilterOpen)}
                className={`p-1.5 rounded-full ${isFilterOpen ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
              >
                <FontAwesomeIcon icon={faFilter} className="text-sm" />
              </button>
            </div>)
          }
        </div>

        {/* 筛选弹窗 */}
        {isFilterOpen && (
          <div ref={filterRef} className="absolute top-25 left-4 right-4 bg-white rounded-lg shadow-lg z-50 p-4 border border-gray-200">
            <div className="flex justify-between items-center mb-3">
              <h3 className="font-medium text-gray-800">筛选条件</h3>
              <button
                onClick={handleFilterClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>

            {/* 难度选择 */}
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">难度</h4>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => setSelectedDifficulty(null)}
                  className={`px-3 py-1 rounded-full text-xs ${
                    selectedDifficulty === null
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  全部
                </button>
                {[1, 2, 3].map((level) => (
                  <button
                    key={level}
                    onClick={() => setSelectedDifficulty(level)}
                    className={`px-3 py-1 rounded-full text-xs ${
                      selectedDifficulty === level
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {getDifficultyText(level)}
                  </button>
                ))}
              </div>
            </div>

            {/* 状态选择 */}
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">状态</h4>
              <div className="flex flex-wrap gap-2 items-center">
                <button
                  onClick={() => setSelectedStatus(null)}
                  className={`px-3 py-1 rounded-full text-xs ${
                    selectedStatus === null
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                  disabled={!isAuthenticated}
                >
                  全部
                </button>
                {[1, 2, 3].map((status) => (
                  <button
                    key={status}
                    onClick={() => setSelectedStatus(status)}
                    className={`px-3 py-1 rounded-full text-xs ${
                      selectedStatus === status
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    } ${!isAuthenticated ? 'opacity-50 cursor-not-allowed' : ''}`}
                    disabled={!isAuthenticated}
                  >
                    {getStatusText(status)}
                  </button>
                ))}
                {!isAuthenticated && (
                  <span className="text-xs text-gray-500 ml-2">请登录后使用状态筛选</span>
                )}
              </div>
            </div>

            {/* 标签选择 */}
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">标签</h4>
              <div className="relative" ref={tagSelectorRef}>
                <div
                  className="w-full py-1.5 px-3 text-sm bg-gray-100 rounded-md border border-gray-200 cursor-pointer flex items-center justify-between"
                  onClick={() => setIsTagSelectorOpen(!isTagSelectorOpen)}
                >
                  <div className="flex flex-wrap gap-1 max-w-[90%]">
                    {selectedTagNames.length > 0 ? (
                      <>
                        {selectedTagNames.slice(0, 2).map((name, index) => (
                          <span key={index} className="bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded-full text-xs">
                            {name}
                          </span>
                        ))}
                        {selectedTagNames.length > 2 && (
                          <span className="bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded-full text-xs">
                            +{selectedTagNames.length - 2}
                          </span>
                        )}
                      </>
                    ) : (
                      <span className="text-gray-500">选择标签</span>
                    )}
                  </div>
                  <FontAwesomeIcon
                    icon={isTagSelectorOpen ? faChevronUp : faChevronDown}
                    className="text-gray-400 text-xs"
                  />
                </div>

                {/* 标签选择器弹窗 */}
                {isTagSelectorOpen && (
                  <div className="absolute top-full left-0 right-0 mt-1 bg-white rounded-md shadow-lg border border-gray-200 z-50">
                    <TagSelector
                      tags={tagOptions}
                      initialSelectedTags={selectedTags}
                      onTagsChange={handleTagsChange}
                      className="max-h-60"
                    />
                  </div>
                )}
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-between mt-4">
              <button
                onClick={resetFilters}
                className="px-3 py-1.5 text-xs text-gray-600 hover:text-gray-800"
              >
                重置
              </button>
              <button
                onClick={handleSearch}
                className="px-4 py-1.5 bg-blue-500 text-white text-xs rounded-md hover:bg-blue-600"
              >
                应用筛选
              </button>
            </div>
          </div>
        )}

        {/* 题目列表 */}
        <div ref={drawerRef} className="flex-1 overflow-auto">
          {(loading && !loadMore) ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-500 border-t-transparent"></div>
            </div>
          ) : (
            <>
              {problems.map((problem) => (
                <div
                  key={problem.id}
                  onClick={() => handleProblemClick(problem)}
                  className={`block p-4 hover:bg-gray-50 border-b border-gray-200 cursor-pointer ${
                    currentProblemId === problem.id ? 'bg-indigo-50' : ''
                  }`}
                >
                  <div className="flex items-center space-x-2 mb-2 overflow-hidden text-ellipsis whitespace-nowrap">
                      {problem.isCompleted && (
                        <FontAwesomeIcon icon={faCheckCircle} className="text-green-500" />
                      )}
                      <span className={`text-[12px] rounded-full px-2 py-1 ${problem.problemType == 1 ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}`}>{ problem.problemType == 1 ? '基础题' : '编程题' }</span>
                    
                      <span className="text-gray-500 text-sm">{problem.id}.</span>
                      <h3 className="font-medium text-gray-900">{problem.title}</h3>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded-full text-xs ${getDifficultyStyle(problem.difficulty)}`}>
                      {getDifficultyText(problem.difficulty)}
                    </span>
                    <div className='flex items-center space-x-2'>
                    {
                      problem.tags && problem.tags.slice(0, 3).map((tag) => (
                        <span key={tag.id} className="bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded-full text-xs">
                          {tag.name}
                        </span>
                      ))
                    }
                    {
                      problem.tags && problem.tags.length > 3 && (
                        <span className="bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded-full text-xs">
                          +{problem.tags.length - 3}
                        </span>
                      )
                    }
                    </div>
                  </div>
                </div>
              ))}

              {/* 加载更多状态 */}
              {loading && loadMore && (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-2 border-blue-500 border-t-transparent"></div>
                  <span className="ml-2 text-sm text-gray-500">加载更多...</span>
                </div>
              )}

              {/* 没有更多数据 */}
              {!loading && !hasMore && problems.length > 0 && (
                <div className="p-4 text-center text-gray-500">
                  没有更多题目了
                </div>
              )}

              {/* 空状态 */}
              {!loading && problems.length === 0 && (
                <div className="p-4 text-center text-gray-500">
                  暂无题目
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default ProblemDrawer;
