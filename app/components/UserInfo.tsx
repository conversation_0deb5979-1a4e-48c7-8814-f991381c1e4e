'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAppSelector, useAppDispatch } from '@/app/redux/hooks';
import { selectUser, selectIsAuthenticated, logout } from '@/app/redux/features/authSlice';
import Image from 'next/image';
import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUser,
  faSignOutAlt,
  faCog,
  faClipboardList,
  faChartLine,
  faUsers,
  faComments
} from '@fortawesome/free-solid-svg-icons';
import { checkAuth } from '../utils/authCheck';
import FeedbackModal from './feedback/FeedbackModal';

const UserInfo: React.FC = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const user = useAppSelector(selectUser);
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);

  const handleLogout = async () => {
    try {
      await dispatch(logout());
      router.push('/login');
    } catch (error) {
      console.error('登出失败:', error);
    }
  };

  if (!isAuthenticated || !user) {
    return (
      <div className="flex items-center space-x-4">
        <Link href="/login" className="text-indigo-600 hover:text-indigo-800">
          登录
        </Link>
        <Link href="/login" className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
          注册
        </Link>
      </div>
    );
  }

  return (
    <div className="relative group">
      <div className="flex items-center space-x-3 cursor-pointer">
        <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-200">
          {user.avatar ? (
            <Image
              src={'https:' + user.avatar}
              alt={user.nickname || '用户头像'}
              width={40}
              height={40}
              className="object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-indigo-100 text-indigo-600">
              <FontAwesomeIcon icon={faUser} />
            </div>
          )}
        </div>
        <span className="font-medium">{user.nickname}</span>
      </div>

      {/* 下拉菜单 */}
      <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg overflow-hidden z-10 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
        <div className="py-2">
          <div className="px-4 py-3 border-b border-gray-100">
            <p className="text-sm font-medium text-gray-900">{user.nickname}</p>
            {/* <p className="text-xs text-gray-500 truncate">{user.email}</p> */}
          </div>
          <Link href="/profile" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
            <FontAwesomeIcon icon={faUser} className="mr-2" />
            个人资料
          </Link>
          {/* <Link href="/teams" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
            <FontAwesomeIcon icon={faUsers} className="mr-2" />
            我的团队
          </Link> */}
          <div className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer" onClick={() => {
            if (checkAuth(3)) {
              router.push('/progress')
            }
          }}>
            <FontAwesomeIcon icon={faChartLine} className="mr-2" />
            学习进度
          </div>
          <Link href="/exam-records" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
            <FontAwesomeIcon icon={faClipboardList} className="mr-2" />
            考试记录
          </Link>
          <Link href="/settings" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
            <FontAwesomeIcon icon={faCog} className="mr-2" />
            账号设置
          </Link>
          <button
            onClick={() => setIsFeedbackModalOpen(true)}
            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
          >
            <FontAwesomeIcon icon={faComments} className="mr-2" />
            意见反馈
          </button>
          <button
            onClick={handleLogout}
            className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
          >
            <FontAwesomeIcon icon={faSignOutAlt} className="mr-2" />
            退出登录
          </button>
        </div>
      </div>

      {/* 反馈Modal */}
      <FeedbackModal
        isOpen={isFeedbackModalOpen}
        onClose={() => setIsFeedbackModalOpen(false)}
      />
    </div>
  );
};

export default UserInfo;
