'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import {useAppDispatch, useAppSelector} from '@/app/redux/hooks';
import {selectIsAuthenticated, selectUser, setUser, UserInfo} from '@/app/redux/features/authSlice';
import ProfileService from '@/app/service/profile-service';

// 缓存检查结果，避免频繁调用API
let lastCheckTime = 0;
let cachedHasBindMobile = false;
const CACHE_DURATION = 60 * 1000; // 缓存时间1分钟

// 存储原始访问路径，用于绑定手机号后返回
let originalPath: string | null = null;

/**
 * 授权检查组件，用于全局检查用户是否已登录但未绑定手机号
 * 如果用户已登录但未绑定手机号，将自动重定向到账户设置页面
 */
export default function AuthCheck() {
  const router = useRouter();
  const pathname = usePathname();
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const user = useAppSelector(selectUser);
  const [isChecking, setIsChecking] = useState(true);
  const dispatch = useAppDispatch();

  useEffect(() => {
    // 监听手机绑定成功事件
    const handleMobileBindSuccess = () => {
      console.log('检测到手机绑定成功事件，重置缓存');
      lastCheckTime = 0;
      cachedHasBindMobile = true;
    };

    window.addEventListener('mobileBindSuccess', handleMobileBindSuccess);

    return () => {
      window.removeEventListener('mobileBindSuccess', handleMobileBindSuccess);
    };
  }, []);

  useEffect(() => {
    // 需要排除的路径，避免无限重定向
    const excludedPaths = ['/settings', '/login', '/register', '/logout'];

    // 在设置页面检查是否有待返回的路径
    if (pathname === '/settings') {
      // 重置缓存，确保下次检查会获取最新的绑定状态
      lastCheckTime = 0;

      // 如果是从其他页面重定向过来的，添加返回提示
      if (originalPath && originalPath !== '/settings') {
        // 这里可以添加UI提示，表明绑定成功后将返回原页面
        console.log('绑定成功后将返回：', originalPath);
      }
      setIsChecking(false);
      return;
    }

    // 如果当前路径已经是被排除的路径，则不进行检查
    if (excludedPaths.some(path => pathname.startsWith(path))) {
      setIsChecking(false);
      return;
    }

    // 如果用户已登录，检查是否绑定了手机号
    if (isAuthenticated && user) {
      const now = Date.now();

      // 检查缓存是否有效
      if (now - lastCheckTime < CACHE_DURATION) {
        // 使用缓存结果
        if (!cachedHasBindMobile) {
          // 存储当前路径，用于绑定成功后返回
          originalPath = pathname;
          router.push('/settings');
        }
        setIsChecking(false);
        return;
      }

      // 缓存失效，重新获取用户资料
      ProfileService.getProfile()
        .then(response => {
          const profile = response.data;
          // 转换 Profile 到 UserInfo
          const userInfo: UserInfo = {
            id: user?.id || 0,
            nickname: profile.nickname,
            email: user?.email || '',
            mobile: profile.mobile,
            avatar: profile.avatar,
            name: profile.name,
            sex: profile.sex,
            age: profile.age?.toString(),
            wechatBound: profile.hasBindWeChat,
            currentMembershipLevel: profile.currentMembershipLevel,
            membershipExpireTime: profile.membershipExpireTime?.toString()
          };
          dispatch(setUser(userInfo));

          // 更新缓存
          lastCheckTime = now;
          cachedHasBindMobile = profile.hasBindMobile;

          // 如果未绑定手机号，重定向到账户设置页面
          if (!profile.hasBindMobile) {
            // 存储当前路径，用于绑定成功后返回
            originalPath = pathname;
            router.push('/settings');
          } else if (pathname === '/settings' && originalPath && originalPath !== '/settings') {
            // 如果在设置页面，且有原始路径，则返回原始路径
            const pathToReturn = originalPath;
            originalPath = null; // 清除原始路径
            router.push(pathToReturn);
          }
        })
        .catch(error => {
          console.error('获取用户资料失败:', error);
        })
        .finally(() => {
          setIsChecking(false);
        });
    } else {
      setIsChecking(false);
    }
  }, [isAuthenticated, user, pathname, router, dispatch]);

  // 只是一个功能组件，不渲染任何UI
  return null;
}
