import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import { ToastType } from './Toast';

// 声明事件类型
export interface ToastEvent {
  type: 'SHOW_TOAST' | 'HIDE_TOAST';
  payload: any;
}

// 创建单例事件发射器
class ToastEventEmitter extends EventEmitter {
  private static instance: ToastEventEmitter;

  private constructor() {
    super();
    // 设置最大监听器数量，避免警告
    this.setMaxListeners(20);
  }

  public static getInstance(): ToastEventEmitter {
    if (!ToastEventEmitter.instance) {
      ToastEventEmitter.instance = new ToastEventEmitter();
    }
    return ToastEventEmitter.instance;
  }

  // 发射Toast事件
  public emitToastEvent(event: ToastEvent): void {
    this.emit('toast', event);
  }

  // 监听Toast事件
  public onToastEvent(listener: (event: ToastEvent) => void): void {
    this.on('toast', listener);
  }

  // 移除Toast事件监听
  public offToastEvent(listener: (event: ToastEvent) => void): void {
    this.off('toast', listener);
  }
}

// 全局Toast服务
export const toastService = {
  // 显示Toast通知
  showToast: (message: string, type: ToastType = 'info', duration: number = 3000): string => {
    const id = uuidv4();
    ToastEventEmitter.getInstance().emitToastEvent({
      type: 'SHOW_TOAST',
      payload: {
        id,
        message,
        type,
        duration
      }
    });
    return id;
  },

  // 隐藏特定Toast
  hideToast: (id: string): void => {
    ToastEventEmitter.getInstance().emitToastEvent({
      type: 'HIDE_TOAST',
      payload: { id }
    });
  },

  // 快捷方法：显示成功Toast
  success: (message: string, duration: number = 3000): string => {
    return toastService.showToast(message, 'success', duration);
  },

  // 快捷方法：显示错误Toast
  error: (message: string, duration: number = 3000): string => {
    return toastService.showToast(message, 'error', duration);
  },

  // 快捷方法：显示警告Toast
  warning: (message: string, duration: number = 3000): string => {
    return toastService.showToast(message, 'warning', duration);
  },

  // 快捷方法：显示信息Toast
  info: (message: string, duration: number = 3000): string => {
    return toastService.showToast(message, 'info', duration);
  },

  // 获取事件发射器实例（用于Provider连接）
  getEmitter: (): ToastEventEmitter => {
    return ToastEventEmitter.getInstance();
  }
};

export default toastService; 