'use client';

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { ToastItem } from './Toast';
import dynamic from 'next/dynamic';

// 动态导入带动画效果的Toast组件
// 这样可以避免SSR时framer-motion的兼容性问题
const DynamicToast = dynamic(() => import('./Toast'), { ssr: false });

// ToastContainer组件属性
interface ToastContainerProps {
  toasts: ToastItem[];
  removeToast: (id: string) => void;
}

// Toast容器组件
const ToastContainer: React.FC<ToastContainerProps> = ({ toasts, removeToast }) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  if (!isMounted || typeof window === 'undefined') return null;

  return createPortal(
    <DynamicToast toasts={toasts} removeToast={removeToast} />,
    document.body
  );
};

export default ToastContainer; 