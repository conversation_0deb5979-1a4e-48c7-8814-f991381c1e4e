'use client';

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { 
  XMarkIcon, 
  CheckCircleIcon, 
  ExclamationCircleIcon, 
  InformationCircleIcon, 
  ExclamationTriangleIcon 
} from '@heroicons/react/24/outline';

// 定义Toast类型
export type ToastType = 'success' | 'error' | 'warning' | 'info';

// 定义单个Toast项的接口
export interface ToastItem {
  id: string;
  message: string;
  type: ToastType;
  duration?: number;
}

// Toast组件属性
interface ToastProps {
  toasts: ToastItem[];
  removeToast: (id: string) => void;
}

// 样式配置
const toastTypeConfig: Record<ToastType, { icon: React.ReactNode; bgColor: string; textColor: string }> = {
  success: {
    icon: <CheckCircleIcon className="w-5 h-5" />,
    bgColor: 'bg-green-50 dark:bg-green-900/30',
    textColor: 'text-green-800 dark:text-green-200',
  },
  error: {
    icon: <ExclamationCircleIcon className="w-5 h-5" />,
    bgColor: 'bg-red-50 dark:bg-red-900/30',
    textColor: 'text-red-800 dark:text-red-200',
  },
  warning: {
    icon: <ExclamationTriangleIcon className="w-5 h-5" />,
    bgColor: 'bg-yellow-50 dark:bg-yellow-900/30',
    textColor: 'text-yellow-800 dark:text-yellow-200',
  },
  info: {
    icon: <InformationCircleIcon className="w-5 h-5" />,
    bgColor: 'bg-blue-50 dark:bg-blue-900/30',
    textColor: 'text-blue-800 dark:text-blue-200',
  },
};

// 单个Toast项组件
const ToastItem: React.FC<{ toast: ToastItem; onClose: () => void }> = ({ toast, onClose }) => {
  const { type, message } = toast;
  const { icon, bgColor, textColor } = toastTypeConfig[type];
  const [isVisible, setIsVisible] = useState(false);

  // 添加自动关闭功能
  useEffect(() => {
    setIsVisible(true);
    const timer = setTimeout(() => {
      setIsVisible(false);
      // 给动画一些时间结束
      setTimeout(() => {
        onClose();
      }, 200);
    }, toast.duration || 3000);

    return () => {
      clearTimeout(timer);
    };
  }, [onClose, toast.duration]);

  return (
    <div 
      className={`
        flex items-center p-4 mb-3 rounded-lg shadow-lg ${bgColor} max-w-md w-full 
        transition-all duration-200 ease-in-out
        ${isVisible ? 'opacity-100 translate-y-0 scale-100' : 'opacity-0 -translate-y-4 scale-95'}
      `}
    >
      <div className={`flex-shrink-0 ${textColor}`}>{icon}</div>
      <div className={`ml-3 mr-8 ${textColor} text-sm font-medium flex-grow`}>{message}</div>
      <button
        type="button"
        className={`${textColor} rounded-lg inline-flex h-6 w-6 items-center justify-center hover:bg-black/10`}
        onClick={() => setIsVisible(false)}
      >
        <XMarkIcon className="w-4 h-4" />
      </button>
    </div>
  );
};

// Toast容器组件
const Toast: React.FC<ToastProps> = ({ toasts, removeToast }) => {
  return (
    <div className="fixed top-4 right-4 z-50 flex flex-col items-end">
      {toasts.map((toast) => (
        <ToastItem key={toast.id} toast={toast} onClose={() => removeToast(toast.id)} />
      ))}
    </div>
  );
};

export default Toast; 