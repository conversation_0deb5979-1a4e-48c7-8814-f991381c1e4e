'use client';

import React, { createContext, useContext, useReducer, ReactNode, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { ToastItem, ToastType } from './Toast';
import ToastContainer from './ToastContainer';
import toastService, { ToastEvent } from './toastService';

// Toast上下文类型
interface ToastContextType {
  showToast: (message: string, type: ToastType, duration?: number) => void;
  hideToast: (id: string) => void;
}

// 默认上下文值
const defaultToastContext: ToastContextType = {
  showToast: () => {},
  hideToast: () => {},
};

// Toast状态类型
interface ToastState {
  toasts: ToastItem[];
}

// Toast操作类型
type ToastAction =
  | { type: 'ADD_TOAST'; payload: ToastItem }
  | { type: 'REMOVE_TOAST'; payload: { id: string } };

// Toast状态reducer
const toastReducer = (state: ToastState, action: ToastAction): ToastState => {
  switch (action.type) {
    case 'ADD_TOAST':
      return {
        ...state,
        toasts: [...state.toasts, action.payload],
      };
    case 'REMOVE_TOAST':
      return {
        ...state,
        toasts: state.toasts.filter((toast) => toast.id !== action.payload.id),
      };
    default:
      return state;
  }
};

// 创建Toast上下文
const ToastContext = createContext<ToastContextType>(defaultToastContext);

// Toast提供者组件
interface ToastProviderProps {
  children: ReactNode;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(toastReducer, { toasts: [] });

  // 显示Toast
  const showToast = (message: string, type: ToastType, duration = 3000) => {
    const id = uuidv4();
    dispatch({
      type: 'ADD_TOAST',
      payload: { id, message, type, duration },
    });
  };

  // 隐藏Toast
  const hideToast = (id: string) => {
    dispatch({
      type: 'REMOVE_TOAST',
      payload: { id },
    });
  };

  // 监听toastService发出的事件
  useEffect(() => {
    const handleToastEvent = (event: ToastEvent) => {
      switch (event.type) {
        case 'SHOW_TOAST':
          const { id, message, type, duration } = event.payload;
          dispatch({
            type: 'ADD_TOAST',
            payload: { id, message, type, duration },
          });
          break;
        case 'HIDE_TOAST':
          hideToast(event.payload.id);
          break;
      }
    };

    // 注册事件监听
    const emitter = toastService.getEmitter();
    emitter.onToastEvent(handleToastEvent);

    // 清理函数
    return () => {
      emitter.offToastEvent(handleToastEvent);
    };
  }, []);

  return (
    <ToastContext.Provider value={{ showToast, hideToast }}>
      {children}
      <ToastContainer toasts={state.toasts} removeToast={hideToast} />
    </ToastContext.Provider>
  );
};

// 自定义Hook，用于在组件中使用Toast
export const useToast = () => {
  const context = useContext(ToastContext);
  if (context === undefined) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
}; 