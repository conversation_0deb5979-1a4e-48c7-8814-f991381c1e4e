import React from 'react';

export default function ProgressSkeleton() {
  return (
    <div className="bg-white rounded-xl shadow-md p-6 animate-pulse">
      <div className="flex items-center justify-between mb-4">
        <div className="h-6 w-24 bg-gray-200 rounded"></div>
      </div>
      <div className="flex items-center space-x-4">
        <div className="w-20 h-20 rounded-full bg-gray-200"></div>
        <div className="flex-1">
          <div className="flex justify-between items-center">
            <div className="h-4 w-16 bg-gray-200 rounded mb-1"></div>
            <div className="h-4 w-16 bg-gray-200 rounded mb-1"></div>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 mt-1 mb-2"></div>
          
          <div className="flex justify-between items-center">
            <div className="h-4 w-16 bg-gray-200 rounded mb-1"></div>
            <div className="h-4 w-16 bg-gray-200 rounded mb-1"></div>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 mt-1"></div>
        </div>
      </div>
      <div className="mt-4 pt-4 border-t border-gray-100 flex justify-center">
        <div className="h-4 w-32 bg-gray-200 rounded"></div>
      </div>
    </div>
  );
} 