'use client';

import React, { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import routerService, { RouterEvent } from './routerService';

export const RouterListener: React.FC = () => {
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // 处理路由事件的回调函数
    const handleRouterEvent = (event: RouterEvent) => {
      try {
        switch (event.type) {
          case 'NAVIGATE':
            const { path, options } = event.payload || {};
            if (!path) {
              console.error('No path provided for navigation');
              return;
            }
            
            // 如果是替换模式，使用replace方法
            if (options?.replace) {
              router.replace(path);
            } else {
              router.push(path);
            }
            break;

          case 'REPLACE':
            const replacePath = event.payload?.path;
            if (!replacePath) {
              console.error('No path provided for replace navigation');
              return;
            }
            router.replace(replacePath);
            break;

          case 'BACK':
            router.back();
            break;

          case 'FORWARD':
            router.forward();
            break;

          case 'RELOAD':
            // Next.js Router没有直接的reload方法，使用refresh
            router.refresh();
            break;

          default:
            console.warn(`Unhandled router event type: ${event.type}`);
        }
      } catch (error) {
        console.error('Error handling router event:', error);
      }
    };

    // 注册事件监听
    const emitter = routerService.getEmitter();
    emitter.onRouterEvent(handleRouterEvent);

    // 清理函数
    return () => {
      emitter.offRouterEvent(handleRouterEvent);
    };
  }, [router]);

  // 无需渲染任何UI元素
  return null;
};

export default RouterListener; 