import { EventEmitter } from 'events';

// 导航选项
export interface NavigateOptions {
  replace?: boolean;  // 是否替换当前历史记录
  scroll?: boolean;   // 是否滚动到页面顶部
  shallow?: boolean;  // 是否执行浅层路由更新
}

// 路由事件类型
export interface RouterEvent {
  type: 'NAVIGATE' | 'BACK' | 'FORWARD' | 'REPLACE' | 'RELOAD';
  payload?: any;
}

// 创建单例事件发射器
class RouterEventEmitter extends EventEmitter {
  private static instance: RouterEventEmitter;

  private constructor() {
    super();
    // 设置最大监听器数量，避免警告
    this.setMaxListeners(20);
  }

  public static getInstance(): RouterEventEmitter {
    if (!RouterEventEmitter.instance) {
      RouterEventEmitter.instance = new RouterEventEmitter();
    }
    return RouterEventEmitter.instance;
  }

  // 发射路由事件
  public emitRouterEvent(event: RouterEvent): void {
    this.emit('router', event);
  }

  // 监听路由事件
  public onRouterEvent(listener: (event: RouterEvent) => void): void {
    this.on('router', listener);
  }

  // 移除路由事件监听
  public offRouterEvent(listener: (event: RouterEvent) => void): void {
    this.off('router', listener);
  }
}

// 全局路由服务
export const routerService = {
  // 导航到指定路径
  navigate: (path: string, options?: NavigateOptions): void => {
    RouterEventEmitter.getInstance().emitRouterEvent({
      type: 'NAVIGATE',
      payload: { path, options }
    });
  },

  // 导航到指定路径（替换当前历史记录）
  replace: (path: string, options?: Omit<NavigateOptions, 'replace'>): void => {
    RouterEventEmitter.getInstance().emitRouterEvent({
      type: 'REPLACE',
      payload: { path, options }
    });
  },

  // 导航回上一页
  back: (): void => {
    RouterEventEmitter.getInstance().emitRouterEvent({
      type: 'BACK'
    });
  },

  // 导航到下一页
  forward: (): void => {
    RouterEventEmitter.getInstance().emitRouterEvent({
      type: 'FORWARD'
    });
  },

  // 重新加载当前页面
  reload: (): void => {
    RouterEventEmitter.getInstance().emitRouterEvent({
      type: 'RELOAD'
    });
  },

  // 使用带参数的路径导航
  navigateWithParams: (
    path: string,
    params: Record<string, string | number | boolean>,
    options?: NavigateOptions
  ): void => {
    // 构建URL并对参数进行编码
    const url = new URL(path, window.location.origin);
    Object.entries(params).forEach(([key, value]) => {
      url.searchParams.append(key, String(value));
    });

    RouterEventEmitter.getInstance().emitRouterEvent({
      type: 'NAVIGATE',
      payload: { path: url.pathname + url.search, options }
    });
  },

  // 获取事件发射器实例（用于Router组件连接）
  getEmitter: (): RouterEventEmitter => {
    return RouterEventEmitter.getInstance();
  }
};

// 如果非浏览器环境，提供降级方案
if (typeof window === 'undefined') {
  console.warn('Router service is only fully functional in browser environments');
}

export default routerService;
