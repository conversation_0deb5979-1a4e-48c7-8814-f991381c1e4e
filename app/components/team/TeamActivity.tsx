'use client'

import { TeamActivity as TeamActivityType } from '@/app/types/team'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import Link from 'next/link'

interface TeamActivityProps {
  activity: TeamActivityType
}

export default function TeamActivity({ activity }: TeamActivityProps) {
  return (
    <div className="flex items-start">
      <div className={`w-8 h-8 ${activity.iconBgColor} rounded-full flex items-center justify-center mr-4 flex-shrink-0`}>
        <FontAwesomeIcon icon={activity.icon} className={activity.iconTextColor} />
      </div>
      <div className="flex-grow">
        <div className="flex items-center justify-between mb-1">
          <h4 className="font-medium text-gray-800">{activity.title}</h4>
          <span className="text-xs text-gray-500">{activity.time}</span>
        </div>
        <p className="text-sm text-gray-600">{activity.description}</p>
        {activity.link && (
          <div className="mt-2">
            <Link href={activity.link} className="text-sm text-indigo-600 hover:text-indigo-800">
              查看详情
            </Link>
          </div>
        )}
      </div>
    </div>
  )
} 