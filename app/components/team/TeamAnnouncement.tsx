'use client'

import { useState, useEffect } from 'react'
import { TeamAnnouncement as TeamAnnouncementType } from '@/app/types/team'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faEdit } from '@fortawesome/free-solid-svg-icons'
import EditNoticeModal from './EditNoticeModal'
import teamService from '@/app/service/team-service'
import { useTeam } from '@/app/contexts/TeamContext'
import { toastService } from '@/app/components/Toast'

interface TeamAnnouncementProps {
  teamId: string
  notice?: string
}

export default function TeamAnnouncement({ teamId, notice }: TeamAnnouncementProps) {
  const [announcement, setAnnouncement] = useState<TeamAnnouncementType | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const { team } = useTeam()

  useEffect(() => {
    // 如果有传入 notice，直接使用
    if (notice) {
      setAnnouncement({
        id: '1',
        content: notice,
        updatedAt: new Date().toISOString(),
        pinned: true
      })
      return
    }
  }, [teamId, notice])

  const handleSaveNotice = async (newNotice: string) => {
    try {
      await teamService.updateNotice({
        teamId: parseInt(teamId),
        notice: newNotice
      })
      
      setAnnouncement(prev => prev ? {
        ...prev,
        content: newNotice,
        updatedAt: new Date().toISOString()
      } : null)
      
      toastService.success('公告更新成功')
    } catch (error) {
      toastService.error('公告更新失败')
      throw error
    }
  }

  return (
    <>
      <div className="bg-white rounded-xl shadow-sm p-6 mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-800">团队公告</h2>
          {team?.role === 1 && (
            <button 
              onClick={() => setIsModalOpen(true)}
              className="text-indigo-600 hover:text-indigo-800 text-sm font-medium"
            >
              <FontAwesomeIcon icon={faEdit} className="mr-1" />
              编辑公告
            </button>
          )}
        </div>
        <div className="prose max-w-none text-gray-700 whitespace-pre-wrap">
          {announcement?.content || ''}
        </div>
      </div>

      <EditNoticeModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSave={handleSaveNotice}
        initialNotice={announcement?.content || ''}
      />
    </>
  )
}