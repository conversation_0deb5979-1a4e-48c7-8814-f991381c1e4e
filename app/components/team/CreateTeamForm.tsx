'use client'

import { useState } from 'react'
import type { CreateTeamForm as CreateTeamFormType, TeamType, JoinMethod } from '@/app/types/team'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faGraduationCap, 
  faTrophy, 
  faUsers,
  faLock,
  faUserShield
} from '@fortawesome/free-solid-svg-icons'
import { useRouter } from 'next/navigation'
import teamService from '@/app/service/team-service'
import CommonDropdown, { DropdownOption } from '@/app/components/CommonDropdown'

const initialForm: CreateTeamFormType = {
  name: '',
  description: '',
  type: 'class',
  joinMethod: 'verify',
  password: ''
}

const joinMethodOptions: DropdownOption<JoinMethod>[] = [
  {
    label: '密码加入',
    value: 'password',
    prefix: <FontAwesomeIcon icon={faLock} className="text-indigo-500" />
  },
  {
    label: '审核加入',
    value: 'verify',
    prefix: <FontAwesomeIcon icon={faUserShield} className="text-indigo-500" />
  }
]

export default function CreateTeamForm() {
  const router = useRouter()
  const [form, setForm] = useState<CreateTeamFormType>(initialForm)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setForm(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleTypeChange = (type: TeamType) => {
    setForm(prev => ({
      ...prev,
      type
    }))
  }

  const handleJoinMethodChange = (method: JoinMethod) => {
    setForm(prev => ({
      ...prev,
      joinMethod: method,
      // 如果不是密码方式，清空密码
      password: method === 'password' ? prev.password : ''
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      setIsSubmitting(true)
      await teamService.create({
        name: form.name,
        description: form.description,
        type: form.type === 'class' ? 1 : form.type === 'contest' ? 2 : 3,
        joinType: form.joinMethod === 'password' ? 2 : 1,
        password: form.password || ''
      })
      router.push('/teams')
    } catch (error) {
      console.error('创建团队失败:', error)
      alert('创建团队失败，请重试')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* 基本信息 */}
      <div>
        <h2 className="text-xl font-semibold text-gray-800 mb-4">基本信息</h2>
        <div className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-gray-700 mb-2 font-medium">
              团队名称<span className="text-red-500">*</span>
            </label>
            <input 
              type="text" 
              id="name"
              name="name"
              value={form.name}
              onChange={handleInputChange}
              placeholder="请输入团队名称，最多20个字符" 
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              required
              maxLength={20}
            />
          </div>
          
          <div>
            <label htmlFor="description" className="block text-gray-700 mb-2 font-medium">
              团队简介<span className="text-red-500">*</span>
            </label>
            <textarea 
              id="description"
              name="description"
              value={form.description}
              onChange={handleInputChange}
              placeholder="请简要描述团队的目标和定位，最多200个字符" 
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 h-24"
              required
              maxLength={200}
            />
          </div>
          
          <div>
            <label className="block text-gray-700 mb-2 font-medium">
              团队类型<span className="text-red-500">*</span>
            </label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div 
                className={`border rounded-lg p-4 hover:border-indigo-500 cursor-pointer relative ${
                  form.type === 'class' ? 'border-indigo-500 bg-indigo-50' : 'border-gray-300'
                }`}
                onClick={() => handleTypeChange('class')}
              >
                <div className="flex items-center mb-2">
                  <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center mr-3">
                    <FontAwesomeIcon icon={faGraduationCap} className="text-indigo-600" />
                  </div>
                  <h3 className="font-medium text-gray-800">班级/课程</h3>
                </div>
                <p className="text-sm text-gray-600">适合学校班级、培训课程等教学场景</p>
              </div>
              
              <div 
                className={`border rounded-lg p-4 hover:border-indigo-500 cursor-pointer relative ${
                  form.type === 'contest' ? 'border-indigo-500 bg-indigo-50' : 'border-gray-300'
                }`}
                onClick={() => handleTypeChange('contest')}
              >
                <div className="flex items-center mb-2">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <FontAwesomeIcon icon={faTrophy} className="text-blue-600" />
                  </div>
                  <h3 className="font-medium text-gray-800">竞赛团队</h3>
                </div>
                <p className="text-sm text-gray-600">适合信息学竞赛、程序设计大赛等</p>
              </div>
              
              <div 
                className={`border rounded-lg p-4 hover:border-indigo-500 cursor-pointer relative ${
                  form.type === 'group' ? 'border-indigo-500 bg-indigo-50' : 'border-gray-300'
                }`}
                onClick={() => handleTypeChange('group')}
              >
                <div className="flex items-center mb-2">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                    <FontAwesomeIcon icon={faUsers} className="text-green-600" />
                  </div>
                  <h3 className="font-medium text-gray-800">兴趣小组</h3>
                </div>
                <p className="text-sm text-gray-600">适合志同道合的朋友一起学习编程</p>
              </div>
            </div>
          </div>
          
          <div>
            <label className="block text-gray-700 mb-2 font-medium">
              加入方式<span className="text-red-500">*</span>
            </label>
            <CommonDropdown
              label="加入方式"
              options={joinMethodOptions}
              value={form.joinMethod}
              onChange={handleJoinMethodChange}
              placeholder="请选择加入方式"
              className="w-full"
            />
          </div>
          
          {form.joinMethod === 'password' && (
            <div>
              <label htmlFor="password" className="block text-gray-700 mb-2 font-medium">
                团队密码<span className="text-red-500">*</span>
              </label>
              <input 
                type="password" 
                id="password"
                name="password"
                value={form.password || ''}
                onChange={handleInputChange}
                placeholder="请设置4-16位团队密码" 
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                required={form.joinMethod === 'password'}
                minLength={4}
                maxLength={16}
              />
            </div>
          )}
        </div>
      </div>
      
      {/* 确认创建 */}
      <div className="flex justify-end space-x-4">
        <button 
          type="button" 
          onClick={() => router.back()}
          className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
        >
          取消
        </button>
        <button 
          type="submit" 
          disabled={isSubmitting}
          className={`px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 ${
            isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {isSubmitting ? '创建中...' : '创建团队'}
        </button>
      </div>
    </form>
  )
} 