'use client'

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faLaptopCode,
  faUsers,
  faBook,
  faFileAlt,
  faCog,
  faShareAlt,
  faCrown
} from '@fortawesome/free-solid-svg-icons'
import Link from 'next/link'
import { TeamDetailView } from '@/app/service/team-service'

interface TeamHeaderProps {
  team: TeamDetailView
}

export default function TeamHeader({ team }: TeamHeaderProps) {
  return (
    <div className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between">
          <div className="flex items-center mb-4 md:mb-0">
            <div className="w-20 h-20 bg-white rounded-xl flex items-center justify-center shadow-md mr-5">
              <FontAwesomeIcon icon={faLaptopCode} className="text-indigo-600 text-4xl" />
            </div>
            <div>
              <div className="flex items-center">
                <h1 className="text-3xl font-bold">{team.name}</h1>
                {team.role === 1 && (
                  <span className="ml-3 bg-white/30 text-xs rounded-full px-2 py-1">
                    <FontAwesomeIcon icon={faCrown} className="mr-1" />
                    我创建的
                  </span>
                )}
              </div>
              <p className="text-indigo-100 mt-1">ID: {team.visibleTeamId} · 创建于 {team.createdTime}</p>
              <div className="flex items-center mt-2 space-x-4">
                <div className="flex items-center">
                  <FontAwesomeIcon icon={faUsers} className="mr-1" />
                  <span>{team.teamUserCount}名成员</span>
                </div>
                <div className="flex items-center">
                  <FontAwesomeIcon icon={faBook} className="mr-1" />
                  <span>{team.homeworkCount}个作业</span>
                </div>
                <div className="flex items-center">
                  <FontAwesomeIcon icon={faFileAlt} className="mr-1" />
                  <span>{team.problemListCount}个题单</span>
                </div>
              </div>
            </div>
          </div>
          <div className="flex space-x-2">
            {team.role === 1 && (
              <Link 
                href={`/teams/${team.visibleTeamId}/manage`} 
                className="bg-white text-indigo-600 px-4 py-2 rounded-lg font-medium hover:bg-opacity-90 transition flex items-center"
              >
                <FontAwesomeIcon icon={faCog} className="mr-2" />
                团队管理
              </Link>
            )}
            <button className="bg-white/20 px-4 py-2 rounded-lg hover:bg-opacity-30 transition">
              <FontAwesomeIcon icon={faShareAlt} className="mr-1" />
              邀请
            </button>
          </div>
        </div>
      </div>
    </div>
  )
} 