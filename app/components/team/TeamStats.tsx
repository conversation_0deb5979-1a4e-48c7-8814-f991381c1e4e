'use client'

import { TeamStats as TeamStatsType } from '@/app/types/team'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faUsers, faCrown, faCode } from '@fortawesome/free-solid-svg-icons'

interface TeamStatsProps {
  stats: TeamStatsType
}

export default function TeamStats({ stats }: TeamStatsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex items-center">
          <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mr-4">
            <FontAwesomeIcon icon={faUsers} className="text-indigo-600 text-xl" />
          </div>
          <div>
            <div className="text-2xl font-bold text-gray-800">{stats.joinedTeams}</div>
            <div className="text-sm text-gray-500">已加入的团队</div>
          </div>
        </div>
      </div>
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex items-center">
          <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
            <FontAwesomeIcon icon={faCrown} className="text-green-600 text-xl" />
          </div>
          <div>
            <div className="text-2xl font-bold text-gray-800">{stats.createdTeams}</div>
            <div className="text-sm text-gray-500">我创建的团队</div>
          </div>
        </div>
      </div>
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex items-center">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
            <FontAwesomeIcon icon={faCode} className="text-blue-600 text-xl" />
          </div>
          <div>
            <div className="text-2xl font-bold text-gray-800">{stats.homeworkCount}</div>
            <div className="text-sm text-gray-500">团队作业/考试</div>
          </div>
        </div>
      </div>
    </div>
  )
} 