'use client'

import { Team } from '@/app/types/team'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faCrown, faUserCheck, faUsers, faCalendarAlt } from '@fortawesome/free-solid-svg-icons'
import Link from 'next/link'

interface TeamCardProps {
  team: Team
}

export default function TeamCard({ team }: TeamCardProps) {
  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-md transition">
      <div 
        className={`h-24 relative bg-gradient-to-r from-${team.gradientColors.from}-500 to-${team.gradientColors.to}-500`}
      >
        <div className="absolute bottom-0 right-0 bg-white/30 px-2 py-1 m-2 rounded text-xs text-white">
          {team.isCreator ? (
            <>
              <FontAwesomeIcon icon={faCrown} className="mr-1" />
              我创建的
            </>
          ) : (
            <>
              <FontAwesomeIcon icon={faUserCheck} className="mr-1" />
              已加入
            </>
          )}
        </div>
      </div>
      <div className="p-5">
        <div className="flex items-center mb-4">
          <div className={`w-16 h-16 bg-${team.gradientColors.from}-100 rounded-full flex items-center justify-center border-4 border-white -mt-12 shadow`}>
            <FontAwesomeIcon icon={team.icon} className={`text-${team.gradientColors.from}-600 text-2xl`} />
          </div>
          <div className="ml-3">
            <h3 className="text-lg font-semibold text-gray-800">{team.name}</h3>
            <p className="text-sm text-gray-500">ID: {team.id}</p>
          </div>
        </div>
        <p className="text-gray-600 text-sm mb-4">
          {team.description}
        </p>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center text-sm text-gray-500">
            <FontAwesomeIcon icon={faUsers} className="mr-1" />
            <span>{team.memberCount}名成员</span>
          </div>
          <div className="flex items-center text-sm text-gray-500">
            <FontAwesomeIcon icon={faCalendarAlt} className="mr-1" />
            <span>{team.createdAt}创建</span>
          </div>
        </div>
        <Link 
          href={`/teams/${team.id}`}
          className={`block w-full py-2 bg-${team.gradientColors.from}-600 text-white text-center rounded-lg hover:bg-${team.gradientColors.from}-700 transition`}
        >
          进入团队
        </Link>
      </div>
    </div>
  )
} 