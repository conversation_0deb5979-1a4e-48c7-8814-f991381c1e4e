'use client'

import { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faTimes } from '@fortawesome/free-solid-svg-icons'

interface EditNoticeModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (notice: string) => Promise<void>
  initialNotice: string
}

export default function EditNoticeModal({
  isOpen,
  onClose,
  onSave,
  initialNotice
}: EditNoticeModalProps) {
  const [notice, setNotice] = useState(initialNotice)
  const [isSaving, setIsSaving] = useState(false)

  // 监听 initialNotice 的变化，更新 notice 状态
  useEffect(() => {
    setNotice(initialNotice)
  }, [initialNotice])

  if (!isOpen) return null

  const handleSave = async () => {
    try {
      setIsSaving(true)
      await onSave(notice)
      onClose()
    } catch (error) {
      console.error('保存公告失败:', error)
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-lg w-full max-w-2xl mx-4">
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200">
          <h3 className="text-xl font-semibold text-gray-800">编辑团队公告</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition"
          >
            <FontAwesomeIcon icon={faTimes} />
          </button>
        </div>
        <div className="p-6">
          <textarea
            value={notice}
            onChange={(e) => setNotice(e.target.value)}
            placeholder="在这里输入团队公告..."
            className="w-full h-48 p-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent resize-none"
          />
          <div className="text-sm text-gray-500 mt-2">
            支持纯文本格式，建议包含团队最新动态、重要通知等信息。
          </div>
        </div>
        <div className="flex justify-end px-6 py-4 bg-gray-50 rounded-b-xl">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 hover:text-gray-900 font-medium mr-2"
          >
            取消
          </button>
          <button
            onClick={handleSave}
            disabled={isSaving}
            className={`px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium ${
              isSaving
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:bg-indigo-700'
            } transition`}
          >
            {isSaving ? '保存中...' : '保存'}
          </button>
        </div>
      </div>
    </div>
  )
} 