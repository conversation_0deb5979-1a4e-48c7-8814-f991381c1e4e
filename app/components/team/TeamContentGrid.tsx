'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { TeamHomework, TeamExam, TeamProblemSet } from '@/app/types/team'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faClock, 
  faCheck, 
  faCalendarAlt, 
  faChartBar,
  faCode
} from '@fortawesome/free-solid-svg-icons'

interface TeamContentGridProps {
  teamId: string
  homeworkCount?: number
  problemListCount?: number
  examCount?: number
}

export default function TeamContentGrid({ 
  teamId, 
  homeworkCount = 0, 
  problemListCount = 0, 
  examCount = 0 
}: TeamContentGridProps) {
  const [homeworks, setHomeworks] = useState<TeamHomework[]>([])
  const [exams, setExams] = useState<TeamExam[]>([])
  const [problemSets, setProblemSets] = useState<TeamProblemSet[]>([])

  useEffect(() => {
    // TODO: 从API获取数据
    const mockHomeworks: TeamHomework[] = [
      {
        id: '1',
        title: '二叉树遍历算法实现',
        description: '实现前序、中序、后序三种遍历算法，并分析时间复杂度。',
        dueDate: '2023-11-20',
        submissionCount: 12,
        totalMembers: 18,
        status: 'ongoing'
      },
      {
        id: '2',
        title: '排序算法练习',
        description: '实现快速排序和归并排序，并比较两种排序算法的优缺点。',
        dueDate: '2023-11-15',
        submissionCount: 16,
        totalMembers: 18,
        status: 'graded'
      }
    ]

    const mockExams: TeamExam[] = [
      {
        id: '1',
        title: '算法期中测试',
        description: '覆盖基础数据结构和算法，包含选择题和编程题。',
        startTime: '2023-11-25T19:00:00',
        duration: 120,
        participantCount: 0,
        status: 'upcoming'
      },
      {
        id: '2',
        title: '基础编程能力测试',
        description: '测试基础语法和编程能力，重点考察数组和字符串操作。',
        startTime: '2023-11-10T19:00:00',
        duration: 90,
        participantCount: 16,
        status: 'ended'
      }
    ]

    const mockProblemSets: TeamProblemSet[] = [
      {
        id: '1',
        title: '算法入门40题',
        description: '从零开始学习算法，循序渐进提升编程能力。',
        problemCount: 40,
        difficulty: 'easy',
        recommended: true
      },
      {
        id: '2',
        title: '数据结构专项练习',
        description: '包含链表、栈、队列、树等数据结构的经典题目。',
        problemCount: 25,
        difficulty: 'medium',
        recommended: true
      }
    ]

    setHomeworks(mockHomeworks)
    setExams(mockExams)
    setProblemSets(mockProblemSets)
  }, [teamId])

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      {/* 近期作业 */}
      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-800">近期作业</h3>
          <Link href={`/teams/${teamId}/homework`} className="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
            查看全部
          </Link>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {homeworks.map(homework => (
              <div key={homework.id} className="border border-gray-200 rounded-lg p-4 hover:border-indigo-300 transition cursor-pointer">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium text-gray-800">{homework.title}</h4>
                  {homework.status === 'ongoing' && (
                    <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                      <FontAwesomeIcon icon={faClock} className="mr-1" />
                      即将截止
                    </span>
                  )}
                  {homework.status === 'graded' && (
                    <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                      <FontAwesomeIcon icon={faCheck} className="mr-1" />
                      已批改
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-600 mb-3">{homework.description}</p>
                <div className="flex justify-between items-center text-xs text-gray-500">
                  <span>已提交: {homework.submissionCount}/{homework.totalMembers}人</span>
                  <Link href={`/teams/${teamId}/homework/${homework.id}`} className="text-indigo-600 hover:text-indigo-800 font-medium">
                    查看详情
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 近期考试 */}
      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-800">近期考试</h3>
          <Link href={`/teams/${teamId}/exams`} className="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
            查看全部
          </Link>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {exams.map(exam => (
              <div key={exam.id} className="border border-gray-200 rounded-lg p-4 hover:border-indigo-300 transition cursor-pointer">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium text-gray-800">{exam.title}</h4>
                  {exam.status === 'upcoming' && (
                    <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                      <FontAwesomeIcon icon={faCalendarAlt} className="mr-1" />
                      即将开始
                    </span>
                  )}
                  {exam.status === 'ended' && (
                    <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                      <FontAwesomeIcon icon={faChartBar} className="mr-1" />
                      已结束
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-600 mb-3">{exam.description}</p>
                <div className="flex justify-between items-center text-xs text-gray-500">
                  {exam.status === 'upcoming' ? (
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faClock} className="mr-1" />
                      <span>{new Date(exam.startTime).toLocaleString()}</span>
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faCalendarAlt} className="mr-1" />
                      <span>参与人数: {exam.participantCount}人</span>
                    </div>
                  )}
                  <Link href={`/teams/${teamId}/exams/${exam.id}`} className="text-indigo-600 hover:text-indigo-800 font-medium">
                    {exam.status === 'ended' ? '查看成绩' : '查看详情'}
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 热门题单 */}
      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-800">热门题单</h3>
          <Link href={`/teams/${teamId}/problem-sets`} className="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
            查看全部
          </Link>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {problemSets.map(problemSet => (
              <div key={problemSet.id} className="border border-gray-200 rounded-lg p-4 hover:border-indigo-300 transition cursor-pointer">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium text-gray-800">{problemSet.title}</h4>
                  {problemSet.recommended && (
                    <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">推荐</span>
                  )}
                </div>
                <p className="text-sm text-gray-600 mb-3">{problemSet.description}</p>
                <div className="flex justify-between items-center text-xs text-gray-500">
                  <div className="flex items-center">
                    <FontAwesomeIcon icon={faCode} className="mr-1" />
                    <span>{problemSet.problemCount}道题目</span>
                  </div>
                  <Link href={`/teams/${teamId}/problem-sets/${problemSet.id}`} className="text-indigo-600 hover:text-indigo-800 font-medium">
                    开始刷题
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
} 