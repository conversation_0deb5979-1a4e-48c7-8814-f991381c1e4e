'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { TeamMember } from '@/app/types/team'
import Image from 'next/image'

interface TeamMemberListProps {
  teamId: string
}

export default function TeamMemberList({ teamId }: TeamMemberListProps) {
  const [members, setMembers] = useState<TeamMember[]>([])

  useEffect(() => {
    // TODO: 从API获取团队成员
    const mockMembers: TeamMember[] = [
      {
        id: '1',
        name: '小明',
        avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
        role: 'creator',
        activityScore: 98
      },
      {
        id: '2',
        name: '小红',
        avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
        role: 'admin',
        activityScore: 92
      },
      {
        id: '3',
        name: '小李',
        avatar: 'https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
        role: 'member',
        activityScore: 85
      },
      {
        id: '4',
        name: '小张',
        avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
        role: 'member',
        activityScore: 79
      }
    ]
    setMembers(mockMembers)
  }, [teamId])

  const getRoleBadgeStyle = (role: TeamMember['role']) => {
    switch (role) {
      case 'creator':
        return 'bg-indigo-100 text-indigo-800'
      case 'admin':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getRoleLabel = (role: TeamMember['role']) => {
    switch (role) {
      case 'creator':
        return '创建者'
      case 'admin':
        return '管理员'
      default:
        return '成员'
    }
  }

  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden mb-8">
      <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-800">团队成员</h3>
        <Link href={`/teams/${teamId}/members`} className="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
          查看全部
        </Link>
      </div>
      <div className="p-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {members.map(member => (
            <div key={member.id} className="border border-gray-200 rounded-lg p-4 flex items-center">
              <Image
                src={member.avatar}
                alt={member.name}
                width={48}
                height={48}
                className="rounded-full object-cover mr-3"
              />
              <div>
                <div className="flex items-center">
                  <h4 className="font-medium text-gray-800">{member.name}</h4>
                  <span className={`ml-2 text-xs px-1.5 py-0.5 rounded ${getRoleBadgeStyle(member.role)}`}>
                    {getRoleLabel(member.role)}
                  </span>
                </div>
                <p className="text-xs text-gray-500 mt-1">活跃度: {member.activityScore}%</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
} 