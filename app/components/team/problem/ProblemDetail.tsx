'use client'

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faSmile,
  faCheckCircle,
  faFire,
  faTag,
  faClock,
  faMemory
} from '@fortawesome/free-solid-svg-icons'
import ReactMarkdown from 'react-markdown'

interface Problem {
  id: string
  title: string
  content: string
  inputFormat: string
  outputFormat: string
  examples: {
    input: string
    output: string
    explanation?: string
  }[]
  constraints: string[]
  difficulty: 'easy' | 'medium' | 'hard'
  tags: string[]
  solvedCount: number
  totalCount: number
  acceptance: number
  timeLimit: number // 单位: ms
  memoryLimit: number // 单位: MB
  createdAt: string
}

interface ProblemDetailProps {
  problem: Problem
}

export default function ProblemDetail({ problem }: ProblemDetailProps) {
  // 根据难度获取样式
  const getDifficultyStyle = (difficulty: Problem['difficulty']) => {
    switch (difficulty) {
      case 'easy':
        return {
          icon: faSmile,
          text: '入门',
          colorClass: 'text-green-600',
          bgColorClass: 'bg-green-100'
        }
      case 'medium':
        return {
          icon: faCheckCircle,
          text: '进阶',
          colorClass: 'text-yellow-600',
          bgColorClass: 'bg-yellow-100'
        }
      case 'hard':
        return {
          icon: faFire,
          text: '挑战',
          colorClass: 'text-red-600',
          bgColorClass: 'bg-red-100'
        }
      default:
        return {
          icon: faSmile,
          text: '未知',
          colorClass: 'text-gray-600',
          bgColorClass: 'bg-gray-100'
        }
    }
  }

  const difficultyStyle = getDifficultyStyle(problem.difficulty)

  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden">
      <div className="p-6">
        <div className="border-b border-gray-200 pb-4 mb-4">
          <div className="flex flex-wrap items-center gap-2 mb-2">
            <h1 className="text-2xl font-bold text-gray-800">{problem.title}</h1>
            <span className={`${difficultyStyle.bgColorClass} ${difficultyStyle.colorClass} text-xs px-2 py-1 rounded-full inline-flex items-center`}>
              <FontAwesomeIcon icon={difficultyStyle.icon} className="mr-1" />
              {difficultyStyle.text}
            </span>
          </div>
          
          <div className="flex flex-wrap gap-4 text-sm text-gray-500">
            <div className="flex items-center">
              <FontAwesomeIcon icon={faClock} className="mr-1" />
              <span>时间限制: {problem.timeLimit} ms</span>
            </div>
            
            <div className="flex items-center">
              <FontAwesomeIcon icon={faMemory} className="mr-1" />
              <span>内存限制: {problem.memoryLimit} MB</span>
            </div>
            
            <div className="flex items-center">
              <span>通过率: {problem.acceptance.toFixed(1)}% ({problem.solvedCount}/{problem.totalCount})</span>
            </div>
          </div>
          
          <div className="flex flex-wrap gap-1 mt-2">
            {problem.tags.map((tag, index) => (
              <span 
                key={index}
                className="bg-indigo-50 text-indigo-600 text-xs px-2 py-1 rounded-full inline-flex items-center"
              >
                <FontAwesomeIcon icon={faTag} className="mr-1" />
                {tag}
              </span>
            ))}
          </div>
        </div>
        
        {/* 题目内容 */}
        <div className="prose prose-indigo max-w-none mb-6">
          <h2 className="text-xl font-bold text-gray-800 mb-2">题目描述</h2>
          <ReactMarkdown>{problem.content}</ReactMarkdown>
        </div>
        
        {/* 输入格式 */}
        <div className="prose prose-indigo max-w-none mb-6">
          <h2 className="text-xl font-bold text-gray-800 mb-2">输入格式</h2>
          <ReactMarkdown>{problem.inputFormat}</ReactMarkdown>
        </div>
        
        {/* 输出格式 */}
        <div className="prose prose-indigo max-w-none mb-6">
          <h2 className="text-xl font-bold text-gray-800 mb-2">输出格式</h2>
          <ReactMarkdown>{problem.outputFormat}</ReactMarkdown>
        </div>
        
        {/* 示例 */}
        <div className="mb-6">
          <h2 className="text-xl font-bold text-gray-800 mb-2">示例</h2>
          <div className="space-y-4">
            {problem.examples.map((example, index) => (
              <div key={index} className="rounded-lg border border-gray-200 overflow-hidden">
                <div className="bg-gray-50 px-4 py-2 border-b border-gray-200">
                  <div className="font-medium">示例 {index + 1}</div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 divide-y md:divide-y-0 md:divide-x divide-gray-200">
                  <div className="p-4">
                    <div className="font-medium text-gray-700 mb-2">输入:</div>
                    <pre className="bg-gray-50 p-3 rounded text-sm font-mono overflow-x-auto">{example.input}</pre>
                  </div>
                  <div className="p-4">
                    <div className="font-medium text-gray-700 mb-2">输出:</div>
                    <pre className="bg-gray-50 p-3 rounded text-sm font-mono overflow-x-auto">{example.output}</pre>
                  </div>
                </div>
                {example.explanation && (
                  <div className="px-4 py-3 bg-gray-50 border-t border-gray-200">
                    <div className="text-sm text-gray-700">
                      <span className="font-medium">解释: </span>
                      {example.explanation}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
        
        {/* 约束条件 */}
        <div>
          <h2 className="text-xl font-bold text-gray-800 mb-2">约束条件</h2>
          <ul className="list-disc list-inside space-y-1 text-gray-700">
            {problem.constraints.map((constraint, index) => (
              <li key={index} className="text-sm">
                <code className="bg-gray-100 px-1 py-0.5 rounded">{constraint}</code>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  )
} 