'use client'

import Link from 'next/link'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faSmile,
  faCheckCircle,
  faFire,
  faTag,
  faExternalLinkAlt,
  faCodeBranch,
  faChartLine,
  faCalendarAlt
} from '@fortawesome/free-solid-svg-icons'

export interface Problem {
  id: string
  title: string
  difficulty: 'easy' | 'medium' | 'hard'
  tags: string[]
  solvedCount: number
  totalCount: number
  acceptance: number
  createdAt: string
  type: 'basic' | 'programming'
}

interface ProblemListProps {
  problems: Problem[]
  isLoading: boolean
  teamId: string
}

export default function ProblemList({ problems, isLoading, teamId }: ProblemListProps) {
  if (isLoading) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-8 text-center">
        <div className="inline-block w-12 h-12 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
        <p className="mt-4 text-gray-500">正在加载题目...</p>
      </div>
    )
  }

  if (problems.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-8 text-center">
        <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <FontAwesomeIcon icon={faCodeBranch} className="text-gray-400 text-2xl" />
        </div>
        <h3 className="text-lg font-medium text-gray-800 mb-2">没有找到题目</h3>
        <p className="text-gray-500">
          当前筛选条件下没有找到任何题目，请尝试更改筛选条件或创建新题目。
        </p>
        <button className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition">
          创建题目
        </button>
      </div>
    )
  }

  // 根据难度获取样式
  const getDifficultyStyle = (difficulty: Problem['difficulty']) => {
    switch (difficulty) {
      case 'easy':
        return {
          icon: faSmile,
          text: '入门',
          colorClass: 'text-green-600',
          bgColorClass: 'bg-green-100'
        }
      case 'medium':
        return {
          icon: faCheckCircle,
          text: '进阶',
          colorClass: 'text-yellow-600',
          bgColorClass: 'bg-yellow-100'
        }
      case 'hard':
        return {
          icon: faFire,
          text: '挑战',
          colorClass: 'text-red-600',
          bgColorClass: 'bg-red-100'
        }
      default:
        return {
          icon: faSmile,
          text: '未知',
          colorClass: 'text-gray-600',
          bgColorClass: 'bg-gray-100'
        }
    }
  }

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden">
      <div className="p-6">
        <h2 className="text-xl font-bold text-gray-800 mb-4">题目列表</h2>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-gray-50 border-b border-gray-200">
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  题目
                </th>
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  难度
                </th>
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  标签
                </th>
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  通过率
                </th>
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  创建日期
                </th>
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {problems.map((problem, index) => {
                const difficultyStyle = getDifficultyStyle(problem.difficulty)
                
                return (
                  <tr key={problem.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="text-sm font-medium text-gray-900">
                          {problem.title}
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-center">
                      <span className={`${difficultyStyle.bgColorClass} ${difficultyStyle.colorClass} text-xs px-2 py-1 rounded-full inline-flex items-center justify-center`}>
                        <FontAwesomeIcon icon={difficultyStyle.icon} className="mr-1" />
                        {difficultyStyle.text}
                      </span>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-center">
                      <div className="flex flex-wrap justify-center gap-1">
                        {problem.tags.map((tag, tagIndex) => (
                          <span 
                            key={tagIndex} 
                            className="bg-indigo-50 text-indigo-600 text-xs px-2 py-1 rounded-full"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-center">
                      <div className="flex flex-col items-center">
                        <div className="text-sm text-gray-900 font-medium">
                          {problem.acceptance.toFixed(1)}%
                        </div>
                        <div className="text-xs text-gray-500">
                          {problem.solvedCount}/{problem.totalCount}
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-center">
                      <div className="flex items-center justify-center text-sm text-gray-500">
                        <FontAwesomeIcon icon={faCalendarAlt} className="mr-1" />
                        {formatDate(problem.createdAt)}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-center">
                      <div className="flex justify-center space-x-2">
                        <Link 
                          href={`/teams/${teamId}/problems/${problem.id}`}
                          className="inline-flex items-center px-3 py-1 border border-indigo-600 text-indigo-600 rounded hover:bg-indigo-50 transition"
                        >
                          <FontAwesomeIcon icon={faExternalLinkAlt} className="mr-1" />
                          做题
                        </Link>
                        <Link 
                          href={`/teams/${teamId}/problems/${problem.id}/statistics`}
                          className="inline-flex items-center px-3 py-1 border border-gray-300 text-gray-500 rounded hover:bg-gray-50 transition"
                        >
                          <FontAwesomeIcon icon={faChartLine} className="mr-1" />
                          统计
                        </Link>
                      </div>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
} 