'use client'

import { TeamProblemSet } from '@/app/types/team'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faCode,
  faSmile,
  faCheckCircle,
  faFire,
  faStar,
  faShare,
  faUser
} from '@fortawesome/free-solid-svg-icons'
import ReactMarkdown from 'react-markdown'

interface ProblemSetDetailProps {
  problemSet: TeamProblemSet
}

export default function ProblemSetDetail({ problemSet }: ProblemSetDetailProps) {
  // 根据难度获取信息
  const getDifficultyInfo = (difficulty: TeamProblemSet['difficulty']) => {
    switch (difficulty) {
      case 'easy':
        return {
          icon: faSmile,
          text: '入门难度',
          colorClass: 'text-green-600',
          bgColorClass: 'bg-green-100'
        }
      case 'medium':
        return {
          icon: faCheckCircle,
          text: '进阶难度',
          colorClass: 'text-yellow-600',
          bgColorClass: 'bg-yellow-100'
        }
      case 'hard':
        return {
          icon: faFire,
          text: '挑战难度',
          colorClass: 'text-red-600',
          bgColorClass: 'bg-red-100'
        }
      default:
        return {
          icon: faSmile,
          text: '未知难度',
          colorClass: 'text-gray-600',
          bgColorClass: 'bg-gray-100'
        }
    }
  }

  const difficultyInfo = getDifficultyInfo(problemSet.difficulty)

  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden">
      <div className="p-6">
        <div className="flex flex-col md:flex-row md:justify-between md:items-start mb-6">
          <div>
            <div className="flex items-center mb-2">
              <h1 className="text-2xl font-bold text-gray-800 mr-3">{problemSet.title}</h1>
              {problemSet.recommended && (
                <span className="bg-amber-100 text-amber-700 text-xs px-2 py-1 rounded-full inline-flex items-center">
                  <FontAwesomeIcon icon={faStar} className="mr-1" />
                  推荐题单
                </span>
              )}
            </div>
            
            <div className="flex items-center space-x-4 mt-2">
              <div className={`${difficultyInfo.bgColorClass} ${difficultyInfo.colorClass} text-sm px-3 py-1 rounded-lg inline-flex items-center`}>
                <FontAwesomeIcon icon={difficultyInfo.icon} className="mr-1" />
                {difficultyInfo.text}
              </div>
              
              <div className="flex items-center text-sm text-gray-500">
                <FontAwesomeIcon icon={faCode} className="mr-1" />
                <span>{problemSet.problemCount}题</span>
              </div>
              
              <div className="flex items-center text-sm text-gray-500">
                <FontAwesomeIcon icon={faUser} className="mr-1" />
                <span>120人练习过</span>
              </div>
            </div>
          </div>
          
          <div className="mt-4 md:mt-0 flex space-x-2">
            <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition">
              开始练习
            </button>
            
            <button className="px-4 py-2 border border-gray-300 text-gray-600 rounded-lg hover:bg-gray-50 transition flex items-center">
              <FontAwesomeIcon icon={faShare} className="mr-1" />
              分享
            </button>
          </div>
        </div>
        
        {/* 题单说明 */}
        <div className="prose prose-indigo max-w-none">
          <ReactMarkdown>{problemSet.description}</ReactMarkdown>
        </div>
      </div>
    </div>
  )
} 