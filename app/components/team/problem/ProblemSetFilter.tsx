'use client'

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faListAlt,
  faSmile,
  faCheckCircle, 
  faFire
} from '@fortawesome/free-solid-svg-icons'

interface ProblemSetFilterProps {
  currentFilter: 'all' | 'easy' | 'medium' | 'hard'
  onFilterChange: (filter: 'all' | 'easy' | 'medium' | 'hard') => void
  problemSetCount: {
    all: number
    easy: number
    medium: number
    hard: number
  }
}

export default function ProblemSetFilter({ 
  currentFilter, 
  onFilterChange,
  problemSetCount 
}: ProblemSetFilterProps) {
  const filters = [
    { id: 'all', label: '全部题单', icon: faListAlt, count: problemSetCount.all },
    { id: 'easy', label: '入门', icon: faSmile, count: problemSetCount.easy },
    { id: 'medium', label: '进阶', icon: faCheckCircle, count: problemSetCount.medium },
    { id: 'hard', label: '挑战', icon: faFire, count: problemSetCount.hard }
  ]

  // 根据难度获取颜色
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'text-green-600 bg-green-100'
      case 'medium':
        return 'text-yellow-600 bg-yellow-100'
      case 'hard':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-indigo-600 bg-indigo-100'
    }
  }

  return (
    <div className="bg-white rounded-xl shadow-sm mb-6 overflow-hidden">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4">
        {filters.map((filter) => {
          const isActive = currentFilter === filter.id
          const colorClass = filter.id !== 'all' ? getDifficultyColor(filter.id) : 'text-indigo-600 bg-indigo-100'
          
          return (
            <button
              key={filter.id}
              onClick={() => onFilterChange(filter.id as any)}
              className={`py-4 px-6 flex items-center justify-center sm:justify-start transition ${
                isActive
                  ? 'bg-gray-50 border-b-2 border-indigo-600'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800'
              }`}
            >
              <div className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                  isActive ? colorClass : 'bg-gray-100 text-gray-500'
                }`}>
                  <FontAwesomeIcon icon={filter.icon} />
                </div>
                <div className="text-left">
                  <div className="font-medium">{filter.label}</div>
                  <div className="text-xs mt-0.5">
                    {filter.count}个题单
                  </div>
                </div>
              </div>
            </button>
          )
        })}
      </div>
    </div>
  )
} 