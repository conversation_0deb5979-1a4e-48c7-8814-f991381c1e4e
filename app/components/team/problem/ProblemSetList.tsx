'use client'

import Link from 'next/link'
import { TeamProblemSet } from '@/app/types/team'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faFileAlt,
  faCode,
  faTag,
  faSmile,
  faCheckCircle,
  faFire,
  faStar,
  faChevronRight
} from '@fortawesome/free-solid-svg-icons'

interface ProblemSetListProps {
  problemSets: TeamProblemSet[]
  isLoading: boolean
  teamId: string
}

export default function ProblemSetList({ problemSets, isLoading, teamId }: ProblemSetListProps) {
  if (isLoading) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-8 text-center">
        <div className="inline-block w-12 h-12 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
        <p className="mt-4 text-gray-500">正在加载题单...</p>
      </div>
    )
  }

  if (problemSets.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-8 text-center">
        <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <FontAwesomeIcon icon={faFileAlt} className="text-gray-400 text-2xl" />
        </div>
        <h3 className="text-lg font-medium text-gray-800 mb-2">没有找到题单</h3>
        <p className="text-gray-500">
          当前筛选条件下没有找到任何题单，请尝试更改筛选条件或创建新题单。
        </p>
        <button className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition">
          创建题单
        </button>
      </div>
    )
  }

  // 根据难度获取样式
  const getDifficultyStyle = (difficulty: TeamProblemSet['difficulty']) => {
    switch (difficulty) {
      case 'easy':
        return {
          icon: faSmile,
          text: '入门',
          colorClass: 'text-green-600',
          bgColorClass: 'bg-green-100'
        }
      case 'medium':
        return {
          icon: faCheckCircle,
          text: '进阶',
          colorClass: 'text-yellow-600',
          bgColorClass: 'bg-yellow-100'
        }
      case 'hard':
        return {
          icon: faFire,
          text: '挑战',
          colorClass: 'text-red-600',
          bgColorClass: 'bg-red-100'
        }
      default:
        return {
          icon: faSmile,
          text: '未知',
          colorClass: 'text-gray-600',
          bgColorClass: 'bg-gray-100'
        }
    }
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {problemSets.map(problemSet => {
        const difficultyStyle = getDifficultyStyle(problemSet.difficulty)
        
        return (
          <Link 
            key={problemSet.id}
            href={`/teams/${teamId}/problem-sets/${problemSet.id}`}
            className="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-200 hover:border-indigo-300 hover:shadow-md transition group"
          >
            <div className="p-6">
              <div className="flex justify-between items-start mb-2">
                <h3 className="text-lg font-semibold text-gray-800 group-hover:text-indigo-600 transition">
                  {problemSet.title}
                </h3>
                <span className={`${difficultyStyle.bgColorClass} ${difficultyStyle.colorClass} text-xs px-2 py-1 rounded-full inline-flex items-center`}>
                  <FontAwesomeIcon icon={difficultyStyle.icon} className="mr-1" />
                  {difficultyStyle.text}
                </span>
              </div>
              
              <p className="text-gray-600 mb-6 line-clamp-2">{problemSet.description}</p>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center text-sm text-gray-500">
                    <FontAwesomeIcon icon={faCode} className="mr-1 text-gray-400" />
                    <span>{problemSet.problemCount}题</span>
                  </div>
                  
                  {problemSet.recommended && (
                    <div className="flex items-center text-sm text-amber-600">
                      <FontAwesomeIcon icon={faStar} className="mr-1" />
                      <span>推荐</span>
                    </div>
                  )}
                </div>
                
                <div className="text-indigo-600 group-hover:translate-x-1 transition-transform">
                  <FontAwesomeIcon icon={faChevronRight} />
                </div>
              </div>
            </div>
          </Link>
        )
      })}
    </div>
  )
} 