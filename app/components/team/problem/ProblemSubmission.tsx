'use client'

import { useState } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faCode,
  faPaperPlane,
  faHistory,
  faExclamationTriangle
} from '@fortawesome/free-solid-svg-icons'

interface ProblemSubmissionProps {
  teamId: string
  problemId: string
}

export default function ProblemSubmission({ teamId, problemId }: ProblemSubmissionProps) {
  const [language, setLanguage] = useState<string>('cpp')
  const [code, setCode] = useState<string>('')
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)

  // 可选的编程语言
  const languages = [
    { id: 'cpp', name: 'C++' },
    { id: 'java', name: 'Java' },
    { id: 'python', name: 'Python' },
    { id: 'javascript', name: 'JavaScript' },
    { id: 'c', name: 'C' },
    { id: 'go', name: 'Go' }
  ]

  // 语言代码模板
  const getTemplateCode = (lang: string) => {
    switch (lang) {
      case 'cpp':
        return `#include <iostream>
#include <vector>
using namespace std;

vector<int> twoSum(vector<int>& nums, int target) {
    // 请在此处编写代码
}

int main() {
    int n, target;
    cin >> n;
    vector<int> nums(n);
    for (int i = 0; i < n; i++) {
        cin >> nums[i];
    }
    cin >> target;
    
    vector<int> result = twoSum(nums, target);
    cout << result[0] << " " << result[1] << endl;
    return 0;
}`;
      case 'java':
        return `import java.util.*;

public class Main {
    public static int[] twoSum(int[] nums, int target) {
        // 请在此处编写代码
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        int n = scanner.nextInt();
        int[] nums = new int[n];
        for (int i = 0; i < n; i++) {
            nums[i] = scanner.nextInt();
        }
        int target = scanner.nextInt();
        
        int[] result = twoSum(nums, target);
        System.out.println(result[0] + " " + result[1]);
    }
}`;
      case 'python':
        return `def two_sum(nums, target):
    # 请在此处编写代码
    pass

# 读取输入
n = int(input())
nums = list(map(int, input().split()))
target = int(input())

# 调用函数并输出结果
result = two_sum(nums, target)
print(f"{result[0]} {result[1]}")`;
      default:
        return '// 请在此处编写代码';
    }
  }

  // 处理语言变更
  const handleLanguageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newLang = e.target.value
    setLanguage(newLang)
    
    // 如果代码为空或者是默认模板，则更新为新语言的模板
    if (code === '' || code === getTemplateCode(language)) {
      setCode(getTemplateCode(newLang))
    }
  }

  // 处理代码提交
  const handleSubmit = async () => {
    if (!code.trim()) {
      setError('代码不能为空')
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      // TODO: 实现API调用提交代码
      console.log('提交代码:', {
        teamId,
        problemId,
        language,
        code
      })

      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 1500))

      // 重定向到提交结果页面
      window.location.href = `/teams/${teamId}/problems/${problemId}/submissions/123456`
    } catch (err) {
      setError('提交失败，请稍后重试')
      console.error('提交失败:', err)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="space-y-4">
      {/* 代码编辑器 */}
      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="font-medium text-gray-800 flex items-center">
            <FontAwesomeIcon icon={faCode} className="mr-2 text-indigo-500" />
            提交代码
          </div>
          <select
            className="bg-gray-50 border border-gray-300 text-gray-700 text-sm rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            value={language}
            onChange={handleLanguageChange}
          >
            {languages.map(lang => (
              <option key={lang.id} value={lang.id}>{lang.name}</option>
            ))}
          </select>
        </div>
        <div className="p-0">
          <textarea
            className="w-full h-80 p-4 font-mono text-sm bg-gray-50 focus:outline-none resize-none"
            value={code || getTemplateCode(language)}
            onChange={(e) => setCode(e.target.value)}
            placeholder="在此处编写代码..."
            spellCheck="false"
          />
        </div>
        {error && (
          <div className="px-4 py-2 bg-red-50 text-red-700 text-sm flex items-center">
            <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
            {error}
          </div>
        )}
        <div className="p-4 flex justify-end space-x-3 border-t border-gray-200">
          <button
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 flex items-center"
            onClick={() => window.location.href = `/teams/${teamId}/problems/${problemId}/submissions`}
          >
            <FontAwesomeIcon icon={faHistory} className="mr-2" />
            历史提交
          </button>
          <button
            className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 flex items-center disabled:opacity-70"
            onClick={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                提交中...
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faPaperPlane} className="mr-2" />
                提交代码
              </>
            )}
          </button>
        </div>
      </div>

      {/* 提交要求说明 */}
      <div className="bg-white rounded-xl shadow-sm p-4">
        <h3 className="text-md font-semibold text-gray-800 mb-2">提交须知</h3>
        <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
          <li>确保代码符合题目要求的输入和输出格式</li>
          <li>提交前请检查代码是否有语法错误</li>
          <li>系统会对你的代码进行多组测试</li>
          <li>注意代码的时间和空间复杂度</li>
        </ul>
      </div>
    </div>
  )
} 