'use client'

import { useState, useEffect } from 'react'
import { TeamDetailStats } from '@/app/types/team'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faChartPie,
  faChartBar,
  faChartLine
} from '@fortawesome/free-solid-svg-icons'

interface TeamStatisticsProps {
  teamId: string
}

export default function TeamStatistics({ teamId }: TeamStatisticsProps) {
  const [stats, setStats] = useState<TeamDetailStats | null>(null)

  useEffect(() => {
    // TODO: 从API获取统计数据
    const mockStats: TeamDetailStats = {
      homeworkCompletionRate: 85,
      memberActivityDistribution: {
        high: 8,
        medium: 6,
        low: 4
      },
      examScoreTrend: [
        { date: '2023-09', averageScore: 75 },
        { date: '2023-10', averageScore: 82 },
        { date: '2023-11', averageScore: 88 }
      ]
    }
    setStats(mockStats)
  }, [teamId])

  if (!stats) {
    return <div>加载中...</div>
  }

  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden">
      <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100">
        <h3 className="text-lg font-semibold text-gray-800">团队数据统计</h3>
      </div>
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* 作业完成率 */}
          <div className="border border-gray-200 rounded-lg p-4 h-64 flex flex-col items-center justify-center">
            <div className="text-center mb-4">
              <div className="text-gray-400 mb-2">
                <FontAwesomeIcon icon={faChartPie} className="text-4xl" />
              </div>
              <p className="text-gray-600">作业完成率统计</p>
            </div>
            <div className="text-4xl font-bold text-indigo-600">
              {stats.homeworkCompletionRate}%
            </div>
            <p className="text-sm text-gray-500 mt-2">平均作业完成率</p>
          </div>
          
          {/* 成员活跃度 */}
          <div className="border border-gray-200 rounded-lg p-4 h-64 flex flex-col items-center justify-center">
            <div className="text-center mb-4">
              <div className="text-gray-400 mb-2">
                <FontAwesomeIcon icon={faChartBar} className="text-4xl" />
              </div>
              <p className="text-gray-600">成员活跃度分布</p>
            </div>
            <div className="w-full flex items-end justify-center space-x-4 mt-4">
              <div className="flex flex-col items-center">
                <div className="h-32 w-8 bg-green-100 rounded-t-lg relative">
                  <div 
                    className="absolute bottom-0 w-full bg-green-500 rounded-t-lg transition-all duration-500"
                    style={{ height: `${(stats.memberActivityDistribution.high / 18) * 100}%` }}
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">高活跃</p>
                <p className="text-xs font-medium">{stats.memberActivityDistribution.high}人</p>
              </div>
              <div className="flex flex-col items-center">
                <div className="h-32 w-8 bg-yellow-100 rounded-t-lg relative">
                  <div 
                    className="absolute bottom-0 w-full bg-yellow-500 rounded-t-lg transition-all duration-500"
                    style={{ height: `${(stats.memberActivityDistribution.medium / 18) * 100}%` }}
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">中活跃</p>
                <p className="text-xs font-medium">{stats.memberActivityDistribution.medium}人</p>
              </div>
              <div className="flex flex-col items-center">
                <div className="h-32 w-8 bg-red-100 rounded-t-lg relative">
                  <div 
                    className="absolute bottom-0 w-full bg-red-500 rounded-t-lg transition-all duration-500"
                    style={{ height: `${(stats.memberActivityDistribution.low / 18) * 100}%` }}
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">低活跃</p>
                <p className="text-xs font-medium">{stats.memberActivityDistribution.low}人</p>
              </div>
            </div>
          </div>
          
          {/* 考试成绩趋势 */}
          <div className="border border-gray-200 rounded-lg p-4 h-64 flex flex-col items-center justify-center">
            <div className="text-center mb-4">
              <div className="text-gray-400 mb-2">
                <FontAwesomeIcon icon={faChartLine} className="text-4xl" />
              </div>
              <p className="text-gray-600">考试成绩趋势</p>
            </div>
            <div className="w-full h-32 flex items-end justify-between px-4">
              {stats.examScoreTrend.map((score, index) => (
                <div key={score.date} className="flex flex-col items-center">
                  <div className="h-24 w-1 bg-indigo-100 rounded-t-lg relative">
                    <div 
                      className="absolute bottom-0 w-full bg-indigo-500 rounded-t-lg transition-all duration-500"
                      style={{ height: `${score.averageScore}%` }}
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">{score.date}</p>
                  <p className="text-xs font-medium">{score.averageScore}分</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 