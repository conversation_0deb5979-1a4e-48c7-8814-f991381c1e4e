'use client'

import Image from 'next/image'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faUsers, 
  faUserCheck, 
  faStar,
  faUserClock,
  faUserTimes
} from '@fortawesome/free-solid-svg-icons'
import { TeamExam } from '@/app/types/team'

interface ExamParticipant {
  id: string
  name: string
  avatar: string
  status: 'registered' | 'participated' | 'absent'
  score?: number
  rank?: number
}

interface ExamParticipantsProps {
  participants: ExamParticipant[]
  status: TeamExam['status']
}

export default function ExamParticipants({ 
  participants, 
  status 
}: ExamParticipantsProps) {
  // 标题根据考试状态变化
  const getTitle = () => {
    switch (status) {
      case 'upcoming':
        return '已报名成员'
      case 'ongoing':
        return '参与成员'
      case 'ended':
        return '参与成绩'
      default:
        return '参与成员'
    }
  }

  // 显示状态图标
  const getStatusIcon = (status: ExamParticipant['status']) => {
    switch (status) {
      case 'registered':
        return <FontAwesomeIcon icon={faUserClock} className="text-indigo-500" />
      case 'participated':
        return <FontAwesomeIcon icon={faUserCheck} className="text-green-500" />
      case 'absent':
        return <FontAwesomeIcon icon={faUserTimes} className="text-gray-400" />
      default:
        return null
    }
  }
  
  // 显示状态文本
  const getStatusText = (status: ExamParticipant['status']) => {
    switch (status) {
      case 'registered':
        return '已报名'
      case 'participated':
        return '已参加'
      case 'absent':
        return '缺席'
      default:
        return ''
    }
  }

  // 根据考试状态返回适合的信息显示
  const renderParticipantInfo = (participant: ExamParticipant) => {
    switch (status) {
      case 'upcoming':
        return (
          <div className="flex items-center text-xs text-gray-500">
            {getStatusIcon(participant.status)}
            <span className="ml-1">{getStatusText(participant.status)}</span>
          </div>
        )
      case 'ongoing':
        return (
          <div className="flex items-center text-xs text-gray-500">
            {participant.status === 'participated' ? (
              <>
                <FontAwesomeIcon icon={faUserCheck} className="text-green-500" />
                <span className="ml-1">进行中</span>
              </>
            ) : (
              <>
                {getStatusIcon(participant.status)}
                <span className="ml-1">{getStatusText(participant.status)}</span>
              </>
            )}
          </div>
        )
      case 'ended':
        if (participant.status === 'participated' && participant.score !== undefined) {
          return (
            <div className="flex flex-col items-end">
              <span className="text-sm font-semibold text-gray-800">{participant.score}分</span>
              {participant.rank && (
                <div className="flex items-center text-xs text-gray-500">
                  <FontAwesomeIcon icon={faStar} className="text-yellow-500 mr-1" />
                  <span>排名 #{participant.rank}</span>
                </div>
              )}
            </div>
          )
        } else {
          return (
            <div className="text-xs text-gray-500 flex items-center">
              {getStatusIcon(participant.status)}
              <span className="ml-1">{getStatusText(participant.status)}</span>
            </div>
          )
        }
      default:
        return null
    }
  }

  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden">
      <div className="p-6">
        <div className="flex items-center mb-4">
          <FontAwesomeIcon icon={faUsers} className="text-indigo-500 mr-2" />
          <h2 className="text-lg font-semibold text-gray-800">{getTitle()}</h2>
          <span className="ml-2 text-sm text-gray-500">({participants.length})</span>
        </div>

        {participants.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            暂无成员报名参加
          </div>
        ) : (
          <div className="space-y-4">
            {participants.map(participant => (
              <div key={participant.id} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="relative w-10 h-10 rounded-full overflow-hidden mr-3">
                    <Image
                      src={participant.avatar}
                      alt={participant.name}
                      fill
                      sizes="40px"
                      className="object-cover"
                    />
                  </div>
                  <div>
                    <div className="font-medium text-gray-800">{participant.name}</div>
                  </div>
                </div>
                {renderParticipantInfo(participant)}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
} 