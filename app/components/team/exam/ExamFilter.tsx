'use client'

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faListAlt,
  faCalendarAlt, 
  faHourglass,
  faFlagCheckered
} from '@fortawesome/free-solid-svg-icons'

interface ExamFilterProps {
  currentFilter: 'all' | 'upcoming' | 'ongoing' | 'ended'
  onFilterChange: (filter: 'all' | 'upcoming' | 'ongoing' | 'ended') => void
  examCount: {
    all: number
    upcoming: number
    ongoing: number
    ended: number
  }
}

export default function ExamFilter({ 
  currentFilter, 
  onFilterChange,
  examCount 
}: ExamFilterProps) {
  const filters = [
    { id: 'all', label: '全部考试', icon: faListAlt, count: examCount.all },
    { id: 'upcoming', label: '即将开始', icon: faCalendarAlt, count: examCount.upcoming },
    { id: 'ongoing', label: '进行中', icon: faHourglass, count: examCount.ongoing },
    { id: 'ended', label: '已结束', icon: faFlagCheckered, count: examCount.ended }
  ]

  return (
    <div className="bg-white rounded-xl shadow-sm mb-6 overflow-hidden">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4">
        {filters.map((filter) => (
          <button
            key={filter.id}
            onClick={() => onFilterChange(filter.id as any)}
            className={`py-4 px-6 flex items-center justify-center sm:justify-start transition ${
              currentFilter === filter.id
                ? 'bg-indigo-50 text-indigo-700 border-b-2 border-indigo-600'
                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800'
            }`}
          >
            <div className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                currentFilter === filter.id
                  ? 'bg-indigo-100'
                  : 'bg-gray-100'
              }`}>
                <FontAwesomeIcon 
                  icon={filter.icon} 
                  className={currentFilter === filter.id ? 'text-indigo-600' : 'text-gray-500'} 
                />
              </div>
              <div className="text-left">
                <div className="font-medium">{filter.label}</div>
                <div className="text-xs mt-0.5">
                  {filter.count}个考试
                </div>
              </div>
            </div>
          </button>
        ))}
      </div>
    </div>
  )
} 