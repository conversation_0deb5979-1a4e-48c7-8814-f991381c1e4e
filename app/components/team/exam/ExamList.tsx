'use client'

import Link from 'next/link'
import { TeamExam } from '@/app/types/team'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faCalendarAlt, 
  faHourglass, 
  faFlagCheckered,
  faFileAlt,
  faChartBar,
  faPlayCircle,
  faClock,
  faUsers,
  faMedal
} from '@fortawesome/free-solid-svg-icons'

interface ExamListProps {
  exams: TeamExam[]
  isLoading: boolean
  teamId: string
}

export default function ExamList({ exams, isLoading, teamId }: ExamListProps) {
  if (isLoading) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-8 text-center">
        <div className="inline-block w-12 h-12 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
        <p className="mt-4 text-gray-500">正在加载考试...</p>
      </div>
    )
  }

  if (exams.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-8 text-center">
        <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <FontAwesomeIcon icon={faFileAlt} className="text-gray-400 text-2xl" />
        </div>
        <h3 className="text-lg font-medium text-gray-800 mb-2">没有找到考试</h3>
        <p className="text-gray-500">
          当前筛选条件下没有找到任何考试，请尝试更改筛选条件或创建新考试。
        </p>
        <button className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition">
          创建考试
        </button>
      </div>
    )
  }

  // 格式化考试时间
  const formatExamTime = (startTime: string, duration: number) => {
    const start = new Date(startTime)
    const end = new Date(start.getTime() + duration * 60 * 1000)
    
    const formatTime = (date: Date) => {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    }
    
    const formatDate = (date: Date) => {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' })
    }
    
    const sameDay = start.getDate() === end.getDate() && 
                    start.getMonth() === end.getMonth() && 
                    start.getFullYear() === end.getFullYear()
    
    if (sameDay) {
      return `${formatDate(start)} ${formatTime(start)} - ${formatTime(end)}`
    } else {
      return `${formatDate(start)} ${formatTime(start)} - ${formatDate(end)} ${formatTime(end)}`
    }
  }

  const getExamCardClass = (status: TeamExam['status']) => {
    switch (status) {
      case 'upcoming':
        return 'border-indigo-200 hover:border-indigo-400'
      case 'ongoing':
        return 'border-green-200 hover:border-green-400'
      case 'ended':
        return 'border-gray-200 hover:border-gray-400'
      default:
        return 'border-gray-200 hover:border-gray-400'
    }
  }

  const getExamStatusIcon = (status: TeamExam['status']) => {
    switch (status) {
      case 'upcoming':
        return (
          <div className="bg-indigo-100 p-3 rounded-full">
            <FontAwesomeIcon icon={faCalendarAlt} className="text-indigo-600" />
          </div>
        )
      case 'ongoing':
        return (
          <div className="bg-green-100 p-3 rounded-full">
            <FontAwesomeIcon icon={faHourglass} className="text-green-600" />
          </div>
        )
      case 'ended':
        return (
          <div className="bg-gray-100 p-3 rounded-full">
            <FontAwesomeIcon icon={faFlagCheckered} className="text-gray-600" />
          </div>
        )
      default:
        return null
    }
  }

  const getStatusBadge = (status: TeamExam['status']) => {
    switch (status) {
      case 'upcoming':
        return (
          <span className="bg-indigo-100 text-indigo-800 text-xs px-2 py-1 rounded-full inline-flex items-center">
            <FontAwesomeIcon icon={faCalendarAlt} className="mr-1" />
            即将开始
          </span>
        )
      case 'ongoing':
        return (
          <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full inline-flex items-center">
            <FontAwesomeIcon icon={faHourglass} className="mr-1" />
            进行中
          </span>
        )
      case 'ended':
        return (
          <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full inline-flex items-center">
            <FontAwesomeIcon icon={faFlagCheckered} className="mr-1" />
            已结束
          </span>
        )
      default:
        return null
    }
  }

  const getActionButton = (exam: TeamExam) => {
    switch (exam.status) {
      case 'upcoming':
        return (
          <Link
            href={`/teams/${teamId}/exams/${exam.id}`}
            className="w-full px-4 py-2 border border-indigo-600 text-indigo-600 rounded-lg hover:bg-indigo-50 flex items-center justify-center"
          >
            <FontAwesomeIcon icon={faCalendarAlt} className="mr-1" />
            查看详情
          </Link>
        )
      case 'ongoing':
        return (
          <Link
            href={`/teams/${teamId}/exams/${exam.id}/take`}
            className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center justify-center"
          >
            <FontAwesomeIcon icon={faPlayCircle} className="mr-1" />
            参加考试
          </Link>
        )
      case 'ended':
        return (
          <Link
            href={`/teams/${teamId}/exams/${exam.id}/results`}
            className="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 flex items-center justify-center"
          >
            <FontAwesomeIcon icon={faChartBar} className="mr-1" />
            查看成绩
          </Link>
        )
      default:
        return null
    }
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {exams.map(exam => (
        <div 
          key={exam.id} 
          className={`bg-white rounded-xl shadow-sm overflow-hidden border ${getExamCardClass(exam.status)}`}
        >
          <div className="p-6">
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-lg font-semibold text-gray-800">{exam.title}</h3>
              {getStatusBadge(exam.status)}
            </div>
            
            <p className="text-gray-600 mb-4 line-clamp-2">{exam.description}</p>
            
            <div className="space-y-2 mb-6">
              <div className="flex items-center text-sm text-gray-500">
                <FontAwesomeIcon icon={faCalendarAlt} className="mr-2 text-gray-400" />
                <span>{formatExamTime(exam.startTime, exam.duration)}</span>
              </div>
              
              <div className="flex items-center text-sm text-gray-500">
                <FontAwesomeIcon icon={faClock} className="mr-2 text-gray-400" />
                <span>时长: {exam.duration} 分钟</span>
              </div>
              
              {exam.status !== 'upcoming' && (
                <div className="flex items-center text-sm text-gray-500">
                  <FontAwesomeIcon icon={faUsers} className="mr-2 text-gray-400" />
                  <span>参与人数: {exam.participantCount} 人</span>
                </div>
              )}
            </div>
            
            {getActionButton(exam)}
          </div>
        </div>
      ))}
    </div>
  )
} 