'use client'

import { TeamExam } from '@/app/types/team'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faCalendarAlt,
  faClock,
  faFileAlt,
  faMedal,
  faUsers,
  faPlayCircle
} from '@fortawesome/free-solid-svg-icons'
import { useState } from 'react'
import ReactMarkdown from 'react-markdown'

interface ExamDetailProps {
  exam: TeamExam
  isRegistered: boolean
  onRegister: () => void
  onStartExam: () => void
}

export default function ExamDetail({
  exam,
  isRegistered,
  onRegister,
  onStartExam
}: ExamDetailProps) {
  const [registering, setRegistering] = useState(false)
  const [starting, setStarting] = useState(false)

  // 格式化考试时间
  const formatExamTime = (startTime: string, duration: number) => {
    const start = new Date(startTime)
    const end = new Date(start.getTime() + duration * 60 * 1000)
    
    const formatTime = (date: Date) => {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    }
    
    const formatDate = (date: Date) => {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' })
    }
    
    const sameDay = start.getDate() === end.getDate() && 
                    start.getMonth() === end.getMonth() && 
                    start.getFullYear() === end.getFullYear()
    
    if (sameDay) {
      return `${formatDate(start)} ${formatTime(start)} - ${formatTime(end)}`
    } else {
      return `${formatDate(start)} ${formatTime(start)} - ${formatDate(end)} ${formatTime(end)}`
    }
  }

  // 获取考试状态的样式
  const getStatusBadge = (status: TeamExam['status']) => {
    switch (status) {
      case 'upcoming':
        return (
          <span className="bg-indigo-100 text-indigo-800 text-xs px-3 py-1 rounded-full inline-flex items-center">
            <FontAwesomeIcon icon={faCalendarAlt} className="mr-1" />
            即将开始
          </span>
        )
      case 'ongoing':
        return (
          <span className="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full inline-flex items-center">
            <FontAwesomeIcon icon={faClock} className="mr-1" />
            进行中
          </span>
        )
      case 'ended':
        return (
          <span className="bg-gray-100 text-gray-800 text-xs px-3 py-1 rounded-full inline-flex items-center">
            <FontAwesomeIcon icon={faMedal} className="mr-1" />
            已结束
          </span>
        )
      default:
        return null
    }
  }

  // 获取操作按钮
  const getActionButton = () => {
    switch (exam.status) {
      case 'upcoming':
        if (isRegistered) {
          return (
            <div className="bg-indigo-50 text-indigo-600 px-4 py-3 rounded-lg flex items-center justify-center mb-4">
              <FontAwesomeIcon icon={faCalendarAlt} className="mr-2" />
              <span>您已成功报名，考试开始时将向您发送通知</span>
            </div>
          )
        } else {
          return (
            <button
              onClick={() => {
                setRegistering(true)
                onRegister()
                setTimeout(() => setRegistering(false), 1000)
              }}
              disabled={registering}
              className="w-full px-4 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 flex items-center justify-center mb-4 disabled:opacity-70"
            >
              {registering ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  <span>正在报名...</span>
                </>
              ) : (
                <>
                  <FontAwesomeIcon icon={faCalendarAlt} className="mr-2" />
                  <span>立即报名</span>
                </>
              )}
            </button>
          )
        }
      case 'ongoing':
        return (
          <button
            onClick={() => {
              setStarting(true)
              onStartExam()
            }}
            disabled={starting}
            className="w-full px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center justify-center mb-4 disabled:opacity-70"
          >
            {starting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                <span>准备中...</span>
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faPlayCircle} className="mr-2" />
                <span>开始考试</span>
              </>
            )}
          </button>
        )
      case 'ended':
        return (
          <button
            className="w-full px-4 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 flex items-center justify-center mb-4"
          >
            <FontAwesomeIcon icon={faMedal} className="mr-2" />
            <span>查看成绩</span>
          </button>
        )
      default:
        return null
    }
  }

  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden">
      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <h1 className="text-2xl font-bold text-gray-800">{exam.title}</h1>
          {getStatusBadge(exam.status)}
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="flex items-center text-sm text-gray-500">
            <FontAwesomeIcon icon={faCalendarAlt} className="mr-2 text-gray-400" />
            <span>{formatExamTime(exam.startTime, exam.duration)}</span>
          </div>
          
          <div className="flex items-center text-sm text-gray-500">
            <FontAwesomeIcon icon={faClock} className="mr-2 text-gray-400" />
            <span>考试时长: {exam.duration} 分钟</span>
          </div>
          
          {exam.totalQuestions && (
            <div className="flex items-center text-sm text-gray-500">
              <FontAwesomeIcon icon={faFileAlt} className="mr-2 text-gray-400" />
              <span>题目数量: {exam.totalQuestions} 题</span>
            </div>
          )}
          
          {exam.totalScore && (
            <div className="flex items-center text-sm text-gray-500">
              <FontAwesomeIcon icon={faMedal} className="mr-2 text-gray-400" />
              <span>总分值: {exam.totalScore} 分</span>
            </div>
          )}
          
          {exam.status !== 'upcoming' && (
            <div className="flex items-center text-sm text-gray-500">
              <FontAwesomeIcon icon={faUsers} className="mr-2 text-gray-400" />
              <span>参与人数: {exam.participantCount} 人</span>
            </div>
          )}
        </div>
        
        {/* 考试操作区 */}
        {getActionButton()}
        
        {/* 考试说明 */}
        <div className="prose prose-indigo max-w-none">
          <ReactMarkdown>{exam.description}</ReactMarkdown>
        </div>
      </div>
    </div>
  )
} 