'use client'

import { useState } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faUpload, 
  faSave, 
  faSpinner,
  faTrash
} from '@fortawesome/free-solid-svg-icons'
import Image from 'next/image'

interface TeamBasicSettingsProps {
  team: any
}

export default function TeamBasicSettings({ team }: TeamBasicSettingsProps) {
  const [formData, setFormData] = useState({
    name: team.name || '',
    description: team.description || '',
    type: team.type || 'contest',
    visibility: 'public',
    joinMethod: 'verify',
    password: ''
  })
  const [avatar, setAvatar] = useState<File | null>(null)
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }
  
  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      setAvatar(file)
      setAvatarPreview(URL.createObjectURL(file))
    }
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // TODO: 实现保存团队信息的逻辑
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    setIsSubmitting(false)
    // 显示成功消息
    alert('团队信息已更新')
  }
  
  const teamTypes = [
    { value: 'class', label: '班级/教学' },
    { value: 'contest', label: '竞赛/培训' },
    { value: 'group', label: '兴趣小组' }
  ]
  
  const joinMethods = [
    { value: 'open', label: '公开加入' },
    { value: 'password', label: '密码加入' },
    { value: 'verify', label: '验证加入' }
  ]
  
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-xl font-bold mb-6 text-gray-800">基本设置</h2>
      
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="md:col-span-2">
            <div className="mb-4">
              <label className="block text-gray-700 font-medium mb-2" htmlFor="name">
                团队名称
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-400"
                required
              />
            </div>
            
            <div className="mb-4">
              <label className="block text-gray-700 font-medium mb-2" htmlFor="description">
                团队描述
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-400"
              />
            </div>
            
            <div className="mb-4">
              <label className="block text-gray-700 font-medium mb-2" htmlFor="type">
                团队类型
              </label>
              <select
                id="type"
                name="type"
                value={formData.type}
                onChange={handleInputChange}
                className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-400"
              >
                {teamTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          <div>
            <label className="block text-gray-700 font-medium mb-2">
              团队头像
            </label>
            <div className="flex flex-col items-center">
              <div className="w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center mb-3 overflow-hidden">
                {avatarPreview ? (
                  <Image 
                    src={avatarPreview} 
                    alt="团队头像预览" 
                    width={128} 
                    height={128} 
                    className="object-cover w-full h-full"
                  />
                ) : team.avatar ? (
                  <Image 
                    src={team.avatar} 
                    alt="团队头像" 
                    width={128} 
                    height={128} 
                    className="object-cover w-full h-full"
                  />
                ) : (
                  <div className="text-center p-4 text-gray-400">
                    <FontAwesomeIcon icon={faUpload} className="text-2xl mb-2" />
                    <p className="text-xs">点击上传头像</p>
                  </div>
                )}
              </div>
              <label className="px-4 py-2 bg-indigo-600 text-white rounded-lg cursor-pointer hover:bg-indigo-700 transition">
                选择图片
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleAvatarChange}
                  className="hidden"
                />
              </label>
              {(avatarPreview || team.avatar) && (
                <button 
                  type="button"
                  onClick={() => {
                    setAvatar(null)
                    setAvatarPreview(null)
                  }}
                  className="text-red-600 mt-2 text-sm flex items-center"
                >
                  <FontAwesomeIcon icon={faTrash} className="mr-1" />
                  删除头像
                </button>
              )}
            </div>
          </div>
        </div>
        
        <div className="border-t border-gray-200 pt-6 mb-6">
          <h3 className="text-lg font-medium mb-4 text-gray-800">加入设置</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-gray-700 font-medium mb-2" htmlFor="visibility">
                可见性
              </label>
              <div className="flex gap-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="visibility"
                    value="public"
                    checked={formData.visibility === 'public'}
                    onChange={handleInputChange}
                    className="mr-2"
                  />
                  公开
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="visibility"
                    value="private"
                    checked={formData.visibility === 'private'}
                    onChange={handleInputChange}
                    className="mr-2"
                  />
                  私密
                </label>
              </div>
            </div>
            
            <div>
              <label className="block text-gray-700 font-medium mb-2" htmlFor="joinMethod">
                加入方式
              </label>
              <select
                id="joinMethod"
                name="joinMethod"
                value={formData.joinMethod}
                onChange={handleInputChange}
                className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-400"
              >
                {joinMethods.map(method => (
                  <option key={method.value} value={method.value}>
                    {method.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          {formData.joinMethod === 'password' && (
            <div className="mt-4">
              <label className="block text-gray-700 font-medium mb-2" htmlFor="password">
                加入密码
              </label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-400"
                placeholder="设置加入团队的密码"
              />
            </div>
          )}
        </div>
        
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isSubmitting}
            className={`px-6 py-2 rounded-lg font-medium flex items-center ${
              isSubmitting 
                ? 'bg-gray-400 cursor-not-allowed' 
                : 'bg-indigo-600 text-white hover:bg-indigo-700'
            }`}
          >
            {isSubmitting ? (
              <>
                <FontAwesomeIcon icon={faSpinner} className="mr-2 animate-spin" />
                保存中...
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faSave} className="mr-2" />
                保存设置
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  )
} 