'use client'

import { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faUserPlus, 
  faBook, 
  faFileAlt, 
  faListAlt, 
  faCode,
  faInfoCircle,
  faCalendarCheck
} from '@fortawesome/free-solid-svg-icons'

interface TeamActivityLogProps {
  teamId: string
}

interface ActivityItem {
  id: string
  type: 'member' | 'homework' | 'exam' | 'problem-set' | 'problem' | 'announcement' | 'setting'
  title: string
  description: string
  timestamp: string
  user: {
    id: string
    name: string
    avatar?: string
  }
}

export default function TeamActivityLog({ teamId }: TeamActivityLogProps) {
  const [activities, setActivities] = useState<ActivityItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  
  useEffect(() => {
    // 加载团队活动日志
    setIsLoading(true)
    
    // 模拟数据
    const mockActivities: ActivityItem[] = [
      {
        id: '1',
        type: 'member',
        title: '新成员加入',
        description: '李明加入了团队',
        timestamp: '2023-10-15 14:30',
        user: {
          id: 'user1',
          name: '张老师',
          avatar: ''
        }
      },
      {
        id: '2',
        type: 'homework',
        title: '发布了新作业',
        description: '基础算法练习 #3',
        timestamp: '2023-10-14 10:15',
        user: {
          id: 'user1',
          name: '张老师',
          avatar: ''
        }
      },
      {
        id: '3',
        type: 'exam',
        title: '创建了新考试',
        description: '动态规划模拟考试',
        timestamp: '2023-10-13 09:20',
        user: {
          id: 'user1',
          name: '张老师',
          avatar: ''
        }
      },
      {
        id: '4',
        type: 'problem-set',
        title: '更新了题单',
        description: '图论专题题单',
        timestamp: '2023-10-12 16:45',
        user: {
          id: 'user2',
          name: '王助教',
          avatar: ''
        }
      },
      {
        id: '5',
        type: 'setting',
        title: '更新了团队设置',
        description: '修改了团队描述',
        timestamp: '2023-10-10 11:30',
        user: {
          id: 'user1',
          name: '张老师',
          avatar: ''
        }
      }
    ]
    
    // 模拟API延迟
    setTimeout(() => {
      setActivities(mockActivities)
      setIsLoading(false)
    }, 500)
  }, [teamId])
  
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'member':
        return { icon: faUserPlus, color: 'bg-blue-100 text-blue-500' }
      case 'homework':
        return { icon: faBook, color: 'bg-green-100 text-green-500' }
      case 'exam':
        return { icon: faFileAlt, color: 'bg-red-100 text-red-500' }
      case 'problem-set':
        return { icon: faListAlt, color: 'bg-yellow-100 text-yellow-500' }
      case 'problem':
        return { icon: faCode, color: 'bg-purple-100 text-purple-500' }
      case 'announcement':
        return { icon: faInfoCircle, color: 'bg-indigo-100 text-indigo-500' }
      case 'setting':
        return { icon: faCalendarCheck, color: 'bg-gray-100 text-gray-500' }
      default:
        return { icon: faInfoCircle, color: 'bg-gray-100 text-gray-500' }
    }
  }

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow p-6 mt-6">
        <h2 className="text-xl font-bold mb-6 text-gray-800">团队活动</h2>
        <div className="flex justify-center py-8">
          <div className="inline-block w-8 h-8 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
          <p className="ml-3 text-gray-500">加载活动记录...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow p-6 mt-6">
      <h2 className="text-xl font-bold mb-6 text-gray-800">团队活动</h2>
      
      {activities.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p>暂无活动记录</p>
        </div>
      ) : (
        <div className="space-y-4">
          {activities.map(activity => {
            const { icon, color } = getActivityIcon(activity.type)
            
            return (
              <div key={activity.id} className="flex">
                <div className={`p-2 rounded-full ${color} mr-4`}>
                  <FontAwesomeIcon icon={icon} />
                </div>
                <div className="flex-1">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium text-gray-800">{activity.title}</h4>
                      <p className="text-sm text-gray-600">{activity.description}</p>
                    </div>
                    <div className="text-xs text-gray-500">{activity.timestamp}</div>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    由 {activity.user.name} 操作
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      )}
      
      {activities.length > 0 && (
        <div className="mt-6 text-center">
          <button className="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
            查看更多活动记录
          </button>
        </div>
      )}
    </div>
  )
} 