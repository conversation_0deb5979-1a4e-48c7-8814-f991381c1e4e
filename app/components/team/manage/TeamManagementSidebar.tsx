'use client'

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faInfoCircle,
  faUsers,
  faBook,
  faFileAlt,
  faListAlt,
  faCode,
  faHistory,
  faCogs,
  faChartLine
} from '@fortawesome/free-solid-svg-icons'

interface TeamManagementSidebarProps {
  activeSection: string
  onSectionChange: (section: string) => void
}

export default function TeamManagementSidebar({
  activeSection,
  onSectionChange
}: TeamManagementSidebarProps) {
  const menuItems = [
    {
      id: 'basic-info',
      name: '基本设置',
      icon: faInfoCircle
    },
    {
      id: 'members',
      name: '成员管理',
      icon: faUsers
    },
    {
      id: 'statistics',
      name: '数据统计',
      icon: faChartLine
    },
    {
      id: 'homework',
      name: '作业管理',
      icon: faBook
    },
    {
      id: 'exams',
      name: '考试管理',
      icon: faFileAlt
    },
    {
      id: 'problem-sets',
      name: '题单管理',
      icon: faListAlt
    },
    {
      id: 'problems',
      name: '题目库',
      icon: faCode
    },
    {
      id: 'activity-log',
      name: '活动记录',
      icon: faHistory
    },
    {
      id: 'advanced',
      name: '高级设置',
      icon: faCogs
    }
  ]

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <h2 className="text-lg font-bold mb-4 px-3 text-gray-800">团队管理</h2>
      <nav>
        <ul className="space-y-1">
          {menuItems.map((item) => (
            <li key={item.id}>
              <button
                className={`w-full text-left px-3 py-2 rounded-md flex items-center 
                  ${activeSection === item.id 
                    ? 'bg-indigo-50 text-indigo-600 font-medium' 
                    : 'text-gray-700 hover:bg-gray-100'}`}
                onClick={() => onSectionChange(item.id)}
              >
                <FontAwesomeIcon 
                  icon={item.icon} 
                  className={`mr-3 ${activeSection === item.id ? 'text-indigo-500' : 'text-gray-500'}`} 
                  fixedWidth
                />
                {item.name}
              </button>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  )
} 