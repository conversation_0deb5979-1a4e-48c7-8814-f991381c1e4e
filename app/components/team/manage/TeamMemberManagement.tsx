'use client'

import { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faUserPlus, 
  faUserMinus, 
  faUserCog, 
  faSearch,
  faS<PERSON>ner,
  faCrown,
  faUserShield,
  faUser,
  faEllipsisH
} from '@fortawesome/free-solid-svg-icons'
import Image from 'next/image'

interface TeamMemberManagementProps {
  teamId: string
}

interface Member {
  id: string
  name: string
  avatar: string | null
  email: string
  role: 'creator' | 'admin' | 'member'
  joinedAt: string
  lastActiveAt: string
  activityScore: number
}

export default function TeamMemberManagement({ teamId }: TeamMemberManagementProps) {
  const [members, setMembers] = useState<Member[]>([])
  const [filteredMembers, setFilteredMembers] = useState<Member[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedRole, setSelectedRole] = useState<string | null>(null)
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false)
  
  useEffect(() => {
    // 加载团队成员
    setIsLoading(true)
    
    // 模拟数据
    const mockMembers: Member[] = [
      {
        id: 'user1',
        name: '张老师',
        avatar: null,
        email: '<EMAIL>',
        role: 'creator',
        joinedAt: '2023-09-15',
        lastActiveAt: '2023-10-15',
        activityScore: 98
      },
      {
        id: 'user2',
        name: '王助教',
        avatar: null,
        email: '<EMAIL>',
        role: 'admin',
        joinedAt: '2023-09-16',
        lastActiveAt: '2023-10-14',
        activityScore: 92
      },
      {
        id: 'user3',
        name: '李明',
        avatar: null,
        email: '<EMAIL>',
        role: 'member',
        joinedAt: '2023-09-18',
        lastActiveAt: '2023-10-13',
        activityScore: 85
      },
      {
        id: 'user4',
        name: '赵小红',
        avatar: null,
        email: '<EMAIL>',
        role: 'member',
        joinedAt: '2023-09-20',
        lastActiveAt: '2023-10-15',
        activityScore: 90
      },
      {
        id: 'user5',
        name: '刘华',
        avatar: null,
        email: '<EMAIL>',
        role: 'member',
        joinedAt: '2023-09-22',
        lastActiveAt: '2023-10-10',
        activityScore: 75
      }
    ]
    
    // 模拟API延迟
    setTimeout(() => {
      setMembers(mockMembers)
      setFilteredMembers(mockMembers)
      setIsLoading(false)
    }, 500)
  }, [teamId])
  
  // 过滤成员
  useEffect(() => {
    let filtered = [...members]
    
    // 按名称或邮箱搜索
    if (searchQuery) {
      filtered = filtered.filter(
        member => 
          member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          member.email.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }
    
    // 按角色过滤
    if (selectedRole) {
      filtered = filtered.filter(member => member.role === selectedRole)
    }
    
    setFilteredMembers(filtered)
  }, [members, searchQuery, selectedRole])
  
  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'creator':
        return (
          <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs flex items-center">
            <FontAwesomeIcon icon={faCrown} className="mr-1" />
            创建者
          </span>
        )
      case 'admin':
        return (
          <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs flex items-center">
            <FontAwesomeIcon icon={faUserShield} className="mr-1" />
            管理员
          </span>
        )
      default:
        return (
          <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs flex items-center">
            <FontAwesomeIcon icon={faUser} className="mr-1" />
            成员
          </span>
        )
    }
  }
  
  const getActivityBadge = (score: number) => {
    if (score >= 90) {
      return <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">活跃</span>
    } else if (score >= 70) {
      return <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">一般</span>
    } else {
      return <span className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs">较少</span>
    }
  }
  
  // 打开邀请成员模态框
  const openInviteModal = () => {
    setIsInviteModalOpen(true)
  }
  
  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-bold mb-6 text-gray-800">成员管理</h2>
        <div className="flex justify-center py-8">
          <div className="inline-block w-8 h-8 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
          <p className="ml-3 text-gray-500">加载成员...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold text-gray-800">成员管理</h2>
        
        <div className="flex space-x-2">
          <button 
            onClick={openInviteModal}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition flex items-center"
          >
            <FontAwesomeIcon icon={faUserPlus} className="mr-2" />
            邀请成员
          </button>
        </div>
      </div>
      
      <div className="mb-6 flex flex-col md:flex-row justify-between gap-4">
        {/* 搜索框 */}
        <div className="relative md:w-1/3">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FontAwesomeIcon icon={faSearch} className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="搜索成员..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-400"
          />
        </div>
        
        {/* 角色过滤 */}
        <div className="flex space-x-2">
          <button
            onClick={() => setSelectedRole(null)}
            className={`px-3 py-1 rounded-full text-sm ${
              selectedRole === null
                ? 'bg-indigo-100 text-indigo-800'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            全部
          </button>
          <button
            onClick={() => setSelectedRole('creator')}
            className={`px-3 py-1 rounded-full text-sm ${
              selectedRole === 'creator'
                ? 'bg-yellow-100 text-yellow-800'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            创建者
          </button>
          <button
            onClick={() => setSelectedRole('admin')}
            className={`px-3 py-1 rounded-full text-sm ${
              selectedRole === 'admin'
                ? 'bg-blue-100 text-blue-800'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            管理员
          </button>
          <button
            onClick={() => setSelectedRole('member')}
            className={`px-3 py-1 rounded-full text-sm ${
              selectedRole === 'member'
                ? 'bg-gray-100 text-gray-800'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            成员
          </button>
        </div>
      </div>
      
      {/* 成员列表 */}
      {filteredMembers.length === 0 ? (
        <div className="bg-gray-50 rounded-lg p-8 text-center">
          <p className="text-gray-500">未找到匹配的成员</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  成员
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  邮箱
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  角色
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  加入时间
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  活跃度
                </th>
                <th className="px-4 py-3"></th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredMembers.map(member => (
                <tr key={member.id} className="hover:bg-gray-50">
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-full overflow-hidden">
                        {member.avatar ? (
                          <Image 
                            src={member.avatar}
                            alt={member.name}
                            width={40}
                            height={40}
                            className="h-10 w-10 rounded-full"
                          />
                        ) : (
                          <div className="h-10 w-10 flex items-center justify-center text-gray-500 bg-gray-100 rounded-full">
                            {member.name.charAt(0)}
                          </div>
                        )}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{member.name}</div>
                        <div className="text-sm text-gray-500">上次活跃: {member.lastActiveAt}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{member.email}</div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    {getRoleBadge(member.role)}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                    {member.joinedAt}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    {getActivityBadge(member.activityScore)}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="relative group">
                      <button className="text-gray-500 hover:text-gray-700 p-1">
                        <FontAwesomeIcon icon={faEllipsisH} />
                      </button>
                      <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 hidden group-hover:block">
                        <div className="py-1">
                          {member.role !== 'admin' && member.role !== 'creator' && (
                            <button className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left">
                              设为管理员
                            </button>
                          )}
                          {member.role === 'admin' && (
                            <button className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left">
                              取消管理员
                            </button>
                          )}
                          <button className="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100 w-full text-left">
                            移除成员
                          </button>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      
      {/* 邀请成员模态框 - 根据需要实现 */}
    </div>
  )
} 