'use client'

import Link from 'next/link'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faUsers, 
  faBook, 
  faFileAlt, 
  faListAlt, 
  faCode,
  faUserCog,
  faChevronRight,
  faChartLine
} from '@fortawesome/free-solid-svg-icons'

interface TeamManagementCardsProps {
  team: any
}

export default function TeamManagementCards({ team }: TeamManagementCardsProps) {
  const cards = [
    {
      id: 'members',
      title: '成员管理',
      icon: faUsers,
      color: 'bg-blue-500',
      description: `管理${team.memberCount}名成员和${team.adminCount}名管理员`,
      stats: [{
        label: '成员数',
        value: team.memberCount
      }],
      link: `/teams/${team.id}/manage?section=members`
    },
    {
      id: 'statistics',
      title: '数据统计',
      icon: faChartLine,
      color: 'bg-indigo-500',
      description: '查看团队活动、作业、考试等数据分析',
      stats: [{
        label: '活跃成员',
        value: Math.round(team.memberCount * 0.8)
      }],
      link: `/teams/${team.id}/manage?section=statistics`
    },
    {
      id: 'homework',
      title: '作业管理',
      icon: faBook,
      color: 'bg-green-500',
      description: `管理${team.homeworkCount}个作业，${team.ongoingHomeworkCount}个正在进行`,
      stats: [{
        label: '作业数',
        value: team.homeworkCount
      }, {
        label: '进行中',
        value: team.ongoingHomeworkCount
      }],
      link: `/teams/${team.id}/manage?section=homework`
    },
    {
      id: 'exams',
      title: '考试管理',
      icon: faFileAlt,
      color: 'bg-red-500',
      description: `管理${team.examCount}场考试，${team.upcomingExamCount}场即将开始`,
      stats: [{
        label: '考试数',
        value: team.examCount
      }, {
        label: '即将开始',
        value: team.upcomingExamCount
      }],
      link: `/teams/${team.id}/manage?section=exams`
    },
    {
      id: 'problem-sets',
      title: '题单管理',
      icon: faListAlt,
      color: 'bg-yellow-500',
      description: `管理${team.problemSetCount}个题单`,
      stats: [{
        label: '题单数',
        value: team.problemSetCount
      }],
      link: `/teams/${team.id}/manage?section=problem-sets`
    },
    {
      id: 'problems',
      title: '题目库',
      icon: faCode,
      color: 'bg-purple-500',
      description: `管理${team.problemCount}道题目`,
      stats: [{
        label: '题目数',
        value: team.problemCount
      }],
      link: `/teams/${team.id}/manage?section=problems`
    }
  ]

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-bold text-gray-800">团队管理功能</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {cards.map(card => (
          <Link
            href={card.link}
            key={card.id}
            className="bg-white rounded-lg shadow hover:shadow-md transition p-4 flex flex-col"
          >
            <div className="flex items-start mb-4">
              <div className={`${card.color} text-white p-2 rounded-lg mr-3`}>
                <FontAwesomeIcon icon={card.icon} className="text-lg" />
              </div>
              <div>
                <h3 className="font-bold text-gray-800">{card.title}</h3>
                <p className="text-sm text-gray-600">{card.description}</p>
              </div>
            </div>
            
            <div className="flex justify-between items-center mt-auto">
              <div className="flex space-x-4">
                {card.stats.map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className="text-xl font-bold text-gray-800">{stat.value}</div>
                    <div className="text-xs text-gray-500">{stat.label}</div>
                  </div>
                ))}
              </div>
              <div className="text-indigo-500">
                <FontAwesomeIcon icon={faChevronRight} />
              </div>
            </div>
          </Link>
        ))}
      </div>
    </div>
  )
} 