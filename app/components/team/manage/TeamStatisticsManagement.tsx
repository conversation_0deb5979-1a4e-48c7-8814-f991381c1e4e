'use client'

import { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faUsers, 
  faBook, 
  faFileAlt, 
  faSpinner,
  faChart<PERSON>ine,
  faChartPie,
  faChartBar
} from '@fortawesome/free-solid-svg-icons'

interface TeamStatisticsManagementProps {
  teamId: string
}

interface StatisticsData {
  memberActivity: {
    highActive: number
    mediumActive: number
    lowActive: number
  }
  homeworkCompletion: {
    completed: number
    inProgress: number
    notStarted: number
  }
  examScores: {
    excellent: number // 90-100
    good: number // 75-89
    average: number // 60-74
    poor: number // <60
  }
  problemSolving: {
    labels: string[]
    data: number[]
  }
  weeklyActivity: {
    dates: string[]
    counts: number[]
  }
}

export default function TeamStatisticsManagement({ teamId }: TeamStatisticsManagementProps) {
  const [statistics, setStatistics] = useState<StatisticsData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter' | 'year'>('month')
  
  useEffect(() => {
    // 加载团队统计数据
    setIsLoading(true)
    
    // 模拟数据 - 在实际应用中应从API获取
    const mockStatistics: StatisticsData = {
      memberActivity: {
        highActive: 5,
        mediumActive: 10,
        lowActive: 3
      },
      homeworkCompletion: {
        completed: 68,
        inProgress: 22,
        notStarted: 10
      },
      examScores: {
        excellent: 20,
        good: 45,
        average: 30,
        poor: 5
      },
      problemSolving: {
        labels: ['简单', '中等', '困难'],
        data: [85, 62, 40]
      },
      weeklyActivity: {
        dates: ['10月1日', '10月8日', '10月15日', '10月22日', '10月29日', '11月5日'],
        counts: [25, 32, 45, 38, 52, 60]
      }
    }
    
    // 模拟API延迟
    setTimeout(() => {
      setStatistics(mockStatistics)
      setIsLoading(false)
    }, 600)
  }, [teamId, selectedPeriod])
  
  if (isLoading || !statistics) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-bold mb-6 text-gray-800">数据统计</h2>
        <div className="flex justify-center py-8">
          <div className="inline-block w-8 h-8 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
          <p className="ml-3 text-gray-500">加载统计数据...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 标题和时间筛选 */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-gray-800">数据统计</h2>
          
          <div className="flex space-x-2">
            <button 
              onClick={() => setSelectedPeriod('week')}
              className={`px-3 py-1 text-sm rounded-full ${
                selectedPeriod === 'week' 
                  ? 'bg-indigo-100 text-indigo-800' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              本周
            </button>
            <button 
              onClick={() => setSelectedPeriod('month')}
              className={`px-3 py-1 text-sm rounded-full ${
                selectedPeriod === 'month' 
                  ? 'bg-indigo-100 text-indigo-800' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              本月
            </button>
            <button 
              onClick={() => setSelectedPeriod('quarter')}
              className={`px-3 py-1 text-sm rounded-full ${
                selectedPeriod === 'quarter' 
                  ? 'bg-indigo-100 text-indigo-800' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              本季度
            </button>
            <button 
              onClick={() => setSelectedPeriod('year')}
              className={`px-3 py-1 text-sm rounded-full ${
                selectedPeriod === 'year' 
                  ? 'bg-indigo-100 text-indigo-800' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              本年度
            </button>
          </div>
        </div>
        
        {/* 数据卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow p-4 text-white">
            <div className="flex items-center mb-4">
              <div className="bg-white bg-opacity-30 p-2 rounded-lg mr-3">
                <FontAwesomeIcon icon={faUsers} className="text-lg" />
              </div>
              <h3 className="font-medium">成员活跃度</h3>
            </div>
            <div className="flex justify-between">
              <div className="text-center">
                <div className="text-2xl font-bold">{statistics.memberActivity.highActive}</div>
                <div className="text-xs text-blue-100">高活跃</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{statistics.memberActivity.mediumActive}</div>
                <div className="text-xs text-blue-100">中活跃</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{statistics.memberActivity.lowActive}</div>
                <div className="text-xs text-blue-100">低活跃</div>
              </div>
            </div>
          </div>
          
          <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow p-4 text-white">
            <div className="flex items-center mb-4">
              <div className="bg-white bg-opacity-30 p-2 rounded-lg mr-3">
                <FontAwesomeIcon icon={faBook} className="text-lg" />
              </div>
              <h3 className="font-medium">作业完成情况</h3>
            </div>
            <div className="flex justify-between">
              <div className="text-center">
                <div className="text-2xl font-bold">{statistics.homeworkCompletion.completed}%</div>
                <div className="text-xs text-green-100">已完成</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{statistics.homeworkCompletion.inProgress}%</div>
                <div className="text-xs text-green-100">进行中</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{statistics.homeworkCompletion.notStarted}%</div>
                <div className="text-xs text-green-100">未开始</div>
              </div>
            </div>
          </div>
          
          <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow p-4 text-white">
            <div className="flex items-center mb-4">
              <div className="bg-white bg-opacity-30 p-2 rounded-lg mr-3">
                <FontAwesomeIcon icon={faFileAlt} className="text-lg" />
              </div>
              <h3 className="font-medium">考试成绩分布</h3>
            </div>
            <div className="flex justify-between">
              <div className="text-center">
                <div className="text-2xl font-bold">{statistics.examScores.excellent}%</div>
                <div className="text-xs text-purple-100">优秀</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{statistics.examScores.good}%</div>
                <div className="text-xs text-purple-100">良好</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{statistics.examScores.average}%</div>
                <div className="text-xs text-purple-100">一般</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{statistics.examScores.poor}%</div>
                <div className="text-xs text-purple-100">较差</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* 图表区域 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 成员活跃度图表 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="font-bold text-gray-800 mb-4 flex items-center">
            <FontAwesomeIcon icon={faChartPie} className="mr-2 text-indigo-500" />
            成员活跃度分布
          </h3>
          <div className="aspect-square relative p-4">
            {/* 这里在实际应用中应使用图表库，如Chart.js或Recharts */}
            <div className="flex justify-center items-center h-full">
              <div className="w-48 h-48 rounded-full border-8 border-blue-500 relative">
                <div 
                  className="absolute inset-0 bg-green-500 rounded-full" 
                  style={{
                    clipPath: `polygon(50% 50%, 50% 0%, ${50 + 50 * Math.cos(Math.PI * 2 * statistics.memberActivity.highActive / 18)}% ${50 - 50 * Math.sin(Math.PI * 2 * statistics.memberActivity.highActive / 18)}%)`
                  }}
                ></div>
                <div 
                  className="absolute inset-0 bg-yellow-500 rounded-full" 
                  style={{
                    clipPath: `polygon(50% 50%, ${50 + 50 * Math.cos(Math.PI * 2 * statistics.memberActivity.highActive / 18)}% ${50 - 50 * Math.sin(Math.PI * 2 * statistics.memberActivity.highActive / 18)}%, ${50 + 50 * Math.cos(Math.PI * 2 * (statistics.memberActivity.highActive + statistics.memberActivity.mediumActive) / 18)}% ${50 - 50 * Math.sin(Math.PI * 2 * (statistics.memberActivity.highActive + statistics.memberActivity.mediumActive) / 18)}%)`
                  }}
                ></div>
                <div 
                  className="absolute inset-0 bg-red-500 rounded-full" 
                  style={{
                    clipPath: `polygon(50% 50%, ${50 + 50 * Math.cos(Math.PI * 2 * (statistics.memberActivity.highActive + statistics.memberActivity.mediumActive) / 18)}% ${50 - 50 * Math.sin(Math.PI * 2 * (statistics.memberActivity.highActive + statistics.memberActivity.mediumActive) / 18)}%, ${50 + 50 * Math.cos(0)}% ${50 - 50 * Math.sin(0)}%)`
                  }}
                ></div>
                <div className="absolute inset-0 flex justify-center items-center">
                  <div className="text-center">
                    <div className="text-2xl font-bold">{statistics.memberActivity.highActive + statistics.memberActivity.mediumActive + statistics.memberActivity.lowActive}</div>
                    <div className="text-sm">总成员</div>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex justify-center mt-4 space-x-4">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-full mr-1"></div>
                <span className="text-xs">高活跃 ({statistics.memberActivity.highActive})</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-yellow-500 rounded-full mr-1"></div>
                <span className="text-xs">中活跃 ({statistics.memberActivity.mediumActive})</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-red-500 rounded-full mr-1"></div>
                <span className="text-xs">低活跃 ({statistics.memberActivity.lowActive})</span>
              </div>
            </div>
          </div>
        </div>
        
        {/* 作业完成情况图表 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="font-bold text-gray-800 mb-4 flex items-center">
            <FontAwesomeIcon icon={faChartBar} className="mr-2 text-green-500" />
            题目解答正确率
          </h3>
          <div className="h-64 flex items-end justify-between px-2">
            {statistics.problemSolving.data.map((value, index) => (
              <div key={index} className="flex flex-col items-center">
                <div 
                  className="w-16 bg-green-500" 
                  style={{ 
                    height: `${value}%`,
                    opacity: 0.6 + (0.4 * index / statistics.problemSolving.data.length)
                  }}
                ></div>
                <div className="text-xs mt-2">{statistics.problemSolving.labels[index]}</div>
                <div className="text-xs font-medium">{value}%</div>
              </div>
            ))}
          </div>
        </div>
        
        {/* 活动趋势图表 */}
        <div className="bg-white rounded-lg shadow p-6 md:col-span-2">
          <h3 className="font-bold text-gray-800 mb-4 flex items-center">
            <FontAwesomeIcon icon={faChartLine} className="mr-2 text-blue-500" />
            活动趋势
          </h3>
          <div className="h-64 relative">
            <div className="absolute inset-x-0 bottom-0 h-px bg-gray-200"></div>
            <div className="absolute inset-y-0 left-0 w-px bg-gray-200"></div>
            
            <div className="absolute inset-0 pt-4 pb-8 pl-8 pr-4">
              <svg className="w-full h-full">
                {/* 辅助线 */}
                {[0, 1, 2, 3].map((i) => (
                  <line 
                    key={i}
                    x1="0" 
                    y1={i * 25 + "%"} 
                    x2="100%" 
                    y2={i * 25 + "%"} 
                    stroke="#e5e7eb" 
                    strokeDasharray="2"
                  />
                ))}
                
                {/* 折线 */}
                <polyline
                  points={statistics.weeklyActivity.dates.map((_, index) => {
                    const x = (index / (statistics.weeklyActivity.dates.length - 1)) * 100;
                    const y = 100 - (statistics.weeklyActivity.counts[index] / Math.max(...statistics.weeklyActivity.counts)) * 100;
                    return `${x}%,${y}%`;
                  }).join(' ')}
                  fill="none"
                  stroke="#3b82f6"
                  strokeWidth="2"
                />
                
                {/* 数据点 */}
                {statistics.weeklyActivity.dates.map((_, index) => {
                  const x = (index / (statistics.weeklyActivity.dates.length - 1)) * 100;
                  const y = 100 - (statistics.weeklyActivity.counts[index] / Math.max(...statistics.weeklyActivity.counts)) * 100;
                  return (
                    <circle
                      key={index}
                      cx={`${x}%`}
                      cy={`${y}%`}
                      r="4"
                      fill="#3b82f6"
                    />
                  )
                })}
              </svg>
            </div>
            
            {/* X轴标签 */}
            <div className="absolute bottom-0 inset-x-0 flex justify-between px-8 -mb-6">
              {statistics.weeklyActivity.dates.map((date, index) => (
                <div key={index} className="text-xs text-gray-500">{date}</div>
              ))}
            </div>
            
            {/* Y轴标签 */}
            <div className="absolute left-0 inset-y-0 flex flex-col justify-between py-4 -ml-6">
              {[0, 20, 40, 60, 80].map((value, index) => (
                <div key={index} className="text-xs text-gray-500">{value}</div>
              ))}
            </div>
          </div>
          <div className="text-center text-xs text-gray-500 mt-8">
            时间段: {selectedPeriod === 'week' ? '本周' : selectedPeriod === 'month' ? '本月' : selectedPeriod === 'quarter' ? '本季度' : '本年度'}
          </div>
        </div>
      </div>
    </div>
  )
} 