'use client'

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faListAlt,
  faClock, 
  faCalendarCheck,
  faCheckDouble
} from '@fortawesome/free-solid-svg-icons'

interface HomeworkFilterProps {
  currentFilter: 'all' | 'ongoing' | 'ended' | 'graded'
  onFilterChange: (filter: 'all' | 'ongoing' | 'ended' | 'graded') => void
  homeworkCount: {
    all: number
    ongoing: number
    ended: number
    graded: number
  }
}

export default function HomeworkFilter({ 
  currentFilter, 
  onFilterChange,
  homeworkCount 
}: HomeworkFilterProps) {
  const filters = [
    { id: 'all', label: '全部作业', icon: faListAlt, count: homeworkCount.all },
    { id: 'ongoing', label: '进行中', icon: faClock, count: homeworkCount.ongoing },
    { id: 'ended', label: '已结束', icon: faCalendarCheck, count: homeworkCount.ended },
    { id: 'graded', label: '已批改', icon: faCheckDouble, count: homeworkCount.graded }
  ]

  return (
    <div className="bg-white rounded-xl shadow-sm mb-6 overflow-hidden">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4">
        {filters.map((filter) => (
          <button
            key={filter.id}
            onClick={() => onFilterChange(filter.id as any)}
            className={`py-4 px-6 flex items-center justify-center sm:justify-start transition ${
              currentFilter === filter.id
                ? 'bg-indigo-50 text-indigo-700 border-b-2 border-indigo-600'
                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800'
            }`}
          >
            <div className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                currentFilter === filter.id
                  ? 'bg-indigo-100'
                  : 'bg-gray-100'
              }`}>
                <FontAwesomeIcon 
                  icon={filter.icon} 
                  className={currentFilter === filter.id ? 'text-indigo-600' : 'text-gray-500'} 
                />
              </div>
              <div className="text-left">
                <div className="font-medium">{filter.label}</div>
                <div className="text-xs mt-0.5">
                  {filter.count}个作业
                </div>
              </div>
            </div>
          </button>
        ))}
      </div>
    </div>
  )
} 