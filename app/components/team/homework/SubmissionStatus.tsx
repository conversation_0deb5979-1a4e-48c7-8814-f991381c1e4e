'use client'

import Link from 'next/link'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faCheck, 
  faHourglassHalf,
  faUsers,
  faChartBar
} from '@fortawesome/free-solid-svg-icons'

interface HomeworkSubmission {
  id: string
  studentName: string
  studentId: string
  submitTime: string
  status: 'pending' | 'graded'
  score?: number
  feedback?: string
}

interface SubmissionStatusProps {
  submissions: HomeworkSubmission[]
  teamId: string
  homeworkId: string
}

export default function SubmissionStatus({ 
  submissions, 
  teamId, 
  homeworkId 
}: SubmissionStatusProps) {
  // 统计数据
  const totalSubmissions = submissions.length
  const gradedSubmissions = submissions.filter(s => s.status === 'graded').length
  const averageScore = submissions
    .filter(s => s.status === 'graded' && s.score !== undefined)
    .reduce((sum, s) => sum + (s.score || 0), 0) / gradedSubmissions || 0
  
  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden">
      <div className="bg-indigo-50 px-6 py-4 border-b border-indigo-100">
        <h2 className="text-lg font-semibold text-gray-800">提交状态</h2>
      </div>
      
      <div className="p-6">
        {/* 统计卡片 */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="bg-green-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-green-800 font-medium">已提交</div>
                <div className="text-2xl font-bold text-green-600">{totalSubmissions}</div>
              </div>
              <div className="bg-green-100 p-3 rounded-full">
                <FontAwesomeIcon icon={faCheck} className="text-green-600" />
              </div>
            </div>
          </div>
          
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-blue-800 font-medium">已批改</div>
                <div className="text-2xl font-bold text-blue-600">{gradedSubmissions}</div>
              </div>
              <div className="bg-blue-100 p-3 rounded-full">
                <FontAwesomeIcon icon={faHourglassHalf} className="text-blue-600" />
              </div>
            </div>
          </div>
          
          <div className="bg-indigo-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-indigo-800 font-medium">平均分</div>
                <div className="text-2xl font-bold text-indigo-600">
                  {averageScore.toFixed(1)}
                </div>
              </div>
              <div className="bg-indigo-100 p-3 rounded-full">
                <FontAwesomeIcon icon={faChartBar} className="text-indigo-600" />
              </div>
            </div>
          </div>
          
          <div className="bg-purple-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-purple-800 font-medium">完成率</div>
                <div className="text-2xl font-bold text-purple-600">
                  {((totalSubmissions / 18) * 100).toFixed(0)}%
                </div>
              </div>
              <div className="bg-purple-100 p-3 rounded-full">
                <FontAwesomeIcon icon={faUsers} className="text-purple-600" />
              </div>
            </div>
          </div>
        </div>
        
        {/* 最近提交 */}
        <h3 className="text-md font-medium text-gray-800 mb-3">最近提交</h3>
        <div className="space-y-3 mb-6">
          {submissions.map(submission => (
            <div key={submission.id} className="border border-gray-200 rounded-lg p-3 hover:border-indigo-300 transition">
              <div className="flex justify-between items-start">
                <div>
                  <div className="font-medium text-gray-800">{submission.studentName}</div>
                  <div className="text-xs text-gray-500">学号: {submission.studentId}</div>
                </div>
                {submission.status === 'graded' && submission.score !== undefined ? (
                  <div className="px-2 py-1 bg-green-100 text-green-800 rounded-lg text-sm font-medium">
                    {submission.score}分
                  </div>
                ) : (
                  <div className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-lg text-sm font-medium">
                    待批改
                  </div>
                )}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                提交时间: {new Date(submission.submitTime).toLocaleString()}
              </div>
              {submission.feedback && (
                <div className="text-xs text-gray-600 mt-2 p-2 bg-gray-50 rounded">
                  <span className="font-medium">反馈:</span> {submission.feedback}
                </div>
              )}
            </div>
          ))}
        </div>
        
        {/* 操作按钮 */}
        <div className="space-y-2">
          <Link 
            href={`/teams/${teamId}/homework/${homeworkId}/submissions`}
            className="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 flex items-center justify-center"
          >
            <FontAwesomeIcon icon={faUsers} className="mr-2" />
            查看全部提交
          </Link>
          
          <Link 
            href={`/teams/${teamId}/homework/${homeworkId}/stats`}
            className="w-full px-4 py-2 border border-indigo-600 text-indigo-600 rounded-lg hover:bg-indigo-50 flex items-center justify-center"
          >
            <FontAwesomeIcon icon={faChartBar} className="mr-2" />
            查看分析报告
          </Link>
        </div>
      </div>
    </div>
  )
} 