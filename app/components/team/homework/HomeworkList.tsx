'use client'

import Link from 'next/link'
import { TeamHomework } from '@/app/types/team'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faClock, 
  faCheck, 
  faCalendarTimes,
  faFileAlt,
  faChartBar,
  faUsers
} from '@fortawesome/free-solid-svg-icons'

interface HomeworkListProps {
  homeworks: TeamHomework[]
  isLoading: boolean
  teamId: string
}

export default function HomeworkList({ homeworks, isLoading, teamId }: HomeworkListProps) {
  if (isLoading) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-8 text-center">
        <div className="inline-block w-12 h-12 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
        <p className="mt-4 text-gray-500">正在加载作业...</p>
      </div>
    )
  }

  if (homeworks.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-8 text-center">
        <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <FontAwesomeIcon icon={faFileAlt} className="text-gray-400 text-2xl" />
        </div>
        <h3 className="text-lg font-medium text-gray-800 mb-2">没有找到作业</h3>
        <p className="text-gray-500">
          当前筛选条件下没有找到任何作业，请尝试更改筛选条件或创建新作业。
        </p>
        <button className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition">
          创建作业
        </button>
      </div>
    )
  }

  const getStatusBadge = (status: TeamHomework['status']) => {
    switch (status) {
      case 'ongoing':
        return (
          <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full inline-flex items-center">
            <FontAwesomeIcon icon={faClock} className="mr-1" />
            进行中
          </span>
        )
      case 'ended':
        return (
          <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full inline-flex items-center">
            <FontAwesomeIcon icon={faCalendarTimes} className="mr-1" />
            已结束
          </span>
        )
      case 'graded':
        return (
          <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full inline-flex items-center">
            <FontAwesomeIcon icon={faCheck} className="mr-1" />
            已批改
          </span>
        )
      default:
        return null
    }
  }

  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead>
            <tr className="bg-gray-50 border-b border-gray-200">
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                作业名称
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                截止日期
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                提交情况
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {homeworks.map((homework) => (
              <tr key={homework.id} className="hover:bg-gray-50 transition">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-start">
                    <div className="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <FontAwesomeIcon icon={faFileAlt} className="text-indigo-600" />
                    </div>
                    <div className="ml-3">
                      <div className="text-sm font-medium text-gray-900">{homework.title}</div>
                      <div className="text-sm text-gray-500 max-w-sm truncate">{homework.description}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getStatusBadge(homework.status)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{new Date(homework.dueDate).toLocaleDateString()}</div>
                  <div className="text-xs text-gray-500">
                    {
                      new Date(homework.dueDate) > new Date() 
                        ? `剩余${Math.ceil((new Date(homework.dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))}天`
                        : '已截止'
                    }
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="w-full bg-gray-200 rounded-full h-2.5 max-w-xs">
                      <div 
                        className="bg-indigo-600 h-2.5 rounded-full" 
                        style={{ width: `${(homework.submissionCount / homework.totalMembers) * 100}%` }}>
                      </div>
                    </div>
                    <span className="ml-2 text-xs text-gray-500">
                      {homework.submissionCount}/{homework.totalMembers}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  <div className="flex space-x-2">
                    <Link 
                      href={`/teams/${teamId}/homework/${homework.id}`}
                      className="text-indigo-600 hover:text-indigo-900 transition"
                    >
                      查看详情
                    </Link>
                    {homework.status === 'graded' && (
                      <Link 
                        href={`/teams/${teamId}/homework/${homework.id}/stats`}
                        className="text-green-600 hover:text-green-900 transition flex items-center"
                      >
                        <FontAwesomeIcon icon={faChartBar} className="mr-1" />
                        成绩统计
                      </Link>
                    )}
                    {homework.status === 'ongoing' && (
                      <Link 
                        href={`/teams/${teamId}/homework/${homework.id}/submissions`}
                        className="text-blue-600 hover:text-blue-900 transition flex items-center"
                      >
                        <FontAwesomeIcon icon={faUsers} className="mr-1" />
                        学生提交
                      </Link>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
} 