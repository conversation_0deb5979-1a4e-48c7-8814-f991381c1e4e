'use client'

import { TeamHomework } from '@/app/types/team'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faClock, 
  faCalendarAlt, 
  faCode,
  faFileUpload,
  faCheckCircle,
  faExclamationCircle,
  faInfoCircle
} from '@fortawesome/free-solid-svg-icons'
import ReactMarkdown from 'react-markdown'

interface HomeworkDetailProps {
  homework: TeamHomework
}

export default function HomeworkDetail({ homework }: HomeworkDetailProps) {
  const isExpired = new Date(homework.dueDate) < new Date()
  const daysLeft = Math.ceil((new Date(homework.dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
  
  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden">
      <div className="bg-indigo-50 px-6 py-4 border-b border-indigo-100">
        <h1 className="text-xl font-bold text-gray-800">{homework.title}</h1>
        <div className="flex items-center text-sm text-gray-600 mt-2">
          <div className="flex items-center mr-6">
            <FontAwesomeIcon icon={faCalendarAlt} className="mr-2 text-indigo-500" />
            截止日期: {new Date(homework.dueDate).toLocaleDateString()}
          </div>
          {!isExpired ? (
            <div className="flex items-center text-green-600">
              <FontAwesomeIcon icon={faClock} className="mr-2" />
              还剩 {daysLeft} 天
            </div>
          ) : (
            <div className="flex items-center text-red-600">
              <FontAwesomeIcon icon={faExclamationCircle} className="mr-2" />
              已截止
            </div>
          )}
        </div>
      </div>
      
      <div className="p-6">
        <div className="mb-8">
          <div className="prose max-w-none">
            <ReactMarkdown>{homework.description}</ReactMarkdown>
          </div>
        </div>
        
        <div className="border-t border-gray-200 pt-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">提交作业</h2>
          
          {isExpired ? (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-700 mb-4">
              <div className="flex items-center">
                <FontAwesomeIcon icon={faExclamationCircle} className="mr-2" />
                <span>作业已截止，无法提交</span>
              </div>
            </div>
          ) : (
            <>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-blue-700 mb-4">
                <div className="flex items-center">
                  <FontAwesomeIcon icon={faInfoCircle} className="mr-2" />
                  <span>请上传你的代码文件和报告</span>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">代码文件</label>
                  <div className="flex items-center justify-center p-6 border-2 border-gray-300 border-dashed rounded-lg hover:bg-gray-50 cursor-pointer">
                    <div className="space-y-1 text-center">
                      <div className="flex justify-center">
                        <FontAwesomeIcon icon={faCode} className="text-gray-400 text-3xl" />
                      </div>
                      <div className="text-sm text-gray-600">
                        <label htmlFor="code-file" className="relative cursor-pointer text-indigo-600 hover:text-indigo-700">
                          <span>上传代码文件</span>
                          <input id="code-file" name="code-file" type="file" className="sr-only" />
                        </label>
                        <p className="text-xs text-gray-500">支持 .cpp, .java, .py 等格式</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">报告文档</label>
                  <div className="flex items-center justify-center p-6 border-2 border-gray-300 border-dashed rounded-lg hover:bg-gray-50 cursor-pointer">
                    <div className="space-y-1 text-center">
                      <div className="flex justify-center">
                        <FontAwesomeIcon icon={faFileUpload} className="text-gray-400 text-3xl" />
                      </div>
                      <div className="text-sm text-gray-600">
                        <label htmlFor="report-file" className="relative cursor-pointer text-indigo-600 hover:text-indigo-700">
                          <span>上传报告文档</span>
                          <input id="report-file" name="report-file" type="file" className="sr-only" />
                        </label>
                        <p className="text-xs text-gray-500">支持 .pdf, .doc, .docx 格式</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition flex items-center">
                <FontAwesomeIcon icon={faCheckCircle} className="mr-2" />
                提交作业
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  )
} 