'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

const navigationItems = [
  { label: '总览', path: '' },
  { label: '作业', path: '/homework' },
  { label: '考试', path: '/exams' },
  { label: '题单', path: '/problem-sets' },
  { label: '成员', path: '/members' },
  { label: '题目库', path: '/problems' },
  { label: '数据统计', path: '/statistics' }
]

export default function TeamNavigation() {
  const pathname = usePathname()
  const teamBasePath = pathname.split('/').slice(0, 3).join('/')

  return (
    <div className="bg-white shadow-sm">
      <div className="container mx-auto px-4">
        <div className="flex overflow-x-auto whitespace-nowrap">
          {navigationItems.map((item) => {
            const isActive = pathname === `${teamBasePath}${item.path}`
            return (
              <Link
                key={item.path}
                href={`${teamBasePath}${item.path}`}
                className={`py-4 px-6 font-medium ${
                  isActive
                    ? 'text-indigo-600 border-b-2 border-indigo-600'
                    : 'text-gray-600 hover:text-indigo-600'
                }`}
              >
                {item.label}
              </Link>
            )
          })}
        </div>
      </div>
    </div>
  )
} 