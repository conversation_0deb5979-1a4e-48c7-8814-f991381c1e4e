import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faRocket, faEnvelope, faPhone,
} from '@fortawesome/free-solid-svg-icons';
import {
  faWeixin
} from '@fortawesome/free-brands-svg-icons';
import Image from "next/image";
import React from "react";

export default function Footer() {
  return (
    <footer className="bg-gray-800 text-gray-300 py-12">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <Image src="/logo2.png" alt="信竞星球" width={100} height={40} />
            </div>
            <p className="text-gray-400">
              培养编程思维，点亮编程未来
            </p>
          </div>
          <div>
            <h3 className="text-white text-lg font-semibold mb-4">快速链接</h3>
            <ul className="space-y-2">
              <li><Link href="/" className="text-gray-400 hover:text-white">首页</Link></li>
              <li><Link href="/basic-problems" className="text-gray-400 hover:text-white">基础题库</Link></li>
              <li><Link href="/coding-problems" className="text-gray-400 hover:text-white">编程题库</Link></li>
              <li><Link href="/exams" className="text-gray-400 hover:text-white">模拟考试</Link></li>
              <li><Link href="/news-list" className="text-gray-400 hover:text-white">编程资讯</Link></li>
            </ul>
          </div>
          <div>
            <h3 className="text-white text-lg font-semibold mb-4">资源中心</h3>
            <ul className="space-y-2">
              <li><Link href="#" className="text-gray-400 hover:text-white">编程教程</Link></li>
              <li><Link href="#" className="text-gray-400 hover:text-white">竞赛资讯</Link></li>
              <li><Link href="/faq" className="text-gray-400 hover:text-white">常见问题</Link></li>
              <li><Link href="/help-center" className="text-gray-400 hover:text-white">帮助中心</Link></li>
            </ul>
          </div>
          <div>
            <h3 className="text-white text-lg font-semibold mb-4">联系我们</h3>
            <ul className="space-y-2">
              <li className="flex items-center space-x-2">
                <FontAwesomeIcon icon={faEnvelope} className="text-gray-400" />
                <span><EMAIL></span>
              </li>
              <li className="flex items-center space-x-2">
                <FontAwesomeIcon icon={faPhone} className="text-gray-400" />
                <span>13423490835</span>
              </li>
              <li className="flex items-center space-x-2">
                <FontAwesomeIcon icon={faWeixin} className="text-gray-400" />
                <span>信竞星球</span>
              </li>
            </ul>
            <div className="flex space-x-2 mt-4">
              {/* 微信图标 */}
              <div className="relative group">
                <div className="text-gray-400 hover:text-white transition-colors">
                  <FontAwesomeIcon icon={faWeixin} className="w-6 h-6" />
                </div>
                <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 bg-white p-2 rounded-lg shadow-lg z-10 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 w-32">
                  <Image 
                    src="/qrcode_wechat.jpg" 
                    alt="微信二维码" 
                    width={120} 
                    height={120} 
                    className="rounded w-32"
                  />
                  <p className="text-xs text-gray-600 text-center mt-1">扫码关注微信公众号</p>
                </div>
              </div>
              
              {/* 小红书图标 */}
              <div className="relative group">
                <div className="text-gray-400 hover:text-white transition-colors">
                  <Image 
                    src="/xiaohongshu.svg" 
                    alt="小红书" 
                    width={24} 
                    height={24} 
                    className="hover:opacity-80 transition-opacity"
                  />
                </div>
                <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 bg-white p-2 rounded-lg shadow-lg z-10 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 w-32">
                  <Image 
                    src="/qrcode_xiaohongshu.jpg" 
                    alt="小红书二维码" 
                    width={120} 
                    height={120} 
                    className="rounded w-32"
                  />
                  <p className="text-xs text-gray-600 text-center mt-1">扫码关注小红书</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="border-t border-gray-700 mt-12 pt-6 text-center text-gray-400">
          <p>
            © 2025 信竞星球 版权所有
            <span className="mx-2">|</span>
            <a
              href="https://beian.miit.gov.cn/"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-white"
            >
              粤ICP备2024252237号-3
            </a>
          </p>
        </div>
      </div>
    </footer>
  );
}
