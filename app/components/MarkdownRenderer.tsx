'use client';

import React, { useMemo, useState, useRef, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus, vs } from 'react-syntax-highlighter/dist/cjs/styles/prism';
import 'katex/dist/katex.min.css';
import katex from 'katex';

// 创建全局图片缓存
const imageUrlCache = new Map<string, React.ReactNode>();

// 单独的代码块组件
interface CodeBlockProps {
  language: string;
  value: string;
  codeTheme: 'dark' | 'light';
}

const CodeBlock: React.FC<CodeBlockProps> = ({ language, value, codeTheme }) => {
  const [copyStatus, setCopyStatus] = useState<'ready' | 'copied'>('ready');

  const handleCopy = () => {
    navigator.clipboard.writeText(value);
    setCopyStatus('copied');

    setTimeout(() => {
      setCopyStatus('ready');
    }, 2000);
  };

  // 强制使用深色主题，即使参数是light
  const effectiveTheme = codeTheme; // 保持用户选择，不再强制使用深色
  const highlightStyle = effectiveTheme === 'light' ? vs : vscDarkPlus;

  // 深色主题下的样式
  const darkModeStyles = {
    backgroundColor: '#1e1e1e',
    color: '#d4d4d4',
    paddingTop: '16px',
    paddingLeft: '0px',
    paddingRight: '0px',
    paddingBottom: '0px',
    margin: '0',
    borderRadius: '0.375rem',
    fontSize: '0.875rem',
    overflow: 'hidden', // 防止内容溢出
  };

  // 浅色主题下的样式
  const lightModeStyles = {
    backgroundColor: '#f8f8f8',
    color: '#333',
    paddingTop: '2.2rem',
    paddingLeft: '1rem',
    paddingRight: '1rem',
    paddingBottom: '1rem',
    margin: '0',
    borderRadius: '0.375rem',
    fontSize: '0.875rem',
    overflow: 'hidden', // 防止内容溢出
  };

  // 根据主题选择样式
  const themeStyles = effectiveTheme === 'light' ? lightModeStyles : darkModeStyles;

  return (
    <div className="relative group code-block-wrapper my-4">
      {/* 语言标签 */}
      {language && (
        <div
          className={`absolute left-0 top-0 ${effectiveTheme === 'light' ? 'bg-gray-200 text-gray-700' : 'bg-gray-800 text-gray-200'} text-xs px-3 py-1 font-mono z-10 code-language-tag`}
          style={{
            borderRadius: '0.375rem 0 0.375rem 0'
          }}
        >
          {language}
        </div>
      )}

      {/* 复制按钮 */}
      <button
        className={`absolute right-0 top-0 ${effectiveTheme === 'light' ? 'bg-gray-200 hover:bg-gray-300 text-gray-700' : 'bg-gray-800 hover:bg-gray-700 text-gray-200'} text-xs px-3 py-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 font-medium z-10 code-copy-btn`}
        style={{
          borderRadius: '0 0.375rem 0 0.375rem'
        }}
        onClick={handleCopy}
      >
        {copyStatus === 'ready' ? '复制' : '已复制!'}
      </button>

      <SyntaxHighlighter
        style={highlightStyle}
        language={language || ''}
        PreTag="div"
        className="code-highlight-block"
        showLineNumbers={true}
        wrapLines={true}
        customStyle={themeStyles}
        lineNumberStyle={{
          minWidth: '2em',
          paddingRight: '0.7em',
          color: effectiveTheme === 'light' ? '#909090' : '#606060',
          textAlign: 'right',
          userSelect: 'none',
          borderRight: effectiveTheme === 'light' ? '1px solid #ddd' : '1px solid #444',
          marginRight: '0.7em',
        }}
      >
        {value}
      </SyntaxHighlighter>
    </div>
  );
};

// 单独的块级公式组件
interface MathBlockProps {
  content: string;
}

const MathBlock: React.FC<MathBlockProps> = ({ content }) => {
  const htmlContent = useMemo(() => {
    try {
      return katex.renderToString(content, {
        displayMode: true,
        throwOnError: false,
        output: "html",
        strict: "ignore"
      });
    } catch (e) {
      console.error('KaTeX render error:', e);
      return `<span class="text-red-500">KaTeX 渲染错误: ${e instanceof Error ? e.message : String(e)}</span>`;
    }
  }, [content]);

  return (
    <div 
      className="my-4 overflow-x-auto"
      dangerouslySetInnerHTML={{ __html: htmlContent }}
    />
  );
};

interface MarkdownRendererProps {
  content: string;
  className?: string;
  codeTheme?: 'dark' | 'light';
  format?: 'markdown' | 'html' | 'auto'; // 新增格式选项
}

// 添加 HTML 内容检测函数
const detectContentFormat = (content: string): 'markdown' | 'html' => {
  if (!content) return 'markdown';
  
  // 检测 HTML 标签的正则表达式
  const htmlTagRegex = /<\/?[a-z][\s\S]*>/i;
  const hasHtmlTags = htmlTagRegex.test(content);
  
  // 检测常见的 Markdown 语法
  const markdownPatterns = [
    /^#{1,6}\s+/m,           // 标题
    /^\*\s+/m,               // 无序列表
    /^\d+\.\s+/m,            // 有序列表
    /\*\*.*\*\*/,            // 粗体
    /\*.*\*/,                // 斜体
    /`.*`/,                  // 行内代码
    /```[\s\S]*?```/,        // 代码块
    /\[.*\]\(.*\)/,          // 链接
    /!\[.*\]\(.*\)/,         // 图片
  ];
  
  const hasMarkdownSyntax = markdownPatterns.some(pattern => pattern.test(content));
  
  // 如果有 HTML 标签且没有明显的 Markdown 语法，判断为 HTML
  if (hasHtmlTags && !hasMarkdownSyntax) {
    return 'html';
  }
  
  // 默认返回 markdown
  return 'markdown';
};

// 添加 HTML 渲染组件
interface HtmlRendererProps {
  content: string;
  className?: string;
  codeTheme?: 'dark' | 'light';
}

const HtmlRenderer: React.FC<HtmlRendererProps> = ({ content, className = '', codeTheme = 'dark' }) => {
  const processedHtml = useMemo(() => {
    if (!content) return '';
    
    // 基本的安全性处理：移除潜在的危险标签和属性
    let processed = content
      // 移除 script 标签
      .replace(/<script[\s\S]*?<\/script>/gi, '')
      // 移除 on* 事件属性
      .replace(/\s+on\w+\s*=\s*["'][^"']*["']/gi, '')
      // 移除 javascript: 协议
      .replace(/javascript:/gi, '');
    
    // 为 HTML 内容添加必要的样式类
    // 为代码块添加样式（支持更多的代码块格式）
    processed = processed.replace(
      /<pre(?:\s+class=["'][^"']*["'])?[^>]*><code([^>]*)>/gi, 
      (match, codeAttrs) => {
        const themeClass = codeTheme === 'light' ? 'bg-gray-100 text-gray-800' : 'bg-gray-800 text-gray-100';
        return `<pre class="${themeClass} p-4 rounded-lg overflow-x-auto my-4 font-mono text-sm"><code${codeAttrs}>`;
      }
    );
    
    // 为独立的 pre 标签添加样式
    processed = processed.replace(
      /<pre(?![^>]*<code)([^>]*)>/gi,
      (match, attrs) => {
        const themeClass = codeTheme === 'light' ? 'bg-gray-100 text-gray-800' : 'bg-gray-800 text-gray-100';
        return `<pre${attrs} class="${themeClass} p-4 rounded-lg overflow-x-auto my-4 font-mono text-sm">`;
      }
    );
    
    // 为行内代码添加样式
    processed = processed.replace(
      /<code(?![^>]*class=["'][^"']*(?:pre|language-))/gi,
      (match) => {
        const themeClass = codeTheme === 'light' ? 'bg-gray-100 text-gray-800' : 'bg-gray-100 text-gray-900';
        return `<code class="${themeClass} px-2 py-1 rounded text-sm font-mono">`;
      }
    );
    
    // 为表格添加样式
    processed = processed.replace(
      /<table([^>]*)>/gi,
      '<div class="overflow-x-auto my-4"><table$1 class="min-w-full border-collapse border border-gray-300">'
    );
    processed = processed.replace(/<\/table>/gi, '</table></div>');
    
    // 为表格元素添加样式
    processed = processed.replace(/<thead([^>]*)>/gi, '<thead$1 class="bg-gray-100">');
    processed = processed.replace(/<tr([^>]*)>/gi, '<tr$1 class="border-b border-gray-300">');
    processed = processed.replace(/<th([^>]*)>/gi, '<th$1 class="border border-gray-300 px-4 py-2 text-left font-semibold">');
    processed = processed.replace(/<td([^>]*)>/gi, '<td$1 class="border border-gray-300 px-4 py-2">');
    
    // 为其他元素添加基础样式（使用大小写不敏感匹配）
    processed = processed.replace(/<h1([^>]*)>/gi, '<h1$1 class="text-2xl font-bold my-4">');
    processed = processed.replace(/<h2([^>]*)>/gi, '<h2$1 class="text-xl font-bold my-3">');
    processed = processed.replace(/<h3([^>]*)>/gi, '<h3$1 class="text-lg font-bold my-2">');
    processed = processed.replace(/<h4([^>]*)>/gi, '<h4$1 class="text-base font-bold my-2">');
    processed = processed.replace(/<h5([^>]*)>/gi, '<h5$1 class="text-sm font-bold my-2">');
    processed = processed.replace(/<h6([^>]*)>/gi, '<h6$1 class="text-xs font-bold my-2">');
    processed = processed.replace(/<p([^>]*)>/gi, '<p$1 class="my-2">');
    processed = processed.replace(/<ul([^>]*)>/gi, '<ul$1 class="list-disc pl-6 my-2">');
    processed = processed.replace(/<ol([^>]*)>/gi, '<ol$1 class="list-decimal pl-6 my-2">');
    processed = processed.replace(/<li([^>]*)>/gi, '<li$1 class="my-1">');
    processed = processed.replace(/<a([^>]*)>/gi, '<a$1 class="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">');
    processed = processed.replace(/<blockquote([^>]*)>/gi, '<blockquote$1 class="border-l-4 border-gray-300 pl-4 py-1 my-2 text-gray-700 italic">');
    processed = processed.replace(/<img([^>]*)>/gi, '<img$1 class="max-w-full h-auto my-4 rounded" loading="lazy">');
    processed = processed.replace(/<strong([^>]*)>/gi, '<strong$1 class="font-bold">');
    processed = processed.replace(/<em([^>]*)>/gi, '<em$1 class="italic">');
    processed = processed.replace(/<hr([^>]*)>/gi, '<hr$1 class="border-gray-300 my-4">');
    
    return processed;
  }, [content, codeTheme]);
  
  return (
    <div 
      className={`html-content prose prose-sm max-w-none ${className}`}
      dangerouslySetInnerHTML={{ __html: processedHtml }}
    />
  );
};

/**
 * 渲染Markdown或HTML内容，支持LaTeX公式和代码高亮
 *
 * 使用示例:
 * ```tsx
 * // Markdown 内容
 * <MarkdownRenderer
 *   content={`
 *     # 标题
 *     这是一段包含 $E=mc^2$ 行内公式的文本。
 *   `}
 *   format="markdown" // 可选，支持 'markdown', 'html', 'auto'
 *   codeTheme="dark"
 * />
 * 
 * // HTML 内容
 * <MarkdownRenderer
 *   content={`
 *     <h1>标题</h1>
 *     <p>这是HTML内容</p>
 *     <pre><code>console.log('Hello');</code></pre>
 *   `}
 *   format="html"
 * />
 * 
 * // 自动检测格式
 * <MarkdownRenderer
 *   content={content}
 *   format="auto" // 自动检测内容格式
 * />
 * ```
 *
 * LaTeX公式支持 (仅在 Markdown 模式下):
 * - 行内公式: $公式内容$
 * - 块级公式: $$公式内容$$
 */
const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  className = '',
  codeTheme = 'dark',
  format = 'auto'
}) => {
  // 确定最终使用的格式
  const finalFormat = useMemo(() => {
    if (format === 'auto') {
      return detectContentFormat(content);
    }
    return format;
  }, [content, format]);

  // 如果是 HTML 格式，使用 HTML 渲染器
  if (finalFormat === 'html') {
    return (
      <HtmlRenderer 
        content={content} 
        className={className}
        codeTheme={codeTheme}
      />
    );
  }

  // 原有的 Markdown 渲染逻辑
  // 预处理 Markdown 内容
  const { processedContent, mathBlocks } = useMemo(() => {
    if (!content) return { processedContent: '', mathBlocks: [] };

    // 替换块级公式为占位符，同时收集块级公式
    const mathBlocks: string[] = [];
    const blockPlaceholder = (index: number) => `MATH_BLOCK_PLACEHOLDER_${index}`;
    
    // 首先提取代码块，防止它们内部的 $$ 被识别为公式
    const codeBlocks: string[] = [];
    const codePlaceholder = (index: number) => `CODE_BLOCK_PLACEHOLDER_${index}`;
    
    let tempContent = content.replace(/```[\s\S]*?```/g, (match) => {
      const index = codeBlocks.length;
      codeBlocks.push(match);
      return codePlaceholder(index);
    });
    
    // 现在替换块级公式
    tempContent = tempContent.replace(/\$\$([\s\S]*?)\$\$/g, (match, formula) => {
      const index = mathBlocks.length;
      mathBlocks.push(formula.trim());
      return blockPlaceholder(index);
    });
    
    // 恢复代码块
    tempContent = tempContent.replace(/CODE_BLOCK_PLACEHOLDER_(\d+)/g, (_, index) => {
      return codeBlocks[parseInt(index)];
    });
    
    return {
      processedContent: tempContent,
      mathBlocks
    };
  }, [content]);

  return (
    <div className={`markdown-body ${className}`}>
      <ReactMarkdown
        remarkPlugins={[
          [remarkMath, { singleDollarTextMath: true }], // 仅处理行内公式
          remarkGfm
        ]}
        rehypePlugins={[
          [rehypeKatex, { throwOnError: false, strict: "ignore" }]
        ]}
        components={{
          // 自定义渲染组件
          h1: ({ children, ...props }) => <h1 className="text-2xl font-bold my-4" {...props}>{children}</h1>,
          h2: ({ children, ...props }) => <h2 className="text-xl font-bold my-3" {...props}>{children}</h2>,
          h3: ({ children, ...props }) => <h3 className="text-lg font-bold my-2" {...props}>{children}</h3>,
          p: ({ children, node, ...props }) => {
            // 检查是否是数学块占位符
            if (
              node?.children?.length === 1 &&
              node.children[0].type === 'text' &&
              typeof node.children[0].value === 'string' &&
              node.children[0].value.startsWith('MATH_BLOCK_PLACEHOLDER_')
            ) {
              const placeholderText = node.children[0].value;
              const match = /MATH_BLOCK_PLACEHOLDER_(\d+)/.exec(placeholderText);
              if (match) {
                const index = parseInt(match[1]);
                if (index < mathBlocks.length) {
                  return <MathBlock content={mathBlocks[index]} />;
                }
              }
            }
            return <p className="my-2" {...props}>{children}</p>;
          },
          ul: ({ children, ...props }) => <ul className="list-disc pl-6 my-2" {...props}>{children}</ul>,
          ol: ({ children, ...props }) => <ol className="list-decimal pl-6 my-2" {...props}>{children}</ol>,
          li: ({ children, ...props }) => <li className="my-1" {...props}>{children}</li>,
          a: ({ children, ...props }) => (
            <a className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer" {...props}>
              {children}
            </a>
          ),
          blockquote: ({ children, ...props }) => (
            <blockquote className="border-l-4 border-gray-300 pl-4 py-1 my-2 text-gray-700" {...props}>
              {children}
            </blockquote>
          ),
          code: ({ inline, className, children, ...props }: any) => {
            const match = /language-(\w+)/.exec(className || '');
            // 通过检查className是否包含language-前缀来判断是否为代码块
            // 或者通过检查父元素标签是否为pre来判断
            const isCodeBlock = Boolean(match) || props.node?.parentNode?.tagName === 'pre';

            const inlineCodeClass = codeTheme === 'light'
              ? "bg-gray-100 text-gray-800"
              : "bg-gray-100 text-gray-900";

            return !isCodeBlock ? (
              <code className={`${inlineCodeClass} px-1 py-0.5 rounded text-sm inline`} {...props}>
                {children}
              </code>
            ) : (
              <CodeBlock
                language={(match && match[1]) || ''}
                value={String(children).replace(/\n$/, '')}
                codeTheme={codeTheme}
              />
            );
          },
          table: ({ children, ...props }) => (
            <div className="overflow-x-auto my-4">
              <table className="min-w-full border-collapse border border-gray-300" {...props}>
                {children}
              </table>
            </div>
          ),
          thead: ({ children, ...props }) => <thead className="bg-gray-100" {...props}>{children}</thead>,
          tbody: ({ children, ...props }) => <tbody {...props}>{children}</tbody>,
          tr: ({ children, ...props }) => <tr className="border-b border-gray-300" {...props}>{children}</tr>,
          th: ({ children, ...props }) => (
            <th className="border border-gray-300 px-4 py-2 text-left font-semibold" {...props}>
              {children}
            </th>
          ),
          td: ({ children, ...props }) => <td className="border border-gray-300 px-4 py-2" {...props}>{children}</td>,
          img: ({ ...props }) => {
            // 对图片URL进行规范化处理
            const srcUrl = props.src || '';
            
            // 检查图片是否已缓存，如果缓存了直接返回
            if (imageUrlCache.has(srcUrl)) {
              return imageUrlCache.get(srcUrl);
            }
            
            // 如果没有缓存，创建新的图片组件并缓存它
            const imageComponent = (
              <img 
                className="max-w-full h-auto my-4 rounded" 
                {...props} 
                src={srcUrl}
                alt={props.alt || ''} 
                loading="lazy"
                decoding="async"
                style={{ 
                  maxHeight: '500px', 
                  objectFit: 'contain',
                  opacity: 1,
                  transition: 'opacity 0.3s'
                }}
                onError={(e) => {
                  // 图片加载错误处理
                  console.error('Image failed to load:', srcUrl);
                  e.currentTarget.style.display = 'none';
                }}
              />
            );
            
            // 将组件存入缓存
            imageUrlCache.set(srcUrl, imageComponent);
            
            return imageComponent;
          },
        }}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  );
};

// 使用React.memo包装组件以避免不必要的重新渲染
export default React.memo(MarkdownRenderer, (prevProps, nextProps) => {
  // 只有当content、codeTheme、format或className改变时才重新渲染
  return prevProps.content === nextProps.content && 
         prevProps.codeTheme === nextProps.codeTheme &&
         prevProps.className === nextProps.className &&
         prevProps.format === nextProps.format;
});
