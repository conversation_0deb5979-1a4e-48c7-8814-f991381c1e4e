"use client";

import { useEffect } from 'react';

interface MockInitializerProps {
  onInitialized?: () => void;
  onError?: () => void;
}

export default function MockInitializer({ onInitialized, onError }: MockInitializerProps) {
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // 动态导入，避免服务器端导入错误
      import('../utils/mockService').then(({ initMockService }) => {
        initMockService()
          .then(() => {
            console.log('Mock service initialized');
            onInitialized?.();
          })
          .catch((err) => {
            console.error('Failed to initialize mock service:', err);
            onError?.();
          });
      }).catch(err => {
        console.error('Failed to import mock service:', err);
        onError?.();
      });
    }
  }, [onInitialized, onError]);

  return null;
} 