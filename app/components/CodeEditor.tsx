"use client";

import { useEffect, useRef, memo, useState } from 'react';
import { EditorState, Transaction } from '@codemirror/state';
import { EditorView, keymap, lineNumbers, highlightActiveLine } from '@codemirror/view';
import { defaultKeymap, history, historyKeymap, indentWithTab } from '@codemirror/commands';
import { syntaxHighlighting, defaultHighlightStyle } from '@codemirror/language';
import { javascript } from '@codemirror/lang-javascript';
import { python } from '@codemirror/lang-python';
import { java } from '@codemirror/lang-java';
import { cpp } from '@codemirror/lang-cpp';
import { oneDark } from '@codemirror/theme-one-dark';
import { Extension } from '@codemirror/state';
import { autocompletion, completionKeymap, CompletionContext, CompletionSource, closeBrackets } from '@codemirror/autocomplete';
import { bracketMatching } from "@codemirror/language";
import CommonDropdown, { DropdownOption } from './CommonDropdown';

interface CodeEditorProps {
  code: string;
  language: string;
  onChange: (value: string) => void;
  onLanguageChange?: (language: string) => void;
  className?: string;
  darkMode?: boolean;
  showLanguageSelect?: boolean;
}

// 本地存储键名常量
const LANGUAGE_STORAGE_KEY = 'preferred_code_language';

function CodeEditor({
  code,
  language: propLanguage,
  onChange,
  onLanguageChange,
  className = '',
  darkMode = false,
  showLanguageSelect = false
}: CodeEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const editorViewRef = useRef<EditorView | null>(null);
  const initialCodeRef = useRef(code);
  const isInitializedRef = useRef(false);
  const onChangeRef = useRef(onChange);
  const [completionActive, setCompletionActive] = useState(false);
  // 添加语言状态，使用props或localStorage中的值，默认为python3
  const [language, setLanguage] = useState<string>('python3');

  // 初始化时从localStorage读取保存的语言
  useEffect(() => {
    // 客户端环境检查
    if (typeof window !== 'undefined') {
      // 尝试从localStorage读取语言设置
      const savedLanguage = localStorage.getItem(LANGUAGE_STORAGE_KEY);
      // 如果存在保存的语言设置，则使用它；否则使用props传入的语言或默认的python3
      const initialLanguage = savedLanguage || propLanguage || 'python3';
      setLanguage(initialLanguage);

      // 如果设置了onLanguageChange回调，通知父组件
      if (onLanguageChange && initialLanguage !== propLanguage) {
        onLanguageChange(initialLanguage);
      }
    }
  }, [propLanguage, onLanguageChange]);

  // 当props中的language变更时同步状态
  useEffect(() => {
    if (propLanguage && propLanguage !== language) {
      setLanguage(propLanguage);
    }
  }, [propLanguage, language]);

  // 更新onChange引用
  useEffect(() => {
    onChangeRef.current = onChange;
  }, [onChange]);

  // 获取语言支持
  const getLanguageSupport = (): Extension => {
    switch (language.toLowerCase()) {
      case 'python':
      case 'python3':
        return python();
      case 'cpp':
      case 'c++':
        return cpp();
      case 'java':
        return java();
      case 'javascript':
      case 'js':
        return javascript();
      default:
        return python();
    }
  };

  // 选项选择处理
  const handleLanguageChange = (value: string | null) => {
    // 如果值为null（用户点击已选项），仍保持原语言不变
    const newLanguage = value || language;

    // 只有当语言确实变化时才更新
    if (newLanguage !== language) {
      setLanguage(newLanguage);

      // 保存到localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem(LANGUAGE_STORAGE_KEY, newLanguage);
      }

      if (onLanguageChange) {
        onLanguageChange(newLanguage);
      }
    }
  };

  // 语言选项
  const languageOptions: DropdownOption<string>[] = [
    { label: 'Python3', value: 'Python3' },
    { label: 'C++', value: 'C++' }
  ];

  // 初始化编辑器
  useEffect(() => {
    if (!editorRef.current) return;

    // 销毁现有编辑器
    if (editorViewRef.current && isInitializedRef.current) {
      editorViewRef.current.destroy();
    }

    // 获取语言支持
    const languageSupport = getLanguageSupport();

    // 增强的自动补全配置
    const completionExt = autocompletion({
      activateOnTyping: true,    // 输入时自动激活
      icons: true,               // 显示图标
      defaultKeymap: true,       // 使用默认快捷键
      maxRenderedOptions: 100    // 最大显示选项数
    });

    // 使用CodeMirror自带的括号配对功能
    const bracketClosingExt = closeBrackets();

    // 创建编辑器状态
    const startState = EditorState.create({
      doc: initialCodeRef.current,
      extensions: [
        lineNumbers(),
        history(),
        keymap.of([...defaultKeymap, ...historyKeymap, ...completionKeymap, indentWithTab]),
        syntaxHighlighting(defaultHighlightStyle),
        highlightActiveLine(),
        completionExt,          // 自定义自动补全配置
        bracketMatching(),      // 括号匹配高亮
        bracketClosingExt,      // 使用官方提供的括号自动配对功能
        EditorView.updateListener.of(update => {
          if (update.changes) {
            onChangeRef.current(update.state.doc.toString());
          }
        }),
        languageSupport,
        ...(darkMode ? [oneDark] : []),
        EditorView.lineWrapping,
        // 添加UI优化
        EditorView.theme({
          ".cm-tooltip.cm-tooltip-autocomplete": {
            "& > ul > li": {
              padding: "4px 8px",
              lineHeight: 1.5
            }
          },
          ".cm-tooltip-autocomplete-info": {
            padding: "4px 8px",
            fontSize: "12px",
            color: "#666",
            borderTop: "1px solid #ddd"
          },
          ".cm-matchingBracket": {
            backgroundColor: "#def0ff",
            color: "#236cada1",
            border: "1px solid #8bb5dca1"
          }
        })
      ]
    });

    // 创建编辑器视图
    const view = new EditorView({
      state: startState,
      parent: editorRef.current
    });

    editorViewRef.current = view;
    isInitializedRef.current = true;

    // 日志记录自动补全状态
    console.log("CodeMirror editor initialized with autocompletion");

    return () => {
      if (editorViewRef.current) {
        editorViewRef.current.destroy();
      }
    };
  }, [language, darkMode]);

  // 更新编辑器内容
  useEffect(() => {
    if (editorViewRef.current && isInitializedRef.current) {
      const currentContent = editorViewRef.current.state.doc.toString();
      if (code !== currentContent) {
        editorViewRef.current.dispatch({
          changes: {
            from: 0,
            to: currentContent.length,
            insert: code
          }
        });
      }
    }
  }, [code]);

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {showLanguageSelect && (
        <div className="bg-gray-50 border-b border-gray-200 p-2 flex justify-between items-center">
          <CommonDropdown<string | null>
            label="语言"
            options={languageOptions}
            value={language}
            onChange={handleLanguageChange}
            placeholder="选择语言"
            className="w-40 h-8 text-sm"
            canClear={false}
          />
        </div>
      )}
      <div
        ref={editorRef}
        className="flex-1 overflow-auto"
        style={{ minHeight: '80%' }}
      />
    </div>
  );
}

export default memo(CodeEditor);
