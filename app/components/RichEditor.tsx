'use client'

import { useState, useEffect } from 'react'
import '@wangeditor/editor/dist/css/style.css'
import { Editor, Toolbar } from '@wangeditor/editor-for-react'
import { IDomEditor, IEditorConfig, IToolbarConfig } from '@wangeditor/editor'

interface RichEditorProps {
  value: string
  onChange: (html: string) => void
  placeholder?: string
  height?: number
  toolbarKeys?: string[]
}

export default function RichEditor({
  value,
  onChange,
  placeholder = '请输入内容...',
  height = 300,
  toolbarKeys = ['bold', 'italic', 'underline', 'through', '|', 'color', 'bgColor', '|',
                'fontSize', 'fontFamily', 'indent', 'lineHeight', 'alignment', '|',
                'bulletedList', 'numberedList', '|', 'emotion', 'clearStyle', '|', 'undo', 'redo']
}: RichEditorProps) {
  // 编辑器实例
  const [editor, setEditor] = useState<IDomEditor | null>(null)
  
  // 组件卸载时销毁编辑器
  useEffect(() => {
    return () => {
      if (editor == null) return
      editor.destroy()
      setEditor(null)
    }
  }, [editor])
  
  // 编辑器配置
  const editorConfig: Partial<IEditorConfig> = {
    placeholder,
    autoFocus: false,
    MENU_CONF: {}
  }
  
  // 工具栏配置
  const toolbarConfig: Partial<IToolbarConfig> = {
    toolbarKeys
  }
  
  // 富文本内容变化处理
  const handleChange = (editor: IDomEditor) => {
    onChange(editor.getHtml())
  }
  
  return (
    <div className="border border-gray-300 rounded-lg">
      <div className="border-b border-gray-300">
        <Toolbar
          editor={editor}
          defaultConfig={toolbarConfig}
          mode="default"
          style={{ borderBottom: '1px solid #e2e8f0' }}
        />
      </div>
      <Editor
        defaultConfig={editorConfig}
        value={value}
        onCreated={setEditor}
        onChange={handleChange}
        mode="default"
        style={{ height: `${height}px`, overflowY: 'auto' }}
      />
    </div>
  )
} 