'use client'

import { forwardRef } from 'react'

interface TextEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  height?: string | number
  className?: string
}

const TextEditor = forwardRef<HTMLTextAreaElement, TextEditorProps>(({
  value,
  onChange,
  placeholder = '请输入内容...',
  height = 200,
  className = ''
}, ref) => {
  return (
    <div className="border border-gray-300 rounded-lg">
      <textarea
        ref={ref}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        className={`w-full px-4 py-2 outline-none focus:ring-2 focus:ring-indigo-500 rounded-lg ${className}`}
        style={{ 
          height,
          minHeight: typeof height === 'number' ? `${height}px` : height,
          resize: 'vertical'
        }}
      />
    </div>
  )
})

TextEditor.displayName = 'TextEditor'

export default TextEditor 