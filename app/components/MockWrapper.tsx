"use client";

import { useEffect, useState } from 'react';
// 修复导入路径
import { MockService } from '../../lib/mockService';

export default function MockWrapper() {
  const [mockStatus, setMockStatus] = useState<'initializing' | 'success' | 'error' | 'idle'>('idle');

  useEffect(() => {
    // 只在客户端和开发环境中运行
    if (typeof window === 'undefined' || process.env.NODE_ENV !== 'development') {
      return;
    }

    const setupMockService = async () => {
      try {
        setMockStatus('initializing');
        console.log('开始初始化 Mock 服务...');

        // 初始化MockService
        // 从API路由文件中导入的代码会自动注册所有模拟数据
        await Promise.all([
        ]);

        // 确认是否初始化成功
        const mockService = MockService.getInstance();
        if (mockService) {
          setMockStatus('success');
          console.log('Mock 服务初始化成功！');
        } else {
          throw new Error('MockService实例创建失败');
        }
      } catch (error) {
        setMockStatus('error');
        console.error('Mock 服务初始化失败:', error);
      }
    };

    setupMockService();
  }, []);

  // 在开发环境中，显示状态
  if (process.env.NODE_ENV === 'development') {
    if (mockStatus === 'error') {
      return (
        <div className="fixed bottom-4 right-4 bg-red-500 text-white px-4 py-2 rounded-md shadow-lg z-50">
          <p className="text-sm font-medium">Mock 服务初始化失败，API 请求可能无法正常工作</p>
        </div>
      );
    }

    if (mockStatus === 'success') {
      return (
        <div className="fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg z-50 opacity-80 hover:opacity-100 transition-opacity duration-300">
          <p className="text-sm font-medium">Mock 服务运行中</p>
        </div>
      );
    }
  }

  return null;
}
