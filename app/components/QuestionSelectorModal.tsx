import { Search, Plus, ChevronDown, X as XIcon } from 'lucide-react'
import { useState, useEffect } from 'react'
import CommonDropdown, { DropdownOption } from './CommonDropdown'
import TagSelector from './TagSelector'
import { Tag, TagGroup } from '@/app/service/tag-service'
import Pagination from './Pagination'
import BaseProblemService, { Problem as BaseProblem } from '@/app/service/base-problem-service'
import CodingProblemService, { Problem as CodingProblem } from '@/app/service/coding-problem-service'
import TagService from '@/app/service/tag-service'

interface Question {
  id: string
  title: string
  type: 'single' | 'multiple' | 'judge' | 'programming'
  difficulty: 'easy' | 'medium' | 'hard'
  defaultPoints: number
  points: number
  source: '公共题库' | '团队题库'
  options?: Array<{
    label: string
    content: string
    isCorrect: boolean
  }>
  description?: string
  inputExample?: string
  outputExample?: string
  tags?: string[]
}

type QuestionType = 'single' | 'multiple' | 'judge' | 'programming'

interface QuestionSelectorModalProps {
  show: boolean
  type: 'basic' | 'programming'
  questions: Omit<Question, 'points'>[]
  selectedQuestions: Question[]
  onClose: () => void
  onAddQuestion: (question: Omit<Question, 'points'>) => void
}

export default function QuestionSelectorModal({
  show,
  type,
  questions: initialQuestions,
  selectedQuestions,
  onClose,
  onAddQuestion
}: QuestionSelectorModalProps) {
  // 状态管理
  const [sourceFilter, setSourceFilter] = useState<string | null>(null)
  const [questionTypeFilter, setQuestionTypeFilter] = useState<string | null>(null)
  const [difficultyFilter, setDifficultyFilter] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedTags, setSelectedTags] = useState<Tag[]>([])
  const [showTagSelector, setShowTagSelector] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const pageSize = 10

  // API 数据状态
  const [questions, setQuestions] = useState<Omit<Question, 'points'>[]>(initialQuestions)
  const [tagGroups, setTagGroups] = useState<TagGroup[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const [totalItems, setTotalItems] = useState(0)

  // 禁用背景滚动
  useEffect(() => {
    if (show) {
      // 保存当前滚动位置，并禁用滚动
      const scrollY = window.scrollY
      document.body.style.position = 'fixed'
      document.body.style.top = `-${scrollY}px`
      document.body.style.width = '100%'
      document.body.style.overflowY = 'scroll'
    } else {
      // 恢复滚动
      const scrollY = document.body.style.top
      document.body.style.position = ''
      document.body.style.top = ''
      document.body.style.width = ''
      document.body.style.overflowY = ''
      if (scrollY) {
        window.scrollTo(0, parseInt(scrollY || '0', 10) * -1)
      }
    }

    return () => {
      // 组件卸载时确保恢复滚动
      document.body.style.position = ''
      document.body.style.top = ''
      document.body.style.width = ''
      document.body.style.overflowY = ''
    }
  }, [show])

  // 加载标签数据
  useEffect(() => {
    const fetchTags = async () => {
      try {
        // 根据题目类型选择不同的标签类型 (1: 基础题, 2: 编程题)
        const tagType = type === 'basic' ? 1 : 2
        const response = await TagService.getTagList(tagType)
        setTagGroups(response.data)
      } catch (error) {
        console.error('获取标签数据失败:', error)
        setErrorMessage('获取标签数据失败，请刷新重试')
      }
    }

    if (show) {
      fetchTags()
    }
  }, [show, type])

  // 加载题目数据
  useEffect(() => {
    const fetchQuestions = async () => {
      if (!show) return
      
      setIsLoading(true)
      setErrorMessage('')
      
      try {
        const params = {
          pageNum: currentPage,
          pageSize: pageSize,
          keyword: searchQuery || undefined,
          categoryId: undefined,
          tagIds: selectedTags.length > 0 ? selectedTags.map(tag => tag.id) : undefined,
          difficulty: difficultyFilter || undefined,
          type: questionTypeFilter || undefined,
          source: sourceFilter || undefined
        }

        let response
        let formattedQuestions: Omit<Question, 'points'>[] = []

        if (type === 'basic') {
          response = await BaseProblemService.getProblemList(params)
          
          // 基础题列表转换
          formattedQuestions = response.data.items.map((problem: BaseProblem) => ({
            id: problem.id.toString(),
            title: problem.question,
            type: mapQuestionType(problem.type),
            difficulty: mapDifficulty(problem.difficulty),
            defaultPoints: 5,
            source: '公共题库',
            description: problem.description,
            tags: problem.tagViews.map(tag => tag.name)
          }))
          
        } else {
          response = await CodingProblemService.getProblemList(params)
          
          // 编程题列表转换
          formattedQuestions = response.data.items.map((problem: CodingProblem) => ({
            id: problem.problemId,
            title: problem.title,
            type: 'programming',
            difficulty: mapDifficulty(problem.difficulty),
            defaultPoints: 10,
            source: '公共题库',
            description: problem.description,
            tags: problem.tags.map(tag => tag.name)
          }))
        }
        
        setQuestions(formattedQuestions)
        setTotalItems(response.data.total)
        
      } catch (error) {
        console.error('获取题目数据失败:', error)
        setErrorMessage('获取题目数据失败，请刷新重试')
      } finally {
        setIsLoading(false)
      }
    }

    fetchQuestions()
  }, [show, type, currentPage, sourceFilter, questionTypeFilter, difficultyFilter, searchQuery, selectedTags])

  // 类型映射函数
  const mapQuestionType = (typeCode: number): QuestionType => {
    switch (typeCode) {
      case 1: return 'single'
      case 2: return 'multiple'
      case 3: return 'judge'
      default: return 'single'
    }
  }

  // 难度映射函数
  const mapDifficulty = (difficultyCode: number): 'easy' | 'medium' | 'hard' => {
    switch (difficultyCode) {
      case 1: return 'easy'
      case 2: return 'medium'
      case 3: return 'hard'
      default: return 'medium'
    }
  }

  if (!show) {
    return null
  }

  // 题库选项
  const sourceOptions: DropdownOption[] = [
    { label: '全部题库', value: null },
    { label: '公共题库', value: 'public' },
    { label: '团队题库', value: 'team' }
  ]

  // 题型选项
  const questionTypeOptions: DropdownOption[] = [
    { label: '全部类型', value: null },
    { label: '单选题', value: '1' },
    { label: '多选题', value: '2' },
    { label: '判断题', value: '3' }
  ]

  // 难度选项
  const difficultyOptions: DropdownOption[] = [
    { label: '全部难度', value: null },
    { label: '简单', value: '1' },
    { label: '中等', value: '2' },
    { label: '困难', value: '3' }
  ]

  // 过滤题目逻辑现在由 API 处理
  const filteredQuestions = questions

  // 分页也不需要在前端处理了，因为 API 已经返回分页结果
  const paginatedQuestions = filteredQuestions

  // 题目类型展示
  const getQuestionTypeText = (type: QuestionType) => {
    switch (type) {
      case 'single': return '单选题'
      case 'multiple': return '多选题'
      case 'judge': return '判断题'
      case 'programming': return '编程题'
      default: return ''
    }
  }
  
  // 难度展示样式
  const getDifficultyBadge = (difficulty: 'easy' | 'medium' | 'hard') => {
    const colors = {
      easy: 'bg-green-100 text-green-800',
      medium: 'bg-yellow-100 text-yellow-800',
      hard: 'bg-red-100 text-red-800'
    }
    
    const labels = {
      easy: '简单',
      medium: '中等',
      hard: '困难'
    }
    
    return (
      <span className={`text-xs px-2 py-1 rounded-full ${colors[difficulty]}`}>
        {labels[difficulty]}
      </span>
    )
  }

  // 处理标签选择
  const handleTagsChange = (tags: Tag[]) => {
    setSelectedTags(tags)
    setShowTagSelector(false)
    setCurrentPage(1) // 切换标签时重置为第一页
  }

  // 处理下拉框变更
  const handleSourceChange = (value: string | number | null) => {
    setSourceFilter(value as string | null)
    setCurrentPage(1) // 重置为第一页
  }

  const handleTypeChange = (value: string | number | null) => {
    setQuestionTypeFilter(value as string | null)
    setCurrentPage(1) // 重置为第一页
  }

  const handleDifficultyChange = (value: string | number | null) => {
    setDifficultyFilter(value as string | null)
    setCurrentPage(1) // 重置为第一页
  }

  // 处理搜索
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
    setCurrentPage(1) // 重置为第一页
  }

  // 处理页码变更
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  // 检查题目是否已添加
  const isQuestionSelected = (questionId: string) => {
    return selectedQuestions.some(q => q.id === questionId)
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="relative max-w-4xl w-full bg-white rounded-xl shadow-2xl p-6 max-h-[90vh] overflow-auto">
        <div className="flex justify-between items-center border-b border-gray-200 pb-4 mb-5">
          <h3 className="text-xl font-medium text-gray-800">
            {type === 'basic' ? '选择基础题' : '选择编程题'}
          </h3>
          <button
            type="button"
            className="text-gray-500 hover:text-gray-700 transition-colors"
            onClick={onClose}
          >
            <XIcon className="h-6 w-6" />
          </button>
        </div>
        
        <div className="space-y-5">
          {/* 筛选条件 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1.5">题库</label>
              <CommonDropdown
                label="题库"
                options={sourceOptions}
                value={sourceFilter}
                onChange={handleSourceChange}
                placeholder="全部题库"
                className="w-full"
              />
            </div>
            
            {type === 'basic' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1.5">题目类型</label>
                <CommonDropdown
                  label="题目类型"
                  options={questionTypeOptions}
                  value={questionTypeFilter}
                  onChange={handleTypeChange}
                  placeholder="全部类型"
                  className="w-full"
                />
              </div>
            )}
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1.5">难度</label>
              <CommonDropdown
                label="难度"
                options={difficultyOptions}
                value={difficultyFilter}
                onChange={handleDifficultyChange}
                placeholder="全部难度"
                className="w-full"
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="col-span-2 relative">
              <label className="block text-sm font-medium text-gray-700 mb-1.5">搜索</label>
              <div className="relative">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <input
                  placeholder="搜索题目ID或标题"
                  className="w-full pl-10 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-shadow bg-gray-100"
                  value={searchQuery}
                  onChange={handleSearchChange}
                />
              </div>
            </div>
            <div className="relative">
              <label className="block text-sm font-medium text-gray-700 mb-1.5">标签</label>
              <button
                type="button"
                className="flex items-center justify-between w-full px-4 py-2 bg-gray-100 border border-gray-200 rounded-lg focus:outline-none transition-colors hover:bg-gray-200"
                onClick={() => setShowTagSelector(!showTagSelector)}
              >
                <span className="text-gray-800">选择标签</span>
                <ChevronDown className="text-gray-500 ml-2 h-4 w-4" />
              </button>
              
              {/* 标签选择器浮层 */}
              {showTagSelector && (
                <div className="absolute right-0 mt-1 z-10 w-[500px] bg-white border border-gray-200 rounded-lg shadow-lg">
                  <TagSelector
                    tags={tagGroups}
                    initialSelectedTags={selectedTags}
                    onTagsChange={handleTagsChange}
                    className="h-[400px]"
                  />
                </div>
              )}
            </div>
          </div>
          
          {/* 已选标签展示 */}
          {selectedTags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {selectedTags.map((tag) => (
                <span key={tag.id} className="px-2.5 py-1 bg-indigo-100 text-indigo-700 rounded-full text-xs flex items-center">
                  {tag.name}
                  <button 
                    className="ml-1.5 text-indigo-500 hover:text-indigo-700"
                    onClick={() => setSelectedTags(selectedTags.filter(t => t.id !== tag.id))}
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
          )}
          
          {/* 错误消息 */}
          {errorMessage && (
            <div className="p-3 bg-red-50 text-red-700 rounded-lg">
              {errorMessage}
            </div>
          )}
          
          {/* 加载状态 */}
          {isLoading ? (
            <div className="py-20 text-center">
              <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-indigo-500 border-r-transparent"></div>
              <p className="mt-2 text-gray-500">加载中...</p>
            </div>
          ) : (
            <>
              {/* 题目列表 */}
              <div className="divide-y divide-gray-200 border border-gray-200 rounded-lg bg-white">
                {filteredQuestions.length === 0 ? (
                  <div className="py-8 text-center text-gray-500">
                    没有匹配的题目
                  </div>
                ) : (
                  paginatedQuestions.map((question) => (
                    <div 
                      key={question.id} 
                      className="p-4 flex flex-col sm:flex-row sm:items-center justify-between gap-3 hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="text-xs px-2 py-0.5 bg-gray-100 rounded">{question.id}</span>
                          <span className="font-medium">{question.title}</span>
                        </div>
                        <div className="text-sm text-gray-500 flex flex-wrap gap-2 mt-1.5">
                          <span className={`px-2 py-0.5 ${
                            question.type === 'programming' 
                              ? 'bg-blue-100 text-blue-700' 
                              : 'bg-gray-100 text-gray-700'
                            } rounded-full`}
                          >
                            {getQuestionTypeText(question.type)}
                          </span>
                          {getDifficultyBadge(question.difficulty)}
                          <span className="text-gray-500">{question.source}</span>
                          <div className="flex gap-1">
                            {question.tags?.map((tag: string, idx: number) => (
                              <span key={idx} className="px-2 py-0.5 bg-indigo-50 text-indigo-600 rounded-full text-xs">
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                      <button
                        type="button"
                        className={`px-3 py-1.5 text-sm rounded-lg border transition-colors ${
                          isQuestionSelected(question.id)
                            ? 'border-gray-300 text-gray-400 cursor-not-allowed'
                            : 'border-indigo-500 text-indigo-500 hover:bg-indigo-50'
                        } flex items-center gap-1.5`}
                        onClick={() => onAddQuestion(question)}
                        disabled={isQuestionSelected(question.id)}
                      >
                        <Plus className="h-4 w-4" />
                        添加
                      </button>
                    </div>
                  ))
                )}
              </div>
              
              {/* 分页 */}
              {filteredQuestions.length > 0 && (
                <div className="mt-5">
                  <Pagination
                    currentPage={currentPage}
                    totalItems={totalItems}
                    pageSize={pageSize}
                    onPageChange={handlePageChange}
                    showInfo={true}
                    maxPageButtons={5}
                    className="pb-2"
                  />
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  )
} 