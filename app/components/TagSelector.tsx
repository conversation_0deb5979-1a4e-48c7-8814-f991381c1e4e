import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch, faChevronDown, faChevronUp, faUndo } from '@fortawesome/free-solid-svg-icons';
import TagService, {Tag, TagGroup} from "@/app/service/tag-service";


interface TagSelectorProps {
  onTagsChange?: (selectedTags: Tag[]) => void;
  className?: string;
  initialSelectedTags?: Tag[];
  tags: TagGroup[]
}

// 自定义滚动条样式
const scrollbarStyles = `
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: #ffffff;
    border-radius: 3px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
  
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #ffffff;
  }
`;

const TagSelector: React.FC<TagSelectorProps> = ({ onTagsChange, className, initialSelectedTags = [], tags }) => {
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
  const [selectedTags, setSelectedTags] = useState<Tag[]>(initialSelectedTags);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredCategories, setFilteredCategories] = useState<TagGroup[]>(tags);
  const [allExpanded, setAllExpanded] = useState(false);
  const [selectedTagsExpanded, setSelectedTagsExpanded] = useState(false);

  // 当初始选中标签变化时更新选中状态
  useEffect(() => {
    setSelectedTags(initialSelectedTags);
  }, [initialSelectedTags]);


  // 初始化所有分类为收起状态
  useEffect(() => {
    const initialExpandedState: Record<string, boolean> = {};
    tags.forEach(category => {
      initialExpandedState[category.name] = false;
    });
    setExpandedCategories(initialExpandedState);
  }, [tags]);

  // 检查是否所有分类都已展开
  useEffect(() => {
    const areAllExpanded = Object.values(expandedCategories).every(value => value === true);
    setAllExpanded(areAllExpanded);
  }, [expandedCategories]);

  // 筛选标签
  useEffect(() => {
    if (!searchTerm) {
      setFilteredCategories(tags);
      return;
    }

    const filtered = tags.map(category => ({
      name: category.name,
      tags: category.tags.filter(tag => tag.name.toLowerCase().includes(searchTerm.toLowerCase()))
    })).filter(category => category.tags.length > 0);

    setFilteredCategories(filtered);
  }, [searchTerm, tags]);

  // 选择标签
  const toggleTag = (tag: Tag) => {
    let newSelectedTags: Tag[];

    if (selectedTags.includes(tag)) {
      newSelectedTags = selectedTags.filter(t => t !== tag);
    } else {
      newSelectedTags = [...selectedTags, tag];
    }

    setSelectedTags(newSelectedTags);
    onTagsChange?.(newSelectedTags);
  };

  // 展开/收起分类
  const toggleCategory = (categoryName: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryName]: !prev[categoryName]
    }));
  };

  // 重置所有选择
  const resetTags = () => {
    setSelectedTags([]);
    onTagsChange?.([]);
  };

  // 展开/收起所有分类
  const toggleAllCategories = () => {
    const newExpandedState: Record<string, boolean> = {};
    tags.forEach(category => {
      newExpandedState[category.name] = !allExpanded;
    });
    setExpandedCategories(newExpandedState);
  };

  // 展开/收起已选标签
  const toggleSelectedTags = () => {
    setSelectedTagsExpanded(!selectedTagsExpanded);
  };

  // 截断标签名称
  const truncateTagName = (name: string, maxLength: number = 8) => {
    if (name.length <= maxLength) return name;
    return `${name.substring(0, maxLength)}...`;
  };

  return (
    <>
      <style jsx>{scrollbarStyles}</style>
      <div className={`bg-white rounded-lg p-3 relative flex flex-col ${className}`}>
        {/* 搜索框 */}
        <div className="relative mb-3">
          <div className="absolute inset-y-0 left-2 flex items-center pointer-events-none">
            <FontAwesomeIcon icon={faSearch} className="text-gray-400 text-sm" />
          </div>
          <input
            type="text"
            placeholder="筛选标签"
            className="w-full py-1.5 pl-7 pr-3 text-sm bg-gray-100 rounded-full focus:outline-none focus:ring-1 focus:ring-blue-300"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        {/* 标签分类 - 可滚动区域（使用自定义滚动条样式） */}
        <div className="overflow-y-auto flex-1 custom-scrollbar pb-2">
          {filteredCategories.map((category) => (
            <div key={category.name} className="mb-2">
              <div
                className="flex justify-between items-center py-1 px-1 cursor-pointer"
                onClick={() => toggleCategory(category.name)}
              >
                <h3 className="text-base font-medium">{category.name}</h3>
                <FontAwesomeIcon
                  icon={expandedCategories[category.name] ? faChevronUp : faChevronDown}
                  className="text-gray-500 text-xs"
                />
              </div>

              {/* 展开时显示所有标签 */}
              {expandedCategories[category.name] ? (
                <div className="flex flex-wrap gap-1.5 mt-1">
                  {category.tags.map((tag) => (
                    <button
                      key={tag.id}
                      className={`px-2.5 py-1 rounded-full text-xs ${
                        selectedTags.includes(tag)
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                      onClick={() => toggleTag(tag)}
                    >
                      {tag.name}
                    </button>
                  ))}
                </div>
              ) : (
                /* 收起时显示一行标签预览 */
                <div className="flex flex-nowrap gap-1.5 mt-1 overflow-hidden">
                  {category?.tags?.slice(0, 3).map((tag) => (
                    <button
                      key={tag.id}
                      className={`px-2.5 py-1 rounded-full text-xs ${
                        selectedTags.includes(tag)
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                      onClick={() => toggleTag(tag)}
                    >
                      {truncateTagName(tag.name)}
                    </button>
                  ))}
                  {category.tags?.length > 3 && (
                    <span className="text-xs text-gray-500 flex items-center">+{category.tags.length - 3}个</span>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* 已选标签显示 - 固定在底部 */}
        {selectedTags.length > 0 && (
          <div className="mt-2 mb-10 pt-2 border-t border-gray-200">
            <div className="flex justify-between items-center mb-1.5">
              <h3 className="text-xs text-gray-500">已选标签：</h3>
              {selectedTags.length > 3 && (
                <button 
                  className="text-xs text-blue-500 hover:text-blue-700 flex items-center"
                  onClick={toggleSelectedTags}
                >
                  {selectedTagsExpanded ? '收起' : '展开'}
                  <FontAwesomeIcon 
                    icon={selectedTagsExpanded ? faChevronUp : faChevronDown} 
                    className="ml-1 text-xs" 
                  />
                </button>
              )}
            </div>
            <div className={`flex flex-wrap gap-1.5 ${selectedTagsExpanded ? 'max-h-[80px] overflow-y-auto custom-scrollbar' : 'max-h-[26px] overflow-hidden'}`}>
              {selectedTags.map(tag => (
                <div key={tag.id} className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full text-xs flex items-center">
                  {selectedTagsExpanded ? tag.name : truncateTagName(tag.name)}
                  <button
                    className="ml-1 text-blue-500 hover:text-blue-700"
                    onClick={() => toggleTag(tag)}
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
            {!selectedTagsExpanded && selectedTags.length > 3 && (
              <div className="text-xs text-gray-500 mt-1">
                共{selectedTags.length}个标签
              </div>
            )}
          </div>
        )}

        {/* 底部固定操作按钮 */}
        <div className="flex justify-between items-center py-2 text-sm absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-3 rounded-b-lg">
          <button
            className="text-blue-500 hover:text-blue-700 flex items-center text-xs"
            onClick={toggleAllCategories}
          >
            {allExpanded ? '收起所有' : '展开全部'}
          </button>
          <button
            className="flex items-center text-gray-500 hover:text-gray-700 text-xs"
            onClick={resetTags}
          >
            <FontAwesomeIcon icon={faUndo} className="mr-1" />
            重置
          </button>
        </div>
      </div>
    </>
  );
};

export default TagSelector;
