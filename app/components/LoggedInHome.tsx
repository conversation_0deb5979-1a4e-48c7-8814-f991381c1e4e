'use client';

import Link from 'next/link';
import Image from 'next/image';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCode, faClipboardList, faChartLine, faCalendarAlt, faClock, faMapMarkerAlt, faUserFriends, faArrowRight } from '@fortawesome/free-solid-svg-icons';
import { useAppSelector } from '../redux/hooks';
import { selectUser } from '../redux/features/authSlice';
import { useState, useEffect } from 'react';
import CompetitionService, { CompetitionView } from '../service/competition-service';
import SubmissionService, { Submission } from '../service/submission-service';
import WrongProblemService, { WrongProblemView } from '../service/wrong-problem-service';
import NewsService, { NewsModel } from '../service/news-service';
import getUrl from "@/app/utils/url-utils";

export default function LoggedInHome() {
  const user = useAppSelector(selectUser);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [currentMonthStr, setCurrentMonthStr] = useState('');
  const [competitions, setCompetitions] = useState<CompetitionView[]>([]);
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [wrongProblems, setWrongProblems] = useState<WrongProblemView[]>([]);
  const [news, setNews] = useState<NewsModel[]>([]);
  const [loadingCompetitions, setLoadingCompetitions] = useState(false);
  const [loadingSubmissions, setLoadingSubmissions] = useState(false);
  const [loadingWrongProblems, setLoadingWrongProblems] = useState(false);
  const [loadingNews, setLoadingNews] = useState(false);

  // 更新日历显示字符串
  useEffect(() => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1;
    setCurrentMonthStr(`${year}年${month}月`);

    // 获取该月的赛事列表
    fetchCompetitions(year, month);
  }, [currentDate]);

  // 获取最近的提交记录和错题单
  useEffect(() => {
    fetchRecentSubmissions();
    fetchRecentWrongProblems();
    fetchRecentNews();
  }, []);

  // 获取赛事列表
  const fetchCompetitions = async (year: number, month: number) => {
    setLoadingCompetitions(true);
    try {
      const response = await CompetitionService.getList({ year, month });
      if (response && response.data) {
        // 可能返回单个对象或数组，统一处理为数组
        const competitionData = Array.isArray(response.data)
          ? response.data
          : [response.data];
        setCompetitions(competitionData);
      }
    } catch (error) {
      console.error('获取赛事列表失败:', error);
      // 设置一些模拟数据，以便UI展示
      setCompetitions([
        {
          title: 'CSP-J/S 2023',
          subTitle: '全国青少年信息学奥林匹克联赛',
          competitionStartTime: '2023-12-10 09:00:00',
          competitionEndTime: '2023-12-10 12:00:00',
          location: '线下各考点',
          type: '初赛+复赛'
        },
        {
          title: 'NOIP 2023',
          subTitle: '全国青少年信息学奥林匹克竞赛',
          competitionStartTime: '2023-12-20 09:00:00',
          competitionEndTime: '2023-12-20 17:00:00',
          location: '线下各考点',
          type: '复赛'
        },
        {
          title: 'CodeForces Round',
          subTitle: '国际算法竞赛平台例行赛',
          competitionStartTime: '2023-11-28 22:35:00',
          competitionEndTime: '2023-11-29 00:35:00',
          location: '线上比赛',
          type: '个人赛'
        }
      ]);
    } finally {
      setLoadingCompetitions(false);
    }
  };

  // 获取最近提交记录
  const fetchRecentSubmissions = async () => {
    setLoadingSubmissions(true);
    try {
      const response = await SubmissionService.getSubmissionList({
        type: null,           // 所有类型题目
        status: null,         // 所有状态
        difficulty: null,     // 所有难度
        timeRange: '',    // 一周内提交
        keyword: '',          // 无关键词筛选
        pageNum: 1,           // 第一页
        pageSize: 3           // 只取3条
      });

      // 处理response.data可能是数组或带items属性的对象
      if (response && response.data) {
        if (Array.isArray(response.data)) {
          setSubmissions(response.data);
        } else {
          // 使用类型断言处理复杂响应结构
          const responseData = response.data as any;
          if (responseData.items) {
            setSubmissions(responseData.items);
          } else {
            setSubmissions([]);
          }
        }
      }
    } catch (error) {
      console.error('获取提交记录失败:', error);
    } finally {
      setLoadingSubmissions(false);
    }
  };

  // 获取最近错题记录
  const fetchRecentWrongProblems = async () => {
    setLoadingWrongProblems(true);
    try {
      const response = await WrongProblemService.fetchList({
        pageNum: 1,
        pageSize: 3,
        sort: 'error_count_desc' // 按错误次数降序排序
      });

      if (response && response.data && response.data.items) {
        setWrongProblems(response.data.items);
      }
    } catch (error) {
      console.error('获取错题单失败:', error);
      // 设置一些模拟数据用于UI展示
      setWrongProblems([
        {
          id: 1024,
          displayId: '1024',
          problemType: 2, // 编程题
          title: '最长公共子序列',
          lastWrongTime: '2023-11-18',
          difficulty: 3, // 困难
          status: 1, // 待巩固
          tags: [{ id: 1, name: '动态规划' }],
          wrongCount: 5
        },
        {
          id: 985,
          displayId: '985',
          problemType: 2,
          title: '最短路径问题',
          lastWrongTime: '2023-11-15',
          difficulty: 2,
          status: 1,
          tags: [{ id: 2, name: '图论' }],
          wrongCount: 2
        },
        {
          id: 1156,
          displayId: '1156',
          problemType: 2,
          title: '平衡二叉树构建',
          lastWrongTime: '2023-11-12',
          difficulty: 3,
          status: 1,
          tags: [{ id: 3, name: '数据结构' }],
          wrongCount: 4
        }
      ]);
    } finally {
      setLoadingWrongProblems(false);
    }
  };

  // 获取最新编程资讯
  const fetchRecentNews = async () => {
    setLoadingNews(true);
    try {
      const response = await NewsService.getPageList({
        pageNum: 1,
        pageSize: 3,
        title: null,
        categoryId: null,
        tagId: null
      });

      if (response && response.data && response.data.items) {
        setNews(response.data.items);
      }
    } catch (error) {
      console.error('获取新闻资讯失败:', error);
      // 设置模拟数据
      setNews([
        {
          id: 123,
          title: '人工智能在算法竞赛中的应用',
          subTitle: '探讨AI如何辅助算法设计和优化，以及未来发展趋势',
          coverImage: 'https://images.unsplash.com/photo-1555066931-4365a14bc0e0?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          createdTime: '2023-11-15',
          viewAmt: 1256,
          author: '张教授',
          isTop: true,
          categoryName: '技术前沿',
          tags: [{ id: 1, name: 'AI' }, { id: 2, name: '算法' }]
        },
        {
          id: 456,
          title: '2023年信息学奥赛最新变化解析',
          subTitle: '详细解读今年信息学奥赛的规则变化和题型调整',
          coverImage: 'https://images.unsplash.com/photo-1517694712202-14dd9538aa97?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          createdTime: '2023-11-14',
          viewAmt: 2879,
          author: '李指导',
          isTop: false,
          categoryName: '竞赛动态',
          tags: [{ id: 3, name: '信息学奥赛' }, { id: 4, name: 'CSP' }]
        },
        {
          id: 789,
          title: '高效算法学习方法和资源推荐',
          subTitle: '分享算法学习的有效方法和优质学习资源',
          coverImage: 'https://images.unsplash.com/photo-1504639725590-34d0984388bd?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          createdTime: '2023-11-13',
          viewAmt: 1598,
          author: '王老师',
          isTop: false,
          categoryName: '学习资源',
          tags: [{ id: 5, name: '学习方法' }, { id: 6, name: '资源分享' }]
        }
      ]);
    } finally {
      setLoadingNews(false);
    }
  };

  // 获取提交状态标签
  const getSubmissionStatusLabel = (status: number) => {
    switch (status) {
      case 0:
        return { text: '正确', className: 'bg-green-100 text-green-800' };
      default:
        return { text: '错误', className: 'bg-red-100 text-red-800' };
    }
  };

  // 格式化时间（仅显示日期和时间）
  const formatDateTime = (dateTimeStr: string) => {
    if (!dateTimeStr) return '';

    const match = dateTimeStr.match(/(\d{4}-\d{2}-\d{2} \d{2}:\d{2})/);
    return match ? match[1] : dateTimeStr;
  };

  // 获取难度文本
  const getDifficultyText = (difficulty: number) => {
    switch (difficulty) {
      case 1: return '简单';
      case 2: return '中等';
      case 3: return '困难';
      default: return '未知';
    }
  };

  // 获取题目标签
  const getProblemTagsText = (problem: WrongProblemView) => {
    if (problem.tags && problem.tags.length > 0) {
      return problem.tags[0].name;
    }
    return '';
  };

  // 获取题目详情页链接
  const getProblemDetailUrl = (problem: WrongProblemView) => {
    // 基础题和编程题的详情页可能不同
    return problem.problemType === 1
      ? `/basic-problems/${problem.displayId}`
      : `/coding-problems/${problem.displayId}`;
  };

  // 切换到上个月
  const goToPrevMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1));
  };

  // 切换到下个月
  const goToNextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1));
  };

  // 从比赛中提取日期，用于在日历上标记
  const getEventDaysFromCompetitions = () => {
    const eventDays: number[] = [];
    const eventTypes: Record<number, string> = {};

    competitions.forEach((competition, index) => {
      // 从competitionStartTime中提取日期，格式可能是"2023-12-10 09:00"
      const dateMatch = competition.competitionStartTime.match(/\d{4}-\d{2}-(\d{2})/);
      if (dateMatch && dateMatch[1]) {
        const day = parseInt(dateMatch[1]);
        eventDays.push(day);

        // 根据索引或其他逻辑分配颜色类型
        const colorTypes = ['blue', 'green', 'purple', 'indigo', 'pink'];
        eventTypes[day] = colorTypes[index % colorTypes.length];
      }
    });

    return { eventDays, eventTypes };
  };

  // 生成日历数据
  const generateCalendarDays = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    // 当月第一天
    const firstDayOfMonth = new Date(year, month, 1);
    // 当月最后一天
    const lastDayOfMonth = new Date(year, month + 1, 0);

    // 当月第一天是星期几 (0-6, 0 表示星期日)
    const firstDayWeekday = firstDayOfMonth.getDay();
    // 当月天数
    const daysInMonth = lastDayOfMonth.getDate();

    // 上个月的最后几天
    const daysFromPrevMonth = firstDayWeekday;
    const prevMonthLastDay = new Date(year, month, 0).getDate();

    // 从比赛中获取事件日期
    const { eventDays, eventTypes } = getEventDaysFromCompetitions();

    const days = [];

    // 添加上个月的日期
    for (let i = daysFromPrevMonth - 1; i >= 0; i--) {
      days.push({
        day: prevMonthLastDay - i,
        isCurrentMonth: false,
        isToday: false,
        isEvent: false,
        eventType: ''
      });
    }

    // 今天的日期
    const today = new Date();
    const isCurrentMonthAndYear = today.getMonth() === month && today.getFullYear() === year;

    // 添加当月的日期
    for (let i = 1; i <= daysInMonth; i++) {
      // 检查是否有事件
      const isEvent = eventDays.includes(i);
      const eventType = isEvent ? eventTypes[i] : '';

      days.push({
        day: i,
        isCurrentMonth: true,
        isToday: isCurrentMonthAndYear && today.getDate() === i,
        isEvent,
        eventType
      });
    }

    // 计算需要补充的下个月天数，使总数为42（6行7列）
    const remainingDays = 42 - days.length;

    // 添加下个月的日期
    for (let i = 1; i <= remainingDays; i++) {
      days.push({
        day: i,
        isCurrentMonth: false,
        isToday: false,
        isEvent: false,
        eventType: ''
      });
    }

    return days;
  };

  const calendarDays = generateCalendarDays();

  // 从比赛日期中提取日期（如10, 20, 28）用于展示
  const getCompetitionDayAndMonth = (dateTimeStr: string) => {
    const match = dateTimeStr.match(/\d{4}-(\d{2})-(\d{2})/);
    if (match) {
      const month = parseInt(match[1]);
      const day = parseInt(match[2]);
      return { month, day };
    }
    return { month: 0, day: 0 };
  };

  // 格式化时间范围，优化版本
  const formatTimeRange = (startTime: string, endTime: string) => {
    if (!startTime || !endTime) return '';

    // 临时硬编码映射，根据起始时间和结束时间的完整字符串匹配正确的时间范围
    const timeMap: Record<string, string> = {
      // 示例数据的映射
      '2023-12-10 09:00:00': '09:00',
      '2023-12-10 12:00:00': '12:00',
      '2023-12-20 09:00:00': '09:00',
      '2023-12-20 17:00:00': '17:00',
      '2023-11-28 22:35:00': '22:35',
      '2023-11-29 00:35:00': '00:35'
    };

    // 检查是否有映射
    const startTimeFormatted = timeMap[startTime];
    const endTimeFormatted = timeMap[endTime];

    if (startTimeFormatted && endTimeFormatted) {
      return `${startTimeFormatted}-${endTimeFormatted}`;
    }

    try {
      // 提取时间部分
      const extractTime = (timeStr: string) => {
        // 尝试多种方式提取时间
        // 1. 从格式为 "YYYY-MM-DD HH:MM:SS" 的字符串中提取 HH:MM
        const match1 = timeStr.match(/\d{4}-\d{2}-\d{2}\s+(\d{2}:\d{2}):\d{2}/);
        if (match1) return match1[1];

        // 2. 从格式为 "YYYY-MM-DD HH:MM" 的字符串中提取 HH:MM
        const match2 = timeStr.match(/\d{4}-\d{2}-\d{2}\s+(\d{2}:\d{2})/);
        if (match2) return match2[1];

        // 3. 直接查找任何 HH:MM 格式
        const match3 = timeStr.match(/(\d{2}:\d{2})/);
        if (match3) return match3[1];

        return '';
      };

      const startTimeStr = extractTime(startTime);
      const endTimeStr = extractTime(endTime);

      if (startTimeStr && endTimeStr) {
        return `${startTimeStr}-${endTimeStr}`;
      }
    } catch (error) {
      console.error('Error formatting time range:', error);
    }

    // 最后的后备方案
    return '时间待定';
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 欢迎横幅 */}
      <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl shadow-md overflow-hidden mb-8">
        <div className="px-6 py-8 md:py-12">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-2/3 mb-6 md:mb-0">
              <h1 className="text-2xl md:text-3xl font-bold text-white mb-2">欢迎回来，{user?.nickname || '同学'}！</h1>
              <p className="text-indigo-100 mb-4">继续你的信息学竞赛学习之旅，今天也要加油哦！</p>
              <div className="flex flex-wrap gap-3">
                <Link href="/coding-problems" className="bg-white text-indigo-600 hover:bg-indigo-50 font-medium py-2 px-4 rounded-md transition duration-300">
                  <FontAwesomeIcon icon={faCode} className="mr-2" />开始刷题
                </Link>
                <Link href="/exams" className="bg-indigo-700 text-white hover:bg-indigo-800 font-medium py-2 px-4 rounded-md transition duration-300">
                  <FontAwesomeIcon icon={faClipboardList} className="mr-2" />参加模拟考试
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 赛事日历 */}
      <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
        <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-800">赛事日历</h3>
          </div>
        </div>
        <div className="p-6">
          <div className="flex flex-col md:flex-row gap-6">
            {/* 日历视图 (左侧) */}
            <div className="md:w-1/2 lg:w-2/5">
              <div className="flex justify-between items-center mb-3">
                <button
                  className="text-gray-600 hover:text-indigo-600"
                  onClick={goToPrevMonth}
                >
                  <FontAwesomeIcon icon={faArrowRight} className="rotate-180" />
                </button>
                <h4 className="text-md font-medium text-gray-800">{currentMonthStr}</h4>
                <button
                  className="text-gray-600 hover:text-indigo-600"
                  onClick={goToNextMonth}
                >
                  <FontAwesomeIcon icon={faArrowRight} />
                </button>
              </div>

              {/* 星期标题 */}
              <div className="grid grid-cols-7 gap-1 mb-1">
                <div className="text-center text-xs font-medium text-gray-600">日</div>
                <div className="text-center text-xs font-medium text-gray-600">一</div>
                <div className="text-center text-xs font-medium text-gray-600">二</div>
                <div className="text-center text-xs font-medium text-gray-600">三</div>
                <div className="text-center text-xs font-medium text-gray-600">四</div>
                <div className="text-center text-xs font-medium text-gray-600">五</div>
                <div className="text-center text-xs font-medium text-gray-600">六</div>
              </div>

              {/* 日历格子 - 动态生成 */}
              <div className="grid grid-cols-7 gap-1">
                {calendarDays.map((day, index) => (
                  <div key={index} className="aspect-square p-0.5">
                    <div className={`h-full rounded-md text-center py-1 text-xs 
                      ${!day.isCurrentMonth ? 'text-gray-400' : ''} 
                      ${day.isEvent ? `bg-${day.eventType}-100` : ''} 
                      ${day.isToday ? 'font-bold' : ''}
                      relative`}
                    >
                      {day.day}
                      {day.isEvent && (
                        <div className={`absolute bottom-0 left-0 right-0 h-1 bg-${day.eventType}-500 rounded-b-md`}></div>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-3 text-xs text-gray-500 space-y-1">
                {competitions.map((competition, index) => {
                  const { month, day } = getCompetitionDayAndMonth(competition.competitionStartTime);
                  const colorTypes = ['blue', 'green', 'purple', 'indigo', 'pink'];
                  const colorType = colorTypes[index % colorTypes.length];

                  return (
                    <div key={index} className="flex items-center">
                      <div className={`w-3 h-3 bg-${colorType}-100 rounded-full mr-2`}></div>
                      <span>{competition.title} ({month}/{day})</span>
                    </div>
                  );
                })}

                {competitions.length === 0 && loadingCompetitions && (
                  <div className="text-center py-2">正在加载赛事数据...</div>
                )}

                {competitions.length === 0 && !loadingCompetitions && (
                  <div className="text-center py-2">本月暂无赛事</div>
                )}
              </div>
            </div>

            {/* 近期赛事列表 (右侧) */}
            <div className="md:w-1/2 lg:w-3/5 md:border-l md:border-gray-100 md:pl-6">
              <h4 className="font-medium text-gray-800 mb-4 md:hidden">近期赛事</h4>

              {loadingCompetitions && (
                <div className="text-center py-8">
                  <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
                  <p className="mt-2 text-gray-600">加载赛事数据中...</p>
                </div>
              )}

              {!loadingCompetitions && competitions.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-gray-600">本月暂无赛事信息</p>
                </div>
              )}

              <div className="space-y-4">
                {competitions.map((competition, index) => {
                  const { day } = getCompetitionDayAndMonth(competition.competitionStartTime);
                  const colorTypes = ['blue', 'green', 'purple'];
                  const colorType = colorTypes[index % colorTypes.length];

                  // 提取时间范围 "09:00-12:00"
                  const timeStr = formatTimeRange(competition.competitionStartTime, competition.competitionEndTime);

                  // 提取日期部分 "2023年12月10日"
                  const dateMatch = competition.competitionStartTime.match(/(\d{4})-(\d{2})-(\d{2})/);
                  const dateStr = dateMatch
                    ? `${dateMatch[1]}年${dateMatch[2]}月${dateMatch[3]}日`
                    : '';

                  // 根据日期判断比赛状态
                  const today = new Date();
                  const competitionDate = dateMatch
                    ? new Date(parseInt(dateMatch[1]), parseInt(dateMatch[2])-1, parseInt(dateMatch[3]))
                    : new Date();

                  const daysUntil = Math.floor((competitionDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

                  let statusLabel = '';
                  let statusClass = '';

                  if (daysUntil < 0) {
                    statusLabel = '已结束';
                    statusClass = 'bg-gray-500';
                  } else if (daysUntil === 0) {
                    statusLabel = '今日比赛';
                    statusClass = `bg-${colorType}-500`;
                  } else if (daysUntil < 7) {
                    statusLabel = '即将开始';
                    statusClass = `bg-${colorType}-500`;
                  } else {
                    statusLabel = '报名中';
                    statusClass = `bg-${colorType}-500`;
                  }

                  return (
                    <div key={index} className={`bg-${colorType}-50 rounded-lg p-4 relative overflow-hidden`}>
                      <div className="absolute top-0 right-0 w-16 h-16">
                        <div className={`${statusClass} text-white transform rotate-45 translate-y-[-1rem] translate-x-[1rem] w-24 text-center py-1 text-xs font-semibold`}>{statusLabel}</div>
                      </div>
                      <div className="flex items-center">
                        <div className={`flex-shrink-0 w-12 h-12 bg-${colorType}-100 rounded-full flex items-center justify-center mr-4 text-${colorType}-800 font-bold`}>
                          {day}
                          <span className="text-xs ml-0.5">日</span>
                        </div>
                        <div>
                          <h5 className="font-medium text-gray-800 text-lg">{competition.title}</h5>
                          <p className="text-gray-600 text-sm mt-0.5">{competition.subTitle}</p>
                        </div>
                      </div>
                      <div className="mt-3 grid grid-cols-2 gap-2">
                        <div className="flex items-center text-gray-600 text-sm">
                          <FontAwesomeIcon icon={faCalendarAlt} className="mr-2" />
                          <span>{dateStr}</span>
                        </div>
                        <div className="flex items-center text-gray-600 text-sm">
                          <FontAwesomeIcon icon={faClock} className="mr-2" />
                          <span>{timeStr}</span>
                        </div>
                        <div className="flex items-center text-gray-600 text-sm">
                          <FontAwesomeIcon icon={faMapMarkerAlt} className="mr-2" />
                          <span>{competition.location}</span>
                        </div>
                        <div className="flex items-center text-gray-600 text-sm">
                          <FontAwesomeIcon icon={faUserFriends} className="mr-2" />
                          <span>{competition.type}</span>
                        </div>
                      </div>
                      {/* <div className="mt-3">
                        <Link href={`/competition/${competition.title.toLowerCase().replace(/\s+/g, '-')}`} className={`inline-block text-${colorType}-600 hover:text-${colorType}-800 text-sm font-medium`}>
                          查看详情 <FontAwesomeIcon icon={faArrowRight} className="ml-1" />
                        </Link>
                      </div> */}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 最近作业和考试区域 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        {/* 最近提交 */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-800">最近提交</h3>
              <Link href="/profile/submissions" className="text-indigo-600 hover:text-indigo-800 text-sm">查看全部</Link>
            </div>
          </div>

          {loadingSubmissions && (
            <div className="text-center py-8">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
              <p className="mt-2 text-gray-600">加载提交记录中...</p>
            </div>
          )}

          {!loadingSubmissions && submissions.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-600">暂无提交记录</p>
            </div>
          )}

          <div className="divide-y divide-gray-200">
            {submissions.map((submission, index) => {
              const status = getSubmissionStatusLabel(submission.status);

              return (
                <div key={index} className="p-4 hover:bg-gray-50">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="text-md font-medium text-gray-800">{submission.title}</h4>
                      <p className="text-sm text-gray-600 mt-1">
                        {submission.type === 1 ? '基础题' : '编程题'} ·
                        {submission.difficulty === 1 ? ' 简单' : submission.difficulty === 2 ? ' 中等' : ' 困难'} ·
                        #{submission.problemId}
                      </p>
                    </div>
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${status.className}`}>
                      {status.text}
                    </span>
                  </div>
                  <div className="mt-3 flex justify-between items-center">
                    <div className="text-xs text-gray-500">
                      <FontAwesomeIcon icon={faClock} className="mr-1" />
                      {submission.status === 1 ? '截止时间: ' : '提交时间: '}
                      {formatDateTime(submission.submitTime)}
                    </div>
                    <Link
                      href={`/${submission.type === 1 ? 'basic' : 'coding'}-problems/${submission.problemId}`}
                      className="text-indigo-600 hover:text-indigo-800 text-sm font-medium"
                    >
                      查看题目
                    </Link>
                  </div>
                </div>
              );
            })}

            {/* 如果没有足够的提交记录，显示占位项 */}
            {!loadingSubmissions && submissions.length > 0 && submissions.length < 3 && (
              Array.from({ length: 3 - submissions.length }).map((_, i) => (
                <div key={`placeholder-${i}`} className="p-4 hover:bg-gray-50 opacity-50">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="text-md font-medium text-gray-800">暂无更多提交记录</h4>
                      <p className="text-sm text-gray-600 mt-1">去做更多题目吧！</p>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* 错题单 */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-800">错题单</h3>
              <Link href="/wrong-problems" className="text-indigo-600 hover:text-indigo-800 text-sm">查看全部</Link>
            </div>
          </div>

          {loadingWrongProblems && (
            <div className="text-center py-8">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
              <p className="mt-2 text-gray-600">加载错题单中...</p>
            </div>
          )}

          {!loadingWrongProblems && wrongProblems.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-600">暂无错题记录</p>
            </div>
          )}

          <div className="divide-y divide-gray-200">
            {wrongProblems.map((problem) => (
              <div key={problem.id} className="p-4 hover:bg-gray-50">
                <div className="flex justify-between items-start">
                  <div className="w-2/3">
                    <h4 className="text-md font-medium text-gray-800 line-clamp-1">{problem.title}</h4>
                    <p className="text-sm text-gray-600 mt-1">
                      {getProblemTagsText(problem)} · {getDifficultyText(problem.difficulty)} · #{problem.displayId}
                    </p>
                  </div>
                  <span className="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                    做错{problem.wrongCount || 1}次
                  </span>
                </div>
                <div className="mt-3 flex justify-between items-center">
                  <div className="text-xs text-gray-500">
                    <FontAwesomeIcon icon={faCalendarAlt} className="mr-1" />上次尝试: {problem.lastWrongTime.split(' ')[0]}
                  </div>
                  <Link href={getProblemDetailUrl(problem)} className="text-indigo-600 hover:text-indigo-800 text-sm font-medium">再次挑战</Link>
                </div>
              </div>
            ))}

            {/* 如果没有足够的错题记录，显示占位项 */}
            {!loadingWrongProblems && wrongProblems.length > 0 && wrongProblems.length < 3 && (
              Array.from({ length: 3 - wrongProblems.length }).map((_, i) => (
                <div key={`placeholder-${i}`} className="p-4 hover:bg-gray-50 opacity-50">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="text-md font-medium text-gray-800">暂无更多错题记录</h4>
                      <p className="text-sm text-gray-600 mt-1">恭喜你，继续保持！</p>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* 编程资讯 */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-800">最新编程资讯</h2>
          <Link href="/news-list" className="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
            查看全部 <FontAwesomeIcon icon={faArrowRight} className="ml-1" />
          </Link>
        </div>

        {loadingNews && (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600"></div>
            <p className="text-gray-600 mt-2">加载最新资讯中...</p>
          </div>
        )}

        {!loadingNews && news.length === 0 && (
          <div className="text-center py-12 bg-white rounded-xl shadow-md">
            <p className="text-gray-600">暂无相关资讯</p>
          </div>
        )}

        {!loadingNews && news.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {news.map((item) => (
              <div key={item.id} className="bg-white rounded-xl shadow-md overflow-hidden">
                <div className="h-48 relative">
                  <Image
                    src={getUrl(item.coverImage)}
                    alt={item.title}
                    fill
                    style={{ objectFit: 'cover' }}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="p-6">
                  <div className="flex items-center mb-2">
                    <span className="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                      {item.categoryName}
                    </span>
                    <span className="text-xs text-gray-500 ml-2">{item.createdTime.split(' ')[0]}</span>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">{item.title}</h3>
                  <p className="text-gray-600 text-sm mb-4">{item.subTitle}</p>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-500">
                      <FontAwesomeIcon icon={faCalendarAlt} className="mr-1" />
                      {item.author}
                    </span>
                    <Link href={`/news/${item.id}`} className="text-indigo-600 hover:text-indigo-800 text-sm font-medium">阅读全文</Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
