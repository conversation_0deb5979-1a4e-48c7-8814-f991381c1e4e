import React from 'react';

export default function ChartSkeleton() {
  return (
    <div className="bg-white rounded-xl shadow-md overflow-hidden animate-pulse">
      <div className="px-6 py-4 bg-gray-200 border-b border-gray-100">
        <div className="h-6 w-32 bg-gray-300 rounded"></div>
      </div>
      <div className="p-6">
        <div className="h-64 bg-gray-200 rounded"></div>
        <div className="grid grid-cols-3 gap-4 mt-6">
          <div className="flex flex-col items-center">
            <div className="h-8 w-12 bg-gray-300 rounded mb-2"></div>
            <div className="h-4 w-16 bg-gray-200 rounded"></div>
          </div>
          <div className="flex flex-col items-center">
            <div className="h-8 w-12 bg-gray-300 rounded mb-2"></div>
            <div className="h-4 w-16 bg-gray-200 rounded"></div>
          </div>
          <div className="flex flex-col items-center">
            <div className="h-8 w-12 bg-gray-300 rounded mb-2"></div>
            <div className="h-4 w-16 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    </div>
  );
} 