'use client';

import React, {useState, useEffect, Suspense, useRef} from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faArrowLeft, faInfoCircle, faTimes, faCheck, faRedo, faSync
} from '@fortawesome/free-solid-svg-icons';
import TradeService, {TradeInfo} from "@/app/service/trade-service";
import { faAlipay, faWeixin } from '@fortawesome/free-brands-svg-icons';
import { QRCodeSVG } from 'qrcode.react';

// 在文件顶部添加类型定义
interface TradeStatusResponse {
  data: number; // -10: 已取消, 0: 待支付, 10: 支付成功, 等
}

// 微信支付二维码弹窗组件
interface WechatPayModalProps {
  isOpen: boolean;
  onClose: () => void;
  productName: string;
  price: number;
  payUrl?: string;
  tradeNo: string;
}

function WechatPayModal({ isOpen, onClose, productName, price, payUrl, tradeNo }: WechatPayModalProps) {
  const [paymentStatus, setPaymentStatus] = useState<'waiting' | 'success' | 'failed'>('waiting');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const pollInterval = useRef<NodeJS.Timeout | null>(null);

  // 获取支付二维码
  useEffect(() => {
    if (!isOpen) return;

    setIsLoading(true);
    setError(null);

    // 模拟从后端获取支付二维码信息
    // const fetchPaymentCode = async () => {
    //   try {
    //     // 实际项目中应该调用后端API获取支付链接
    //     // 这里模拟一个异步请求
    //     await new Promise(resolve => setTimeout(resolve, 1000));
    //
    //     // 使用固定值代替随机值，避免水合错误
    //     const mockPaymentUrl = `weixin://wxpay/bizpayurl?pr=${price}&tradeno=${tradeNo || 'default-trade-no'}`;
    //     setPaymentCode(mockPaymentUrl);
    //     setIsLoading(false);
    //   } catch (err) {
    //     setError('获取支付二维码失败，请重试');
    //     setIsLoading(false);
    //   }
    // };

    // fetchPaymentCode();
  }, [isOpen, price, payUrl, tradeNo]);

  // 轮询订单状态
  useEffect(() => {
    if (!isOpen || paymentStatus !== 'waiting') return;

    const pollStatus = async () => {
      try {
        // 调用订单状态API
        const statusResponse = await TradeService.getTradeStatus(tradeNo);
        // 注意：接口返回的是一个包含data属性的对象，而data才是实际的订单状态值
        // 使用类型断言告诉TypeScript这是什么类型
        const orderStatus = (statusResponse as any).data as number;
        if (orderStatus === 10) { // 10 表示已支付
          setPaymentStatus('success');
          if (pollInterval.current) {
            clearInterval(pollInterval.current);
          }
        }
      } catch (err) {
        console.error('轮询订单状态失败:', err);
      }
    };

    // 每3秒轮询一次
    pollInterval.current = setInterval(pollStatus, 3000);

    return () => {
      if (pollInterval.current) {
        clearInterval(pollInterval.current);
      }
    };
  }, [isOpen, tradeNo, paymentStatus]);

  // 支付成功后自动关闭
  useEffect(() => {
    if (paymentStatus === 'success') {
      const timer = setTimeout(() => {
        onClose();
        // 这里可以添加跳转到成功页面的逻辑
        window.location.href = '/profile'; // 实际项目中应该跳转到成功页面
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [paymentStatus, onClose]);

  // 重新加载支付二维码
  // const handleRefresh = () => {
  //   setIsLoading(true);
  //   setError(null);
  //
  //   // 模拟从后端重新获取支付二维码
  //   setTimeout(() => {
  //     // 使用固定值代替随机时间戳
  //     const mockPaymentUrl = `weixin://wxpay/bizpayurl?pr=${price}&tradeno=${tradeNo || 'refreshed-trade-no'}`;
  //     setPaymentCode(mockPaymentUrl);
  //     setIsLoading(false);
  //   }, 1000);
  // };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-xl max-w-md w-full p-6 relative">
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
        >
          <FontAwesomeIcon icon={faTimes} />
        </button>

        {/* 标题 */}
        <div className="text-center mb-6">
          <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-green-100 mb-4">
            <FontAwesomeIcon icon={faWeixin} size="lg" color="#57BE6A" />
          </div>
          <h2 className="text-2xl font-bold text-gray-800">微信支付</h2>
        </div>

        {/* 商品信息 */}
        <div className="mb-6">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-gray-600">商品名称</span>
              <span className="text-gray-800 font-medium">{productName}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">支付金额</span>
              <span className="text-xl font-bold text-green-600">
                ¥{(typeof price === 'number' ? price / 100 : 0).toFixed(2)}
              </span>
            </div>
          </div>
        </div>

        {/* 支付状态 */}
        {paymentStatus === 'success' ? (
          <div className="text-center py-6">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-4">
              <FontAwesomeIcon icon={faCheck} size="lg" color="#10B981" />
            </div>
            <h3 className="text-xl font-bold text-green-600 mb-2">支付成功</h3>
            <p className="text-gray-600">正在跳转到个人中心...</p>
          </div>
        ) : (
          <>
            {/* 二维码 */}
            <div className="flex justify-center mb-6">
              <div className="border border-gray-200 p-4 rounded-lg relative">
                {!payUrl ? (
                  <div className="w-48 h-48 flex items-center justify-center">
                    <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-green-500"></div>
                  </div>
                ) : error ? (
                  <div className="w-48 h-48 flex flex-col items-center justify-center bg-gray-50 p-4">
                    <p className="text-red-500 text-center mb-4">{error}</p>
                  </div>
                ) : (
                  <div className="flex flex-col items-center">
                    <QRCodeSVG
                      value={payUrl}
                      size={180}
                      level="H"
                      includeMargin={true}
                      bgColor="#ffffff"
                      fgColor="#000000"
                    />
                    <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 bg-white px-2">
                      {/* 客户端组件包装图片 */}
                      <ClientOnlyImage />
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* 支付提示 */}
            <div className="text-center">
              <p className="text-gray-600 mb-2">请使用微信扫描二维码完成支付</p>
              <p className="text-gray-500 text-sm">二维码有效期为2小时</p>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

// 创建一个客户端专用图片组件
function ClientOnlyImage() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <Image
      src="/wechat-pay-logo.png"
      alt="微信支付"
      width={90}
      height={30}
      onError={(e) => {
        const target = e.target as HTMLImageElement;
        target.style.display = 'none';
      }}
    />
  );
}

// 添加倒计时组件
function CountdownTimer({ expireAt }: { expireAt: string | number }) {
  const [timeLeft, setTimeLeft] = useState<string>('');

  useEffect(() => {
    const calculateTimeLeft = () => {
      const now = new Date().getTime();
      const expireTime = typeof expireAt === 'string' ? new Date(expireAt).getTime() : expireAt;
      const difference = expireTime - now;
      console.log(expireAt)

      if (difference <= 0) {
        setTimeLeft('订单已过期');
        return;
      }

      const minutes = Math.floor(difference / (1000 * 60));
      const seconds = Math.floor((difference % (1000 * 60)) / 1000);
      setTimeLeft(`${minutes}分${seconds}秒`);
    };

    calculateTimeLeft();
    const timer = setInterval(calculateTimeLeft, 1000);

    return () => clearInterval(timer);
  }, [expireAt]);

  return (
    <div className="text-red-600 font-medium">
      {timeLeft}
    </div>
  );
}

// 添加支付宝支付弹窗组件
interface AlipayModalProps {
  isOpen: boolean;
  onClose: () => void;
  productName: string;
  price: number;
  aliPayBody?: string;
  tradeNo: string;
}

function AlipayModal({ isOpen, onClose, productName, price, aliPayBody, tradeNo }: AlipayModalProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [paymentStatus, setPaymentStatus] = useState<'waiting' | 'success' | 'failed'>('waiting');
  const aliPayQrCodeRef = useRef<HTMLDivElement>(null);
  const pollInterval = useRef<NodeJS.Timeout | null>(null);

  // 轮询订单状态
  useEffect(() => {
    if (!isOpen || paymentStatus !== 'waiting') return;

    const pollStatus = async () => {
      try {
        // 调用订单状态API
        const statusResponse = await TradeService.getTradeStatus(tradeNo);
        // 注意：接口返回的是一个包含data属性的对象，而data才是实际的订单状态值
        // 使用类型断言告诉TypeScript这是什么类型
        const orderStatus = (statusResponse as any).data as number;
        if (orderStatus === 10) { // 10 表示已支付
          setPaymentStatus('success');
          if (pollInterval.current) {
            clearInterval(pollInterval.current);
          }
        }
      } catch (err) {
        console.error('轮询订单状态失败:', err);
      }
    };

    // 每3秒轮询一次
    pollInterval.current = setInterval(pollStatus, 3000);

    return () => {
      if (pollInterval.current) {
        clearInterval(pollInterval.current);
      }
    };
  }, [isOpen, tradeNo, paymentStatus]);

  useEffect(() => {
    if (!isOpen || !aliPayBody) return;

    setIsLoading(true);
    setError(null);

    setTimeout(() => {
      if (!aliPayQrCodeRef.current) return;

      const iframe = document.createElement('iframe');
      iframe.width = '180px';
      iframe.height = '185px';
      aliPayQrCodeRef.current.appendChild(iframe);

      iframe.onload = () => {
        if (!iframe.contentWindow) return;
        iframe.contentWindow.document.body.innerHTML = aliPayBody;
        const form = iframe.contentWindow.document.getElementsByTagName('form')[0];
        if (form) {
          form.submit();
        }
        setIsLoading(false);
      };
    }, 10);
  }, [isOpen, aliPayBody]);

  // 支付成功后自动关闭
  useEffect(() => {
    if (paymentStatus === 'success') {
      const timer = setTimeout(() => {
        onClose();
        window.location.href = '/profile';
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [paymentStatus, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-xl max-w-md w-full p-6 relative">
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
        >
          <FontAwesomeIcon icon={faTimes} />
        </button>

        {/* 标题 */}
        <div className="text-center mb-6">
          <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-blue-100 mb-4">
            <FontAwesomeIcon icon={faAlipay} size="lg" color="#469DE2" />
          </div>
          <h2 className="text-2xl font-bold text-gray-800">支付宝支付</h2>
        </div>

        {/* 商品信息 */}
        <div className="mb-6">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-gray-600">商品名称</span>
              <span className="text-gray-800 font-medium">{productName}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">支付金额</span>
              <span className="text-xl font-bold text-blue-600">
                ¥{(typeof price === 'number' ? price / 100 : 0).toFixed(2)}
              </span>
            </div>
          </div>
        </div>

        {/* 支付状态 */}
        {paymentStatus === 'success' ? (
          <div className="text-center py-6">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-4">
              <FontAwesomeIcon icon={faCheck} size="lg" color="#10B981" />
            </div>
            <h3 className="text-xl font-bold text-green-600 mb-2">支付成功</h3>
            <p className="text-gray-600">正在跳转到个人中心...</p>
          </div>
        ) : (
          <>
            <div ref={aliPayQrCodeRef} className="flex items-center justify-center"></div>
            {isLoading ? (
              <div className="text-center py-6">
                <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p className="text-gray-600">正在加载支付页面...</p>
              </div>
            ) : error ? (
              <div className="text-center py-6">
                <div className="text-red-500 mb-4">{error}</div>
                <button
                  onClick={onClose}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
                >
                  关闭
                </button>
              </div>
            ) : (
              <div className="text-center">
                <p className="text-gray-600 mb-2">请在支付宝中完成支付</p>
                <p className="text-gray-500 text-sm">支付完成后将自动跳转</p>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}

// 分离出依赖于URL参数的组件
function PaymentContent() {
  const searchParams = useSearchParams();
  const [paymentMethod, setPaymentMethod] = useState<'alipay' | 'wechat'>('alipay');
  const [isMounted, setIsMounted] = useState(false);

  // 添加微信支付弹窗的状态
  const [showWechatModal, setShowWechatModal] = useState(false);
  const [showAlipayModal, setShowAlipayModal] = useState(false);
  const [tradeNo, setTradeNo] = useState<string>('');
  const [payUrl, setPayUrl] = useState<string>('')
  const [tradeInfo, setTradeInfo] = useState<TradeInfo | null>(null)

  // 从URL参数获取订单信息
  const plan = searchParams?.get('plan') || 'basic'; // 默认为基础版
  const billingType = searchParams?.get('billingType') || 'monthly'; // 默认为月付
  const price = parseFloat(searchParams?.get('price') || '0'); // 默认为0
  const planId = searchParams?.get('planId') || '';
  const upgrade = searchParams?.get('upgrade') || '0';
  const platform = searchParams?.get('platform'); // 默认为支付宝
  const months = searchParams?.get('months') || '1'; // 默认为1个月
  const isContinuousMonthlySubscription = searchParams?.get('isContinuousMonthlySubscription') || '0'; // 默认非连续包月

  useEffect(() => {
    setIsMounted(true);
    // 初始化支付方式根据platform参数
    setPaymentMethod(platform === '10' ? 'alipay' : 'wechat');
    
    TradeService.getPrepayTradeNo({
      planId: Number(planId), 
      platform: platform === '10' ? 10 : 23, 
      upgrade: upgrade === '1' ? 1 : 0,
      months: Number(months),
      isContinuousMonthlySubscription: isContinuousMonthlySubscription === '1' ? 1 : 0
    }).then(resp => {
      setTradeInfo(resp.data)
      console.log(resp.data)
      
      // 自动弹窗展示支付二维码
      if (resp.data) {
        // 仅当platform参数有值且非空时才自动弹出支付窗口
        if (platform && platform.trim() !== '') {
          if (platform === '10') {
            setShowAlipayModal(true);
          } else {
            setPayUrl(resp.data.payUrl || '');
            setShowWechatModal(true);
          }
        }
      }
    })
  }, []);

  // 根据参数构建商品名称和描述
  const getProductName = () => {
    const planName = plan === 'basic' ? '基础版' : plan === 'pro' ? 'Pro版' : 'Ultra版';
    const billingLabel = tradeInfo?.isUpgrade === 1 ? '（升级）' : '';
    return `信竞星球 ${planName}会员${billingLabel}`;
  };

  const getProductDescription = () => {
    if (tradeInfo?.isUpgrade === 1) {
      return tradeInfo.upgradeDays + "天";
    }
    
    // 使用TradeInfo返回的months信息
    if (tradeInfo?.months) {
      const months = tradeInfo.months;
      if (months === 1) return "30天会员服务";
      if (months === 3) return "90天会员服务";
      if (months === 6) return "180天会员服务";
      if (months === 12) return "365天会员服务";
      return months + "个月会员服务";
    }
    
    // 兼容旧版本
    if (billingType === 'monthly') {
      return "1个月会员服务";
    } else if (billingType === 'quarterly') {
      return "3个月会员服务";
    } else if (billingType === 'semiannually') {
      return "6个月会员服务";
    } else {
      return "12个月会员服务";
    }
  };

  // 切换支付方式
  const handlePaymentMethodChange = (method: 'alipay' | 'wechat') => {
    // 如果是连续包月，不允许切换到微信支付
    if (tradeInfo?.isContinuousMonthlySubscription === 1 && method === 'wechat') {
      return;
    }
    setPaymentMethod(method);
  };

  const openAliPay = (body: string) => {
    const div = document.createElement('div')
    div.innerHTML = body
    div.getElementsByTagName('form')[0].submit()

    // 创建一个新的窗口
    let newWindow = window.open('', '_blank');

    // 检查新窗口是否被阻止弹出（用户可能禁用了弹出窗口）
    if (newWindow && !newWindow.closed) {
      // 将form表单写入新窗口
      newWindow.document.write(body);
      // 提交表单
      newWindow.document.forms[0].submit();
    } else {
      // 如果窗口被阻止，则可以在当前页面提交表单或提示用户允许弹出窗口
      console.warn('请允许弹出窗口以便继续支付');
      // 或者尝试在当前页面提交表单
      // document.body.innerHTML += res.data.resultStr;
      // document.forms[0].submit();
    }
  }

  // 获取微信二维码
  useEffect(() => {
    if (showWechatModal && tradeInfo) {
      if (!tradeInfo.payUrl) {
        // 如果payUrl为空，重新调用接口获取
        TradeService.getPrepayTradeNo({
          planId: Number(planId), 
          platform: 23, // 微信支付
          upgrade: upgrade === '1' ? 1 : 0,
          months: Number(months),
          isContinuousMonthlySubscription: isContinuousMonthlySubscription === '1' ? 1 : 0
        }).then(resp => {
          setTradeInfo(resp.data);
          setPayUrl(resp.data.payUrl || '');
        });
      } else {
        setPayUrl(tradeInfo.payUrl || '');
      }
    }
  }, [showWechatModal, tradeInfo]);

  // 处理确认付款
  const handleConfirmPayment = () => {
    // 如果是连续包月且选择了微信支付，自动切换为支付宝支付
    if (tradeInfo?.isContinuousMonthlySubscription === 1 && paymentMethod === 'wechat') {
      handlePaymentMethodChange('alipay');
    }
    
    if (paymentMethod === 'alipay') {
      // 支付宝支付逻辑 - 显示弹窗
      if (!tradeInfo?.aliPayBody) {
        // 如果aliPayBody为空，重新调用接口获取
        TradeService.getPrepayTradeNo({
          planId: Number(planId), 
          platform: 10, // 支付宝
          upgrade: upgrade === '1' ? 1 : 0,
          months: Number(months),
          isContinuousMonthlySubscription: isContinuousMonthlySubscription === '1' ? 1 : 0
        }).then(resp => {
          setTradeInfo(resp.data);
          setShowAlipayModal(true);
        });
      } else {
        setShowAlipayModal(true);
      }
    } else {
      // 微信支付逻辑 - 显示二维码弹窗
      setShowWechatModal(true);
    }
  };

  // 如果没有挂载，返回一个静态的UI，避免水合错误
  if (!isMounted || tradeInfo == null) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
          <div className="p-6 bg-indigo-50 border-b border-indigo-100">
            <h2 className="text-xl font-bold text-gray-800">订单信息</h2>
          </div>
          <div className="p-6">
            <div className="mb-8">
              <div className="flex justify-between items-center pb-4 border-b border-gray-200">
                <span className="text-gray-700 font-medium">商品</span>
                <span className="text-gray-700 font-medium">金额</span>
              </div>
              <div className="py-4 border-b border-gray-100">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800">加载中...</h3>
                    <p className="text-gray-600 text-sm">加载中...</p>
                  </div>
                  <span className="text-xl font-bold text-indigo-600">¥0.00</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 获取会员时长显示文本
  const getMembershipDurationText = () => {
    if (tradeInfo.isUpgrade === 1) {
      return `${tradeInfo.upgradeDays}天`;
    }
    
    if (tradeInfo.months) {
      if (tradeInfo.months === 1) return '30天';
      if (tradeInfo.months === 3) return '90天';
      if (tradeInfo.months === 6) return '180天';
      if (tradeInfo.months === 12) return '365天';
      return `${tradeInfo.months}个月`;
    }
    
    return '';
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
        {/* 订单信息 */}
        <div className="p-6 bg-indigo-50 border-b border-indigo-100">
          <h2 className="text-xl font-bold text-gray-800">订单信息</h2>
        </div>

        {/* 添加订单号和倒计时 */}
        <div className="px-6 py-4 border-b border-gray-100">
          <div className="flex justify-between items-center mb-2">
            <span className="text-gray-600">订单号</span>
            <span className="text-gray-800 font-medium">{tradeInfo.tradeNo}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">支付剩余时间</span>
            <CountdownTimer expireAt={tradeInfo.expireAt} />
          </div>
          {/* 更新会员时长显示 */}
          <div className="flex justify-between items-center mt-2">
            <span className="text-gray-600">会员时长</span>
            <span className="text-gray-800 font-medium">
              {getMembershipDurationText()}
              {tradeInfo.isContinuousMonthlySubscription === 1 && 
                <span className="ml-2 text-xs text-white bg-indigo-500 px-2 py-0.5 rounded-full">自动续费</span>
              }
            </span>
          </div>
        </div>
        <div className="p-6">
          {/* 订单详情 */}
          <div className="mb-8">
            <div className="flex justify-between items-center pb-4 border-b border-gray-200">
              <span className="text-gray-700 font-medium">商品</span>
              <span className="text-gray-700 font-medium">金额</span>
            </div>

            <div className="py-4 border-b border-gray-100">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-lg font-semibold text-gray-800">{getProductName()}</h3>
                  <p className="text-gray-600 text-sm">{getProductDescription()}</p>
                  
                  {/* 显示连续包月信息 */}
                  {tradeInfo && tradeInfo.isContinuousMonthlySubscription === 1 && (
                    <p className="text-indigo-600 text-sm mt-1 flex items-center">
                      <FontAwesomeIcon icon={faSync} className="mr-1" />
                      自动续费服务，可随时取消 
                      <span className="ml-2 text-xs text-red-600 flex items-center">
                        <FontAwesomeIcon icon={faInfoCircle} className="mr-1" />
                        享5%优惠
                      </span>
                    </p>
                  )}
                </div>
                <span className="text-xl font-bold text-indigo-600">¥{(tradeInfo.tradeFee / 100).toFixed(2)}</span>
              </div>
            </div>

            <div className="flex justify-between items-center pt-4">
              <span className="text-lg font-semibold text-gray-800">应付总额</span>
              <span className="text-2xl font-bold text-indigo-700">¥{(tradeInfo.tradeFee / 100).toFixed(2)}</span>
            </div>
          </div>

          {/* 支付方式选择 */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">选择支付方式</h3>
            <div className="space-y-4">
              {/* 支付宝 */}
              <div
                className={`border ${paymentMethod === 'alipay' ? 'border-indigo-300' : 'border-gray-200'} hover:border-indigo-300 rounded-lg p-4 cursor-pointer payment-option`}
                onClick={() => handlePaymentMethodChange('alipay')}
              >
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3">
                    <div className={`w-5 h-5 rounded-full ${paymentMethod === 'alipay' ? 'bg-blue-500' : 'bg-transparent'}`}></div>
                  </div>
                  <div className="flex items-center">
                    <FontAwesomeIcon icon={faAlipay} size="2xl" width={50} color="#469DE2" />
                    <span className="text-gray-800 font-medium">支付宝</span>
                  </div>
                </div>
              </div>

              {/* 微信支付 */}
              <div
                className={`border ${paymentMethod === 'wechat' ? 'border-indigo-300' : 'border-gray-200'} hover:border-indigo-300 rounded-lg p-4 cursor-pointer payment-option ${tradeInfo && tradeInfo.isContinuousMonthlySubscription === 1 ? 'opacity-50 pointer-events-none' : ''}`}
                onClick={() => handlePaymentMethodChange('wechat')}
              >
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3">
                    <div className={`w-5 h-5 rounded-full ${paymentMethod === 'wechat' ? 'bg-blue-500' : 'bg-transparent'}`}></div>
                  </div>
                  <div className="flex items-center">
                    <FontAwesomeIcon icon={faWeixin} size="2xl" width={50} color="#57BE6A" />
                    <span className="text-gray-800 font-medium">微信支付</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 支付提示与注意事项 */}
          <div className="text-sm text-gray-600 space-y-2">
            <p><FontAwesomeIcon icon={faInfoCircle} className="text-indigo-500 mr-2" />付款成功后系统将自动为您开通会员服务</p>
            <p><FontAwesomeIcon icon={faInfoCircle} className="text-indigo-500 mr-2" />如遇到支付问题，请联系客服 13423490835</p>
          </div>
        </div>
      </div>

      {/* 底部按钮 */}
      <div className="flex justify-between items-center">
        <Link href="/membership" className="flex items-center text-indigo-600 hover:text-indigo-800">
          <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
          <span>返回会员页面</span>
        </Link>
        <div className="flex space-x-4">
          <button
            className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-100"
            onClick={() => window.history.back()}
          >
            取消订单
          </button>
          <button
            onClick={handleConfirmPayment}
            className="px-6 py-3 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700"
          >
            立即支付
          </button>
        </div>
      </div>

      {/* 支付宝支付弹窗 */}
      <AlipayModal
        isOpen={showAlipayModal}
        onClose={() => setShowAlipayModal(false)}
        productName={getProductName()}
        price={tradeInfo.tradeFee}
        aliPayBody={tradeInfo?.aliPayBody}
        tradeNo={tradeInfo?.tradeNo || ''}
      />

      {/* 微信支付弹窗 */}
      <WechatPayModal
        isOpen={showWechatModal}
        onClose={() => setShowWechatModal(false)}
        productName={getProductName()}
        price={tradeInfo.tradeFee}
        payUrl={payUrl}
        tradeNo={tradeInfo?.tradeNo || ''}
      />
    </div>
  );
}

// 加载中状态的占位组件
function LoadingPayment() {
  return (
    <div className="max-w-4xl mx-auto text-center py-12">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500 mx-auto mb-4"></div>
      <p className="text-gray-600">正在加载支付信息...</p>
    </div>
  );
}

export default function Payment() {
  return (
    <div className="bg-gray-50 min-h-screen">
      {/* 主要内容 */}
      <div className="container mx-auto px-4 py-12">
        {/* 页面标题 */}
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-gray-800 mb-4">确认付款</h1>
          <p className="text-gray-600 max-w-2xl mx-auto">请选择您喜欢的支付方式完成订单</p>
        </div>

        {/* 使用Suspense包装使用useSearchParams的组件 */}
        <Suspense fallback={<LoadingPayment />}>
          <PaymentContent />
        </Suspense>
      </div>
    </div>
  );
}
