'use client'

import CreateTeamForm from '@/app/components/team/CreateTeamForm'
import Link from 'next/link'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faArrowLeft } from '@fortawesome/free-solid-svg-icons'

export default function CreateTeamPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-3xl mx-auto">
        {/* 标题 */}
        <div className="mb-8">
          <Link 
            href="/teams" 
            className="text-indigo-600 hover:text-indigo-800 mb-2 inline-block"
          >
            <FontAwesomeIcon icon={faArrowLeft} className="mr-1" />
            返回团队页面
          </Link>
          <h1 className="text-3xl font-bold text-gray-800 mt-2">创建团队</h1>
          <p className="text-gray-600 mt-1">创建一个团队与小伙伴一起学习编程</p>
        </div>

        {/* 创建表单 */}
        <div className="bg-white rounded-xl shadow-sm overflow-hidden mb-8">
          <div className="p-6">
            <CreateTeamForm />
          </div>
        </div>
      </div>
    </div>
  )
} 