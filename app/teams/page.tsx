'use client'

import { useState, useEffect } from 'react'
import TeamCard from '@/app/components/team/TeamCard'
import TeamActivity from '@/app/components/team/TeamActivity'
import TeamStats from '@/app/components/team/TeamStats'
import { Team, TeamActivity as TeamActivityType, TeamStats as TeamStatsType } from '@/app/types/team'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faSearch, 
  faPlus, 
  faLaptopCode, 
  faCode,
  faGraduationCap,
  faClipboardList,
  faUserPlus,
  faFileAlt,
  faClock
} from '@fortawesome/free-solid-svg-icons'
import Link from 'next/link'
import teamService from '@/app/service/team-service'
import { TeamView } from '@/app/service/team-service'

const mockActivities: TeamActivityType[] = [
  {
    id: '1',
    type: 'homework',
    title: '信竞训练营发布了新作业',
    description: '《二叉树遍历算法》作业已发布，请在11月20日前完成。',
    time: '2小时前',
    icon: faClipboardList,
    iconBgColor: 'bg-blue-100',
    iconTextColor: 'text-blue-600',
    link: '#'
  },
  {
    id: '2',
    type: 'member',
    title: '张三加入了算法进阶班',
    description: '欢迎新成员加入！',
    time: '昨天',
    icon: faUserPlus,
    iconBgColor: 'bg-green-100',
    iconTextColor: 'text-green-600'
  },
  {
    id: '3',
    type: 'problem',
    title: 'Python学习小组发布了新题单',
    description: '《Python基础练习100题》题单已更新，请查收。',
    time: '2天前',
    icon: faFileAlt,
    iconBgColor: 'bg-purple-100',
    iconTextColor: 'text-purple-600',
    link: '#'
  },
  {
    id: '4',
    type: 'exam',
    title: '算法进阶班将开始模拟考试',
    description: '《算法期中测试》将于11月25日开始，请做好准备。',
    time: '3天前',
    icon: faClock,
    iconBgColor: 'bg-yellow-100',
    iconTextColor: 'text-yellow-600',
    link: '#'
  }
]

const mockStats: TeamStatsType = {
  joinedTeams: 3,
  createdTeams: 1,
  homeworkCount: 15
}

export default function TeamsPage() {
  const [activeTab, setActiveTab] = useState('my-teams')
  const [teams, setTeams] = useState<Team[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchTeams()
  }, [activeTab])

  const fetchTeams = async () => {
    try {
      setLoading(true)
      const isMy = activeTab === 'my-teams' ? 1 : 0
      const isClosed = activeTab === 'archived' ? 1 : 0
      const response = await teamService.getTeamList({
        pageNum: 1,
        pageSize: 10,
        isMy,
        isClosed
      })
      
      const formattedTeams = response.data.items.map((team: TeamView) => ({
        id: team.visibleTeamId.toString(),
        name: team.name,
        description: team.description,
        memberCount: team.teamUserCount,
        createdAt: team.createdTime,
        isCreator: team.isCreator,
        icon: faLaptopCode,
        gradientColors: getGradientColors(team.visibleTeamId)
      }))
      
      setTeams(formattedTeams)
    } catch (error) {
      console.error('获取团队列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getGradientColors = (teamId: number) => {
    const colors = [
      { from: 'indigo', to: 'teal' },
      { from: 'blue', to: 'teal' },
      { from: 'green', to: 'teal' },
      { from: 'purple', to: 'teal' },
      { from: 'pink', to: 'teal' },
      { from: 'red', to: 'teal' }
    ]
    return colors[teamId % colors.length]
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 标题和操作按钮 */}
      <div className="mb-8 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">团队</h1>
          <p className="text-gray-600 mt-1">加入或创建团队，与小伙伴一起学习编程</p>
        </div>
        <div className="mt-4 md:mt-0 flex">
          <Link 
            href="/teams/create" 
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition flex items-center"
          >
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            创建团队
          </Link>
        </div>
      </div>

      {/* 团队分类标签 */}
      <div className="mb-6 border-b border-gray-200">
        <ul className="flex flex-wrap -mb-px">
          <li className="mr-2">
            <button 
              onClick={() => setActiveTab('my-teams')}
              className={`inline-block py-2 px-4 font-medium ${
                activeTab === 'my-teams' 
                  ? 'text-indigo-600 border-b-2 border-indigo-600' 
                  : 'text-gray-600 hover:text-indigo-600'
              }`}
            >
              我的团队
            </button>
          </li>
          <li className="mr-2">
            <button 
              onClick={() => setActiveTab('created')}
              className={`inline-block py-2 px-4 font-medium ${
                activeTab === 'created' 
                  ? 'text-indigo-600 border-b-2 border-indigo-600' 
                  : 'text-gray-600 hover:text-indigo-600'
              }`}
            >
              我创建的
            </button>
          </li>
          <li className="mr-2">
            <button 
              onClick={() => setActiveTab('archived')}
              className={`inline-block py-2 px-4 font-medium ${
                activeTab === 'archived' 
                  ? 'text-indigo-600 border-b-2 border-indigo-600' 
                  : 'text-gray-600 hover:text-indigo-600'
              }`}
            >
              已归档
            </button>
          </li>
        </ul>
      </div>

      {/* 搜索和加入团队 */}
      <div className="bg-white rounded-xl shadow-sm p-6 mb-8">
        <div className="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
          <div className="flex-grow">
            <div className="relative">
              <input 
                type="text" 
                placeholder="输入团队ID或邀请码加入团队" 
                className="w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              />
              <button className="absolute right-2 top-2 text-gray-400 hover:text-indigo-600">
                <FontAwesomeIcon icon={faSearch} />
              </button>
            </div>
          </div>
          <button className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition">
            加入团队
          </button>
        </div>
      </div>

      {/* 团队统计卡片 */}
      <TeamStats stats={mockStats} />

      {/* 团队列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {loading ? (
          <div className="col-span-full text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">加载中...</p>
          </div>
        ) : teams.length === 0 ? (
          <div className="col-span-full text-center py-8">
            <p className="text-gray-600">暂无团队</p>
          </div>
        ) : (
          teams.map(team => (
            <TeamCard key={team.id} team={team} />
          ))
        )}
      </div>

      {/* 团队活动动态 */}
      <div className="bg-white rounded-xl shadow-sm overflow-hidden mb-8">
        <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100">
          <h3 className="text-lg font-semibold text-gray-800">团队最近活动</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {mockActivities.map(activity => (
              <TeamActivity key={activity.id} activity={activity} />
            ))}
          </div>
        </div>
      </div>
    </div>
  )
} 