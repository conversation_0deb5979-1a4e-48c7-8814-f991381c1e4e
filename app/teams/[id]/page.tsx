'use client'

import { useParams } from 'next/navigation'
import TeamAnnouncement from '@/app/components/team/TeamAnnouncement'
import TeamContentGrid from '@/app/components/team/TeamContentGrid'
import TeamMemberList from '@/app/components/team/TeamMemberList'
import TeamStatistics from '@/app/components/team/TeamStatistics'
import { useTeam } from '@/app/contexts/TeamContext'

export default function TeamDetailPage() {
  const params = useParams()
  const teamId = params.id as string
  const { team } = useTeam()

  console.log('TeamDetailPage rendered with team:', team)

  // 如果 team 未定义，显示加载状态
  if (!team) {
    console.log('Team is undefined, showing loading state')
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 团队公告 */}
      <TeamAnnouncement teamId={teamId} notice={team.notice} />

      {/* 内容卡片网格 */}
      <TeamContentGrid 
        teamId={teamId} 
        homeworkCount={team.homeworkCount}
        problemListCount={team.problemListCount}
      />

      {/* 团队成员列表 */}
      <TeamMemberList teamId={teamId} />

      {/* 团队统计 */}
      <TeamStatistics teamId={teamId} />
    </div>
  )
} 