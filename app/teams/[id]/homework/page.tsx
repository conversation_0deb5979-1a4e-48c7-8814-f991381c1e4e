'use client'

import { useState, useEffect } from 'react'
import { useParams, useRouter } from 'next/navigation'
import TeamHeader from '@/app/components/team/TeamHeader'
import TeamNavigation from '@/app/components/team/TeamNavigation'
import HomeworkList from '@/app/components/team/homework/HomeworkList'
import HomeworkFilter from '@/app/components/team/homework/HomeworkFilter'
import { TeamHomework } from '@/app/types/team'

export default function TeamHomeworksPage() {
  const params = useParams()
  const router = useRouter()
  const teamId = params.id as string
  const [homeworks, setHomeworks] = useState<TeamHomework[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [filter, setFilter] = useState<'all' | 'ongoing' | 'ended' | 'graded'>('all')

  useEffect(() => {
    // TODO: 从API获取作业数据
    setIsLoading(true)
    
    // 模拟数据
    const mockHomeworks: TeamHomework[] = [
      {
        id: '1',
        title: '二叉树遍历算法实现',
        description: '实现前序、中序、后序三种遍历算法，并分析时间复杂度。',
        dueDate: '2023-11-20',
        submissionCount: 12,
        totalMembers: 18,
        status: 'ongoing'
      },
      {
        id: '2',
        title: '排序算法练习',
        description: '实现快速排序和归并排序，并比较两种排序算法的优缺点。',
        dueDate: '2023-11-15',
        submissionCount: 16,
        totalMembers: 18,
        status: 'graded'
      },
      {
        id: '3',
        title: '动态规划入门题',
        description: '完成5道动态规划经典题目，包括背包问题、最长公共子序列等。',
        dueDate: '2023-11-10',
        submissionCount: 15,
        totalMembers: 18,
        status: 'graded'
      },
      {
        id: '4',
        title: '图论算法基础',
        description: '实现DFS、BFS、最短路径算法，并解决相关应用题。',
        dueDate: '2023-11-30',
        submissionCount: 5,
        totalMembers: 18,
        status: 'ongoing'
      },
      {
        id: '5',
        title: '贪心算法专题',
        description: '学习贪心策略，解决区间调度、哈夫曼编码等问题。',
        dueDate: '2023-11-05',
        submissionCount: 18,
        totalMembers: 18,
        status: 'ended'
      }
    ]
    
    // 模拟API延迟
    setTimeout(() => {
      setHomeworks(mockHomeworks)
      setIsLoading(false)
    }, 500)
  }, [teamId])

  const filteredHomeworks = homeworks.filter(
    homework => filter === 'all' || homework.status === filter
  )

  return (
    <div className="min-h-screen bg-gray-50">

      {/* 主要内容 */}
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">团队作业</h1>
            <p className="text-gray-600 mt-1">管理和查看团队作业提交情况</p>
          </div>
          <div className="mt-4 lg:mt-0">
            <button 
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition flex items-center"
              onClick={() => router.push(`/teams/${teamId}/homework/create`)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              创建作业
            </button>
          </div>
        </div>

        {/* 筛选器 */}
        <HomeworkFilter
          currentFilter={filter}
          onFilterChange={setFilter}
          homeworkCount={{
            all: homeworks.length,
            ongoing: homeworks.filter(h => h.status === 'ongoing').length,
            ended: homeworks.filter(h => h.status === 'ended').length,
            graded: homeworks.filter(h => h.status === 'graded').length
          }}
        />

        {/* 作业列表 */}
        <HomeworkList 
          homeworks={filteredHomeworks} 
          isLoading={isLoading} 
          teamId={teamId} 
        />
      </div>
    </div>
  )
} 