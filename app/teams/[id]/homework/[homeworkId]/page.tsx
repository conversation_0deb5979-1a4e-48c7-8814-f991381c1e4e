'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import TeamHeader from '@/app/components/team/TeamHeader'
import TeamNavigation from '@/app/components/team/TeamNavigation'
import HomeworkDetail from '@/app/components/team/homework/HomeworkDetail'
import SubmissionStatus from '@/app/components/team/homework/SubmissionStatus'
import { TeamHomework } from '@/app/types/team'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faArrowLeft } from '@fortawesome/free-solid-svg-icons'

interface HomeworkSubmission {
  id: string
  studentName: string
  studentId: string
  submitTime: string
  status: 'pending' | 'graded'
  score?: number
  feedback?: string
}

export default function HomeworkDetailPage() {
  const params = useParams()
  const teamId = params.id as string
  const homeworkId = params.homeworkId as string
  
  const [homework, setHomework] = useState<TeamHomework | null>(null)
  const [submissions, setSubmissions] = useState<HomeworkSubmission[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // TODO: 从API获取作业详情
    setIsLoading(true)
    
    // 模拟数据
    const mockHomework: TeamHomework = {
      id: homeworkId,
      title: '二叉树遍历算法实现',
      description: `
# 二叉树遍历算法实现

## 目标
理解并实现三种基本的二叉树遍历算法：前序遍历、中序遍历和后序遍历。

## 要求
1. 定义一个二叉树节点结构
2. 实现递归版本的三种遍历算法
3. 实现非递归（迭代）版本的三种遍历算法
4. 对每种算法进行时间复杂度和空间复杂度分析
5. 测试你的算法，确保它们能够正确工作

## 提交内容
- 源代码文件（.cpp, .java, .py等）
- 一份简短的报告，说明你的实现和复杂度分析

## 参考资料
- [二叉树的遍历](https://www.geeksforgeeks.org/tree-traversals-inorder-preorder-and-postorder/)
- [非递归实现二叉树遍历](https://www.geeksforgeeks.org/iterative-preorder-traversal/)
      `,
      dueDate: '2023-11-20',
      submissionCount: 12,
      totalMembers: 18,
      status: 'ongoing'
    }
    
    const mockSubmissions: HomeworkSubmission[] = [
      {
        id: '1',
        studentName: '王小明',
        studentId: 'S001',
        submitTime: '2023-11-15T16:32:45',
        status: 'graded',
        score: 92,
        feedback: '实现非常好，但缺少对迭代算法的空间复杂度分析。'
      },
      {
        id: '2',
        studentName: '李小红',
        studentId: 'S002',
        submitTime: '2023-11-16T09:15:22',
        status: 'pending'
      },
      {
        id: '3',
        studentName: '张小强',
        studentId: 'S003',
        submitTime: '2023-11-14T21:05:18',
        status: 'graded',
        score: 85,
        feedback: '递归实现很清晰，但非递归实现有些问题。'
      }
    ]
    
    // 模拟API延迟
    setTimeout(() => {
      setHomework(mockHomework)
      setSubmissions(mockSubmissions)
      setIsLoading(false)
    }, 500)
  }, [teamId, homeworkId])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <div className="inline-block w-12 h-12 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
            <p className="ml-4 text-gray-500">正在加载作业详情...</p>
          </div>
        </div>
      </div>
    )
  }

  if (!homework) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="bg-white rounded-xl shadow-sm p-8 text-center">
            <h3 className="text-lg font-medium text-gray-800 mb-2">作业未找到</h3>
            <p className="text-gray-500">
              无法找到该作业，它可能已被删除或您没有访问权限。
            </p>
            <Link 
              href={`/teams/${teamId}/homework`}
              className="mt-4 inline-flex items-center text-indigo-600 hover:text-indigo-800"
            >
              <FontAwesomeIcon icon={faArrowLeft} className="mr-1" />
              返回作业列表
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link 
            href={`/teams/${teamId}/homework`}
            className="text-indigo-600 hover:text-indigo-800 flex items-center w-fit"
          >
            <FontAwesomeIcon icon={faArrowLeft} className="mr-1" />
            返回作业列表
          </Link>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 作业详情 */}
          <div className="lg:col-span-2">
            <HomeworkDetail homework={homework} />
          </div>
          
          {/* 提交状态 */}
          <div className="lg:col-span-1">
            <SubmissionStatus 
              submissions={submissions} 
              teamId={teamId} 
              homeworkId={homeworkId} 
            />
          </div>
        </div>
      </div>
    </div>
  )
} 