'use client'

import { useState } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { Search, Trash2, Plus, Edit2, Check, X, Upload, CloudUpload, ChevronDown, AlertCircle, X as XIcon } from 'lucide-react'
import QuestionSelectorModal from '@/app/components/QuestionSelectorModal'
import HomeworkService from '@/app/service/homework-service'
import DatePicker from 'react-datepicker'
import 'react-datepicker/dist/react-datepicker.css'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'

// 题目类型定义
type QuestionType = 'single' | 'multiple' | 'judge' | 'programming'

interface Question {
  id: string
  title: string
  type: QuestionType
  difficulty: 'easy' | 'medium' | 'hard'
  defaultPoints: number
  points: number
  source: '公共题库' | '团队题库'
  options?: Array<{
    label: string
    content: string
    isCorrect: boolean
  }>
  description?: string
  inputExample?: string
  outputExample?: string
  tags?: string[]
}

export default function CreateHomeworkPage() {
  const params = useParams()
  const router = useRouter()
  const teamId = params.id as string
  
  // 表单状态
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [dueDate, setDueDate] = useState<Date | undefined>(undefined)
  const [selectedQuestions, setSelectedQuestions] = useState<Question[]>([])
  const [editingPointsId, setEditingPointsId] = useState<string | null>(null)
  const [tempPoints, setTempPoints] = useState<number>(0)
  const [files, setFiles] = useState<File[]>([])
  const [showBasicQuestionModal, setShowBasicQuestionModal] = useState(false)
  const [showProgrammingQuestionModal, setShowProgrammingQuestionModal] = useState(false)
  const [advancedSettings, setAdvancedSettings] = useState({
    allowLateSubmission: false,
    latePenalty: 20,
    enableTimeLimit: false,
    timeLimit: 60,
    showResultsImmediately: false,
    allowMultipleSubmissions: false,
    notifyAllStudents: false,
    enableCheatingDetection: false
  })
  
  // 模拟公共题库数据
  const publicQuestions: Omit<Question, 'points'>[] = [
    { 
      id: 'p1', 
      title: '什么是时间复杂度？', 
      type: 'single', 
      difficulty: 'easy', 
      defaultPoints: 5, 
      source: '公共题库',
      options: [
        { label: 'A', content: '算法执行所需的存储空间', isCorrect: false },
        { label: 'B', content: '算法执行所需的时间', isCorrect: false },
        { label: 'C', content: '算法执行所需的基本操作数量级', isCorrect: true },
        { label: 'D', content: '算法中的循环次数', isCorrect: false },
      ],
      tags: ['算法', '基础概念']
    },
    { 
      id: 'p2', 
      title: '以下哪些是线性数据结构？', 
      type: 'multiple', 
      difficulty: 'medium', 
      defaultPoints: 10, 
      source: '公共题库',
      options: [
        { label: 'A', content: '数组', isCorrect: true },
        { label: 'B', content: '链表', isCorrect: true },
        { label: 'C', content: '二叉树', isCorrect: false },
        { label: 'D', content: '栈', isCorrect: true },
      ],
      tags: ['数据结构', '线性结构']
    },
    { 
      id: 'p3', 
      title: '快速排序的平均时间复杂度是O(n²)？', 
      type: 'judge', 
      difficulty: 'easy', 
      defaultPoints: 5, 
      source: '公共题库',
      options: [
        { label: '对', content: '', isCorrect: false },
        { label: '错', content: '', isCorrect: true },
      ],
      tags: ['算法', '排序算法']
    },
    { 
      id: 'p4', 
      title: '实现一个二叉树的中序遍历', 
      type: 'programming', 
      difficulty: 'medium', 
      defaultPoints: 15, 
      source: '公共题库',
      description: '请实现一个函数，对给定的二叉树进行中序遍历，并返回遍历结果数组。',
      inputExample: 'root = [1, null, 2, 3]',
      outputExample: '[1, 3, 2]',
      tags: ['数据结构', '二叉树', '遍历']
    },
    { 
      id: 'p5', 
      title: '使用动态规划解决背包问题', 
      type: 'programming', 
      difficulty: 'hard', 
      defaultPoints: 20, 
      source: '公共题库',
      description: '给定一组物品，每种物品都有自己的重量和价值，在限定的总重量内，选择若干物品使得物品的总价值最高。',
      inputExample: 'weights = [2, 3, 4, 5], values = [3, 4, 5, 6], maxWeight = 8',
      outputExample: '10',
      tags: ['算法', '动态规划', '背包问题']
    },
  ]
  
  // 模拟团队题库数据
  const teamQuestions: Omit<Question, 'points'>[] = [
    { 
      id: 't1', 
      title: '数组和链表的区别', 
      type: 'single', 
      difficulty: 'easy', 
      defaultPoints: 5, 
      source: '团队题库',
      options: [
        { label: 'A', content: '数组支持随机访问，链表不支持', isCorrect: true },
        { label: 'B', content: '链表支持随机访问，数组不支持', isCorrect: false },
        { label: 'C', content: '数组和链表都不支持随机访问', isCorrect: false },
        { label: 'D', content: '数组和链表都支持随机访问', isCorrect: false },
      ],
      tags: ['数据结构', '基础概念']
    },
    { 
      id: 't2', 
      title: '常见排序算法的性能比较', 
      type: 'multiple', 
      difficulty: 'medium', 
      defaultPoints: 10, 
      source: '团队题库',
      options: [
        { label: 'A', content: '冒泡排序的平均时间复杂度是O(n²)', isCorrect: true },
        { label: 'B', content: '快速排序的平均时间复杂度是O(n log n)', isCorrect: true },
        { label: 'C', content: '堆排序的平均时间复杂度是O(n)', isCorrect: false },
        { label: 'D', content: '归并排序的空间复杂度是O(1)', isCorrect: false },
      ],
      tags: ['算法', '排序算法', '复杂度分析']
    },
    { 
      id: 't3', 
      title: 'BFS适合解决最短路径问题？', 
      type: 'judge', 
      difficulty: 'easy', 
      defaultPoints: 5, 
      source: '团队题库',
      options: [
        { label: '对', content: '', isCorrect: true },
        { label: '错', content: '', isCorrect: false },
      ],
      tags: ['算法', '图论', '搜索算法']
    },
    { 
      id: 't4', 
      title: '实现一个LRU缓存', 
      type: 'programming', 
      difficulty: 'hard', 
      defaultPoints: 20, 
      source: '团队题库',
      description: '请实现一个LRU（最近最少使用）缓存机制，要求在O(1)时间复杂度内完成get和put操作。',
      inputExample: 'LRUCache cache = new LRUCache(2);\ncache.put(1, 1);\ncache.put(2, 2);\ncache.get(1);',
      outputExample: '1',
      tags: ['数据结构', '设计', '哈希表']
    },
  ]
  
  // 从作业中移除题目
  const removeQuestion = (questionId: string) => {
    setSelectedQuestions(selectedQuestions.filter(q => q.id !== questionId))
  }
  
  // 开始编辑分数
  const startEditingPoints = (questionId: string, currentPoints: number) => {
    setEditingPointsId(questionId)
    setTempPoints(currentPoints)
  }
  
  // 保存分数
  const savePoints = (questionId: string) => {
    if (tempPoints <= 0) {
      alert('分数必须大于0')
      return
    }
    
    setSelectedQuestions(
      selectedQuestions.map(q => 
        q.id === questionId ? { ...q, points: tempPoints } : q
      )
    )
    setEditingPointsId(null)
  }
  
  // 取消编辑分数
  const cancelEditingPoints = () => {
    setEditingPointsId(null)
  }
  
  // 文件上传处理
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files)
      setFiles(prev => [...prev, ...newFiles])
    }
  }
  
  // 删除上传的文件
  const removeFile = (index: number) => {
    setFiles(files.filter((_, i) => i !== index))
  }
  
  // 切换高级设置
  const toggleAdvancedSetting = (key: keyof typeof advancedSettings) => {
    setAdvancedSettings({
      ...advancedSettings,
      [key]: !advancedSettings[key]
    })
  }
  
  // 更新高级设置值
  const updateAdvancedSettingValue = (key: 'latePenalty' | 'timeLimit', value: number) => {
    setAdvancedSettings({
      ...advancedSettings,
      [key]: value
    })
  }
  
  // 添加题目到作业
  const addQuestionToHomework = (question: Omit<Question, 'points'>) => {
    if (!selectedQuestions.some(q => q.id === question.id)) {
      setSelectedQuestions([
        ...selectedQuestions,
        { ...question, points: question.defaultPoints }
      ])
    }
  }
  
  // 计算总分
  const totalPoints = selectedQuestions.reduce((sum, q) => sum + q.points, 0)
  
  // 格式化日期为指定字符串格式
  const formatDateTime = (date: Date | null | undefined): string => {
    if (!date) return ''
    return format(date, 'yyyy-MM-dd HH:mm:ss')
  }
  
  // 提交表单
  const handleSubmit = async (e: React.FormEvent, isDraft: boolean = false) => {
    e.preventDefault()
    
    if (!title.trim()) {
      alert('请输入作业标题')
      return
    }
    
    if (!dueDate) {
      alert('请设置截止日期')
      return
    }
    
    if (selectedQuestions.length === 0) {
      alert('请至少添加一道题目')
      return
    }
    
    try {
      // 格式化数据
      const problems = selectedQuestions.map(q => ({
        problemId: q.id,
        problemType: q.type === 'programming' ? 2 : 1, // 1: 基础题 2: 编程题
        score: q.points
      }))
      
      const formattedDate = formatDateTime(dueDate)
      
      const homeworkData = {
        title,
        endTime: formattedDate,
        description,
        problems,
        enableMultipleSubmit: advancedSettings.allowMultipleSubmissions ? 1 : 0 as 0 | 1,
        status: isDraft ? 0 : 1 as 0 | 1
      }
      
      // 调用接口
      const response = await HomeworkService.createHomework(homeworkData)
      
      // 创建成功后返回作业列表页
      alert(isDraft ? '作业已保存为草稿！' : '作业创建成功！')
      router.push(`/teams/${teamId}/homework`)
    } catch (error) {
      console.error('创建作业失败:', error)
      alert('创建作业失败，请稍后重试')
    }
  }
  
  // 题目类型展示
  const getQuestionTypeText = (type: QuestionType) => {
    switch (type) {
      case 'single': return '单选题'
      case 'multiple': return '多选题'
      case 'judge': return '判断题'
      case 'programming': return '编程题'
      default: return ''
    }
  }
  
  // 难度展示样式
  const getDifficultyBadge = (difficulty: 'easy' | 'medium' | 'hard') => {
    const colors = {
      easy: 'bg-green-100 text-green-800',
      medium: 'bg-yellow-100 text-yellow-800',
      hard: 'bg-red-100 text-red-800'
    }
    
    const labels = {
      easy: '简单',
      medium: '中等',
      hard: '困难'
    }
    
    return (
      <span className={`text-xs px-2 py-1 rounded-full ${colors[difficulty]}`}>
        {labels[difficulty]}
      </span>
    )
  }

  // 添加基础题按钮点击
  const handleAddBasicQuestion = () => {
    setShowBasicQuestionModal(true)
  }

  // 添加编程题按钮点击
  const handleAddProgrammingQuestion = () => {
    setShowProgrammingQuestionModal(true)
  }

  // 获取所有题目（包括公共题库和团队题库）
  const allQuestions = [...publicQuestions, ...teamQuestions]

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
          <div className="px-6 py-4 bg-indigo-50 border-b border-indigo-100">
            <h1 className="text-xl font-bold text-gray-800">创建新作业</h1>
          </div>
          
          <div className="p-6">
            <form onSubmit={(e) => handleSubmit(e, false)}>
              {/* 作业基本信息 */}
              <div className="mb-8">
                <h2 className="text-lg font-semibold text-gray-800 mb-4">基本信息</h2>
                <div className="grid grid-cols-1 gap-6">
                  <div>
                    <label htmlFor="homework-title" className="block text-sm font-medium text-gray-700 mb-1">
                      作业标题 <span className="text-red-500">*</span>
                    </label>
                    <input
                      id="homework-title"
                      type="text"
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                      placeholder="请输入作业标题"
                      required
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="end-time" className="block text-sm font-medium text-gray-700 mb-1">
                      截止时间 <span className="text-red-500">*</span>
                    </label>
                    <DatePicker
                      id="end-time"
                      selected={dueDate}
                      onChange={(date: Date | null) => setDueDate(date || undefined)}
                      showTimeSelect
                      timeFormat="HH:mm"
                      timeIntervals={15}
                      timeCaption="时间"
                      dateFormat="yyyy-MM-dd HH:mm:ss"
                      locale={zhCN}
                      placeholderText="选择截止日期和时间"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                      required
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="homework-description" className="block text-sm font-medium text-gray-700 mb-1">
                      作业说明
                    </label>
                    <textarea
                      id="homework-description"
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      rows={4}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                      placeholder="请输入作业说明，如作业要求、注意事项等"
                    />
                  </div>
                  
                  {/* <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      附件上传
                    </label>
                    <div className="flex items-center justify-center px-6 py-4 border-2 border-gray-300 border-dashed rounded-lg">
                      <div className="space-y-1 text-center">
                        <CloudUpload className="mx-auto h-12 w-12 text-gray-400" />
                        <div className="flex text-sm text-gray-600">
                          <label htmlFor="file-upload" className="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none">
                            <span>点击上传文件</span>
                            <input id="file-upload" name="file-upload" type="file" className="sr-only" multiple onChange={handleFileUpload} />
                          </label>
                          <p className="pl-1">或拖拽文件到此处</p>
                        </div>
                        <p className="text-xs text-gray-500">
                          支持 PDF, DOCX, PPTX, ZIP 等格式，单个文件不超过10MB
                        </p>
                      </div>
                    </div>
                    
                    {files.length > 0 && (
                      <div className="mt-4 space-y-2">
                        <h3 className="text-sm font-medium text-gray-700">已上传文件</h3>
                        {files.map((file, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-8 w-8 bg-indigo-100 rounded-md flex items-center justify-center">
                                <div className="text-indigo-600 text-xs font-medium">
                                  {file.name.split('.').pop()?.toUpperCase()}
                                </div>
                              </div>
                              <div className="ml-3">
                                <p className="text-sm font-medium text-gray-900 truncate max-w-xs">{file.name}</p>
                                <p className="text-xs text-gray-500">{(file.size / 1024).toFixed(1)} KB</p>
                              </div>
                            </div>
                            <button
                              type="button"
                              onClick={() => removeFile(index)}
                              className="ml-2 flex-shrink-0 text-red-500 hover:text-red-700"
                            >
                              <Trash2 className="h-5 w-5" />
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div> */}
                </div>
              </div>
              
              {/* 题目管理 */}
              <div className="border-t border-gray-200 pt-8 mb-8">
                <h2 className="text-lg font-semibold text-gray-800 mb-4">题目管理</h2>
                
                <div className="mb-6">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-md font-medium text-gray-700">
                      已添加题目 ({selectedQuestions.length})
                    </h3>
                    <div className="flex space-x-2">
                      <button
                        type="button"
                        className="px-3 py-1.5 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 text-sm flex items-center"
                        onClick={handleAddBasicQuestion}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        添加基础题
                      </button>
                      <button
                        type="button"
                        className="px-3 py-1.5 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 text-sm flex items-center"
                        onClick={handleAddProgrammingQuestion}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        添加编程题
                      </button>
                    </div>
                  </div>
                  
                  {/* 题目列表 */}
                  {selectedQuestions.length > 0 ? (
                    <div className="space-y-4">
                      {selectedQuestions.map((question, index) => (
                        <div
                          key={question.id}
                          className="bg-white border border-gray-200 rounded-lg shadow-sm"
                        >
                          <div className="p-4 flex flex-col sm:flex-row sm:items-center justify-between gap-3 bg-gray-50 border-b border-gray-200 rounded-t-lg">
                            <div className="flex items-center">
                              <span className={`px-2 py-1 ${
                                question.type === 'programming' 
                                  ? 'bg-blue-100 text-blue-800' 
                                  : 'bg-green-100 text-green-800'
                              } text-xs font-medium rounded-full mr-2`}>
                                {getQuestionTypeText(question.type)}
                              </span>
                              <h4 className="text-md font-medium">题目 {index + 1}：{question.title}</h4>
                            </div>
                            <div className="flex items-center space-x-2">
                              {editingPointsId === question.id ? (
                                <div className="flex items-center space-x-1">
                                  <input
                                    type="number"
                                    min="1"
                                    max="100"
                                    className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md text-center"
                                    value={tempPoints}
                                    onChange={(e) => setTempPoints(parseInt(e.target.value) || 0)}
                                  />
                                  <button 
                                    type="button"
                                    className="p-1 text-green-500 hover:text-green-700"
                                    onClick={() => savePoints(question.id)}
                                  >
                                    <Check className="h-4 w-4" />
                                  </button>
                                  <button 
                                    type="button"
                                    className="p-1 text-gray-500 hover:text-gray-700"
                                    onClick={cancelEditingPoints}
                                  >
                                    <X className="h-4 w-4" />
                                  </button>
                                </div>
                              ) : (
                                <>
                                  <input
                                    type="number"
                                    value={question.points}
                                    className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md text-center cursor-not-allowed bg-gray-100"
                                    readOnly
                                  />
                                  <button 
                                    type="button"
                                    onClick={() => startEditingPoints(question.id, question.points)}
                                    className="text-gray-500 hover:text-gray-700"
                                    title="修改分数"
                                  >
                                    <Edit2 className="h-4 w-4" />
                                  </button>
                                </>
                              )}
                              <button
                                type="button"
                                onClick={() => removeQuestion(question.id)}
                                className="text-red-500 hover:text-red-700"
                                title="删除题目"
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                          <div className="p-4 flex items-center">
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-gray-500">
                                来源: {question.source}
                              </span>
                              <span>•</span>
                              <span className="text-sm">
                                {getDifficultyBadge(question.difficulty)}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="py-8 text-center text-gray-500 border-2 border-dashed rounded-lg">
                      <div className="flex flex-col items-center">
                        <AlertCircle className="h-12 w-12 text-gray-400 mb-3" />
                        <p className="text-gray-500 mb-2">还没有添加题目</p>
                        <p className="text-sm text-gray-400">
                          请点击上方按钮添加题目
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              
              {/* 高级设置 */}
              <div className="border-t border-gray-200 pt-8 mb-8">
                <h2 className="text-lg font-semibold text-gray-800 mb-4">高级设置</h2>
                
                {/* <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        checked={advancedSettings.allowLateSubmission}
                        onChange={() => toggleAdvancedSetting('allowLateSubmission')}
                      />
                      <span className="ml-2 text-sm text-gray-700">允许迟交（扣分）</span>
                    </label>
                    
                    {advancedSettings.allowLateSubmission && (
                      <div className="mt-2 pl-6">
                        <label htmlFor="late-penalty" className="block text-sm font-medium text-gray-700 mb-1">迟交扣分比例 (%)</label>
                        <input
                          type="number"
                          id="late-penalty"
                          name="late-penalty"
                          placeholder="20"
                          value={advancedSettings.latePenalty}
                          onChange={(e) => updateAdvancedSettingValue('latePenalty', parseInt(e.target.value) || 0)}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        />
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        checked={advancedSettings.enableTimeLimit}
                        onChange={() => toggleAdvancedSetting('enableTimeLimit')}
                      />
                      <span className="ml-2 text-sm text-gray-700">启用限时模式</span>
                    </label>
                    
                    {advancedSettings.enableTimeLimit && (
                      <div className="mt-2 pl-6">
                        <label htmlFor="time-limit" className="block text-sm font-medium text-gray-700 mb-1">限时时长（分钟）</label>
                        <input
                          type="number"
                          id="time-limit"
                          name="time-limit"
                          placeholder="60"
                          value={advancedSettings.timeLimit}
                          onChange={(e) => updateAdvancedSettingValue('timeLimit', parseInt(e.target.value) || 0)}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        />
                      </div>
                    )}
                  </div>
                </div> */}
                
                <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        checked={advancedSettings.showResultsImmediately}
                        onChange={() => toggleAdvancedSetting('showResultsImmediately')}
                      />
                      <span className="ml-2 text-sm text-gray-700">作业完成后立即显示结果</span>
                    </label>
                  </div> */}
                  
                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        checked={advancedSettings.allowMultipleSubmissions}
                        onChange={() => toggleAdvancedSetting('allowMultipleSubmissions')}
                      />
                      <span className="ml-2 text-sm text-gray-700">允许多次提交（取最高分）</span>
                    </label>
                  </div>
                </div>
                
                {/* <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        checked={advancedSettings.notifyAllStudents}
                        onChange={() => toggleAdvancedSetting('notifyAllStudents')}
                      />
                      <span className="ml-2 text-sm text-gray-700">发布后通知所有学生</span>
                    </label>
                  </div>
                  
                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        checked={advancedSettings.enableCheatingDetection}
                        onChange={() => toggleAdvancedSetting('enableCheatingDetection')}
                      />
                      <span className="ml-2 text-sm text-gray-700">启用作弊检测</span>
                    </label>
                  </div>
                </div> */}
              </div>
              
              {/* 底部按钮 */}
              <div className="flex justify-end space-x-4 border-t border-gray-200 pt-6">
                <button
                  type="button"
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                  onClick={(e) => handleSubmit(e, true)}
                >
                  保存为草稿
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
                >
                  发布作业
                </button>
              </div>
            </form>
          </div>
        </div>
        
        {/* 使用QuestionSelectorModal组件替换原有的弹窗代码 */}
        <QuestionSelectorModal 
          show={showBasicQuestionModal}
          type="basic"
          questions={allQuestions}
          selectedQuestions={selectedQuestions}
          onClose={() => setShowBasicQuestionModal(false)}
          onAddQuestion={(question) => {
            addQuestionToHomework(question)
          }}
        />
        
        <QuestionSelectorModal 
          show={showProgrammingQuestionModal}
          type="programming"
          questions={allQuestions}
          selectedQuestions={selectedQuestions}
          onClose={() => setShowProgrammingQuestionModal(false)}
          onAddQuestion={(question) => {
            addQuestionToHomework(question)
          }}
        />
      </div>
    </div>
  )
} 