'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import TeamHeader from '@/app/components/team/TeamHeader'
import TeamNavigation from '@/app/components/team/TeamNavigation'
import ProblemSetList from '@/app/components/team/problem/ProblemSetList'
import ProblemSetFilter from '@/app/components/team/problem/ProblemSetFilter'
import { TeamProblemSet } from '@/app/types/team'

export default function TeamProblemSetsPage() {
  const params = useParams()
  const teamId = params.id as string
  const [problemSets, setProblemSets] = useState<TeamProblemSet[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [filter, setFilter] = useState<'all' | 'easy' | 'medium' | 'hard'>('all')

  useEffect(() => {
    // TODO: 从API获取题单数据
    setIsLoading(true)
    
    // 模拟数据
    const mockProblemSets: TeamProblemSet[] = [
      {
        id: '1',
        title: '数组和字符串基础练习',
        description: '适合初学者的数组和字符串处理题目集合，包含基础操作和简单算法。',
        problemCount: 10,
        difficulty: 'easy',
        recommended: true
      },
      {
        id: '2',
        title: '动态规划入门',
        description: '基础动态规划问题集合，包含经典的DP问题和详细的解题思路讲解。',
        problemCount: 8,
        difficulty: 'medium',
        recommended: true
      },
      {
        id: '3',
        title: '图论算法专题',
        description: '常见图论算法题目集，包括最短路径、最小生成树和拓扑排序等经典问题。',
        problemCount: 12,
        difficulty: 'hard',
        recommended: false
      },
      {
        id: '4',
        title: '贪心算法实战',
        description: '贪心算法题目集，帮助理解贪心策略的应用场景和证明思路。',
        problemCount: 6,
        difficulty: 'medium',
        recommended: false
      },
      {
        id: '5',
        title: '搜索与回溯',
        description: 'DFS、BFS和回溯算法专项训练，提高解决搜索问题的能力。',
        problemCount: 9,
        difficulty: 'hard',
        recommended: true
      },
      {
        id: '6',
        title: '基础算法练习',
        description: '包含排序、查找等基础算法的练习题，适合算法入门。',
        problemCount: 15,
        difficulty: 'easy',
        recommended: false
      }
    ]
    
    // 模拟API延迟
    setTimeout(() => {
      setProblemSets(mockProblemSets)
      setIsLoading(false)
    }, 500)
  }, [teamId])

  const filteredProblemSets = problemSets.filter(
    ps => filter === 'all' || ps.difficulty === filter
  )

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 团队头部信息 */}
      <TeamHeader teamId={teamId} />

      {/* 团队导航 */}
      <TeamNavigation />

      {/* 主要内容 */}
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">团队题单</h1>
            <p className="text-gray-600 mt-1">浏览和练习团队题目集合</p>
          </div>
          <div className="mt-4 lg:mt-0">
            <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              创建题单
            </button>
          </div>
        </div>

        {/* 筛选器 */}
        <ProblemSetFilter
          currentFilter={filter}
          onFilterChange={setFilter}
          problemSetCount={{
            all: problemSets.length,
            easy: problemSets.filter(ps => ps.difficulty === 'easy').length,
            medium: problemSets.filter(ps => ps.difficulty === 'medium').length,
            hard: problemSets.filter(ps => ps.difficulty === 'hard').length
          }}
        />

        {/* 题单列表 */}
        <ProblemSetList 
          problemSets={filteredProblemSets} 
          isLoading={isLoading} 
          teamId={teamId} 
        />
      </div>
    </div>
  )
} 