'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import TeamHeader from '@/app/components/team/TeamHeader'
import TeamNavigation from '@/app/components/team/TeamNavigation'
import ProblemSetDetail from '@/app/components/team/problem/ProblemSetDetail'
import ProblemList from '@/app/components/team/problem/ProblemList'
import { TeamProblemSet } from '@/app/types/team'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faArrowLeft } from '@fortawesome/free-solid-svg-icons'

interface Problem {
  id: string
  title: string
  difficulty: 'easy' | 'medium' | 'hard'
  tags: string[]
  solvedCount: number
  totalCount: number
  acceptance: number
}

export default function ProblemSetDetailPage() {
  const params = useParams()
  const teamId = params.id as string
  const problemSetId = params.problemSetId as string
  
  const [problemSet, setProblemSet] = useState<TeamProblemSet | null>(null)
  const [problems, setProblems] = useState<Problem[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // TODO: 从API获取题单详情和题目列表
    setIsLoading(true)
    
    // 模拟题单数据
    const mockProblemSet: TeamProblemSet = {
      id: problemSetId,
      title: '动态规划入门',
      description: `
# 动态规划入门

这个题单包含了动态规划的经典问题，帮助你掌握DP的核心思想和常见模式。

## 学习目标
1. 理解动态规划的基本原理
2. 掌握状态定义和状态转移方程的推导
3. 学会解决基础的DP问题类型

## 推荐学习顺序
先从基础的一维DP问题开始，然后是二维DP，最后尝试更复杂的状态定义。

## 学习资料
- [动态规划基础](https://xuejiaxi.fun/articles/dp-basics)
- [DP问题分类与解法](https://xuejiaxi.fun/articles/dp-types)

祝学习愉快！
      `,
      problemCount: 8,
      difficulty: 'medium',
      recommended: true
    }
    
    // 模拟题目数据
    const mockProblems: Problem[] = [
      {
        id: '101',
        title: '斐波那契数列',
        difficulty: 'easy',
        tags: ['动态规划', '数学'],
        solvedCount: 245,
        totalCount: 300,
        acceptance: 81.7
      },
      {
        id: '102',
        title: '爬楼梯',
        difficulty: 'easy',
        tags: ['动态规划'],
        solvedCount: 210,
        totalCount: 280,
        acceptance: 75.0
      },
      {
        id: '103',
        title: '打家劫舍',
        difficulty: 'medium',
        tags: ['动态规划', '数组'],
        solvedCount: 150,
        totalCount: 240,
        acceptance: 62.5
      },
      {
        id: '104',
        title: '最长递增子序列',
        difficulty: 'medium',
        tags: ['动态规划', '二分查找'],
        solvedCount: 120,
        totalCount: 220,
        acceptance: 54.5
      },
      {
        id: '105',
        title: '编辑距离',
        difficulty: 'hard',
        tags: ['动态规划', '字符串'],
        solvedCount: 95,
        totalCount: 200,
        acceptance: 47.5
      },
      {
        id: '106',
        title: '背包问题',
        difficulty: 'medium',
        tags: ['动态规划', '贪心'],
        solvedCount: 135,
        totalCount: 230,
        acceptance: 58.7
      },
      {
        id: '107',
        title: '最长公共子序列',
        difficulty: 'medium',
        tags: ['动态规划', '字符串'],
        solvedCount: 130,
        totalCount: 210,
        acceptance: 61.9
      },
      {
        id: '108',
        title: '最大子数组和',
        difficulty: 'easy',
        tags: ['动态规划', '分治', '数组'],
        solvedCount: 190,
        totalCount: 240,
        acceptance: 79.2
      }
    ]
    
    // 模拟API延迟
    setTimeout(() => {
      setProblemSet(mockProblemSet)
      setProblems(mockProblems)
      setIsLoading(false)
    }, 500)
  }, [teamId, problemSetId])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <TeamHeader teamId={teamId} />
        <TeamNavigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <div className="inline-block w-12 h-12 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
            <p className="ml-4 text-gray-500">正在加载题单详情...</p>
          </div>
        </div>
      </div>
    )
  }

  if (!problemSet) {
    return (
      <div className="min-h-screen bg-gray-50">
        <TeamHeader teamId={teamId} />
        <TeamNavigation />
        <div className="container mx-auto px-4 py-8">
          <div className="bg-white rounded-xl shadow-sm p-8 text-center">
            <h3 className="text-lg font-medium text-gray-800 mb-2">题单未找到</h3>
            <p className="text-gray-500">
              无法找到该题单，它可能已被删除或您没有访问权限。
            </p>
            <Link 
              href={`/teams/${teamId}/problems`}
              className="mt-4 inline-flex items-center text-indigo-600 hover:text-indigo-800"
            >
              <FontAwesomeIcon icon={faArrowLeft} className="mr-1" />
              返回题单列表
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <TeamHeader teamId={teamId} />
      <TeamNavigation />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link 
            href={`/teams/${teamId}/problems`}
            className="text-indigo-600 hover:text-indigo-800 flex items-center w-fit"
          >
            <FontAwesomeIcon icon={faArrowLeft} className="mr-1" />
            返回题单列表
          </Link>
        </div>
        
        <div className="grid grid-cols-1 gap-6">
          {/* 题单详情 */}
          <ProblemSetDetail problemSet={problemSet} />
          
          {/* 题目列表 */}
          <ProblemList problems={problems} teamId={teamId} />
        </div>
      </div>
    </div>
  )
} 