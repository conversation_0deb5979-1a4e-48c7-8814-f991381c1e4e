'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import TeamHeader from '@/app/components/team/TeamHeader'
import TeamNavigation from '@/app/components/team/TeamNavigation'
import ExamDetail from '@/app/components/team/exam/ExamDetail'
import ExamParticipants from '@/app/components/team/exam/ExamParticipants'
import { TeamExam } from '@/app/types/team'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faArrowLeft } from '@fortawesome/free-solid-svg-icons'

interface ExamParticipant {
  id: string
  name: string
  avatar: string
  status: 'registered' | 'participated' | 'absent'
  score?: number
  rank?: number
}

export default function ExamDetailPage() {
  const params = useParams()
  const router = useRouter()
  const teamId = params.id as string
  const examId = params.examId as string
  
  const [exam, setExam] = useState<TeamExam | null>(null)
  const [participants, setParticipants] = useState<ExamParticipant[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isRegistered, setIsRegistered] = useState(false)

  useEffect(() => {
    // TODO: 从API获取考试详情
    setIsLoading(true)
    
    // 模拟数据
    const mockExam: TeamExam = {
      id: examId,
      title: '算法期中测试',
      description: `
# 算法期中测试

## 考试说明
本次考试将测试你对基础数据结构和算法的理解和应用能力。考试包含选择题和编程题两部分。

## 考试内容
1. **选择题部分**：20题，每题2分，共40分
   - 数组与链表
   - 栈与队列
   - 树与图
   - 基础算法概念

2. **编程题部分**：3题，共60分
   - 基础题：简单的字符串处理（15分）
   - 中等题：数据结构应用（20分）
   - 挑战题：算法设计与优化（25分）

## 注意事项
- 考试全程监控，禁止查阅资料
- 提交后无法修改答案
- 请提前调试好编程环境
- 如遇技术问题请立即联系监考老师

祝大家考试顺利！
      `,
      startTime: '2023-11-25T19:00:00',
      duration: 120,
      participantCount: 16,
      status: 'upcoming',
      totalQuestions: 23,
      totalScore: 100
    }
    
    const mockParticipants: ExamParticipant[] = [
      {
        id: '1',
        name: '王小明',
        avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
        status: 'registered'
      },
      {
        id: '2',
        name: '李小红',
        avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
        status: 'registered'
      },
      {
        id: '3',
        name: '张小强',
        avatar: 'https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
        status: 'registered'
      }
    ]
    
    // 模拟API延迟
    setTimeout(() => {
      setExam(mockExam)
      setParticipants(mockParticipants)
      setIsRegistered(true) // 假设当前用户已报名
      setIsLoading(false)
    }, 500)
  }, [teamId, examId])

  const handleRegister = () => {
    // TODO: 实现报名API调用
    setIsRegistered(true)
  }

  const handleStartExam = () => {
    router.push(`/teams/${teamId}/exams/${examId}/take`)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <div className="inline-block w-12 h-12 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
            <p className="ml-4 text-gray-500">正在加载考试详情...</p>
          </div>
        </div>
      </div>
    )
  }

  if (!exam) {
    return (
      <div className="min-h-screen bg-gray-50">
        <TeamHeader teamId={teamId} />
        <TeamNavigation />
        <div className="container mx-auto px-4 py-8">
          <div className="bg-white rounded-xl shadow-sm p-8 text-center">
            <h3 className="text-lg font-medium text-gray-800 mb-2">考试未找到</h3>
            <p className="text-gray-500">
              无法找到该考试，它可能已被删除或您没有访问权限。
            </p>
            <Link 
              href={`/teams/${teamId}/exams`}
              className="mt-4 inline-flex items-center text-indigo-600 hover:text-indigo-800"
            >
              <FontAwesomeIcon icon={faArrowLeft} className="mr-1" />
              返回考试列表
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link 
            href={`/teams/${teamId}/exams`}
            className="text-indigo-600 hover:text-indigo-800 flex items-center w-fit"
          >
            <FontAwesomeIcon icon={faArrowLeft} className="mr-1" />
            返回考试列表
          </Link>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 考试详情 */}
          <div className="lg:col-span-2">
            <ExamDetail 
              exam={exam} 
              isRegistered={isRegistered}
              onRegister={handleRegister}
              onStartExam={handleStartExam}
            />
          </div>
          
          {/* 参与人员 */}
          <div className="lg:col-span-1">
            <ExamParticipants 
              participants={participants}
              status={exam.status}
            />
          </div>
        </div>
      </div>
    </div>
  )
} 