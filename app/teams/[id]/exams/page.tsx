'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import TeamHeader from '@/app/components/team/TeamHeader'
import TeamNavigation from '@/app/components/team/TeamNavigation'
import ExamList from '@/app/components/team/exam/ExamList'
import ExamFilter from '@/app/components/team/exam/ExamFilter'
import { TeamExam } from '@/app/types/team'

export default function TeamExamsPage() {
  const params = useParams()
  const teamId = params.id as string
  const [exams, setExams] = useState<TeamExam[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [filter, setFilter] = useState<'all' | 'upcoming' | 'ongoing' | 'ended'>('all')

  useEffect(() => {
    // TODO: 从API获取考试数据
    setIsLoading(true)
    
    // 模拟数据
    const mockExams: TeamExam[] = [
      {
        id: '1',
        title: '算法期中测试',
        description: '覆盖基础数据结构和算法，包含选择题和编程题。',
        startTime: '2023-11-25T19:00:00',
        duration: 120,
        participantCount: 0,
        status: 'upcoming'
      },
      {
        id: '2',
        title: '基础编程能力测试',
        description: '测试基础语法和编程能力，重点考察数组和字符串操作。',
        startTime: '2023-11-10T19:00:00',
        duration: 90,
        participantCount: 16,
        status: 'ended'
      },
      {
        id: '3',
        title: '周末模拟赛',
        description: '模拟真实竞赛环境，题目难度接近NOI普及组。',
        startTime: '2023-11-18T10:00:00',
        duration: 180,
        participantCount: 12,
        status: 'ended'
      },
      {
        id: '4',
        title: '动态规划专题测试',
        description: '考察动态规划各类经典题型的掌握情况。',
        startTime: '2023-12-05T19:00:00',
        duration: 120,
        participantCount: 0,
        status: 'upcoming'
      },
      {
        id: '5',
        title: '实时编程挑战',
        description: '解决5道算法题，实时排名，限时完成。',
        startTime: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30分钟前开始
        duration: 120,
        participantCount: 8,
        status: 'ongoing'
      }
    ]
    
    // 模拟API延迟
    setTimeout(() => {
      setExams(mockExams)
      setIsLoading(false)
    }, 500)
  }, [teamId])

  const filteredExams = exams.filter(
    exam => filter === 'all' || exam.status === filter
  )

  return (
    <div className="min-h-screen bg-gray-50">

      {/* 主要内容 */}
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">团队考试</h1>
            <p className="text-gray-600 mt-1">管理和参与团队考试</p>
          </div>
          <div className="mt-4 lg:mt-0">
            <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              创建考试
            </button>
          </div>
        </div>

        {/* 筛选器 */}
        <ExamFilter
          currentFilter={filter}
          onFilterChange={setFilter}
          examCount={{
            all: exams.length,
            upcoming: exams.filter(e => e.status === 'upcoming').length,
            ongoing: exams.filter(e => e.status === 'ongoing').length,
            ended: exams.filter(e => e.status === 'ended').length
          }}
        />

        {/* 考试列表 */}
        <ExamList 
          exams={filteredExams} 
          isLoading={isLoading} 
          teamId={teamId} 
        />
      </div>
    </div>
  )
} 