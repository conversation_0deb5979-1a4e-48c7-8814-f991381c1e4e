'use client'

import { useState, useEffect } from 'react'
import { useParams, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import TeamHeader from '@/app/components/team/TeamHeader'
import TeamNavigation from '@/app/components/team/TeamNavigation'
import TeamManagementSidebar from '@/app/components/team/manage/TeamManagementSidebar'
import TeamBasicSettings from '@/app/components/team/manage/TeamBasicSettings'
import TeamActivityLog from '@/app/components/team/manage/TeamActivityLog'
import TeamManagementCards from '@/app/components/team/manage/TeamManagementCards'
import TeamMemberManagement from '@/app/components/team/manage/TeamMemberManagement'
import TeamStatisticsManagement from '@/app/components/team/manage/TeamStatisticsManagement'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faArrowLeft } from '@fortawesome/free-solid-svg-icons'

export default function TeamManagementPage() {
  const params = useParams()
  const searchParams = useSearchParams()
  const teamId = params.id as string
  const [activeSection, setActiveSection] = useState<string>('basic-info')
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [team, setTeam] = useState<any>(null)

  useEffect(() => {
    // 从URL查询参数中获取当前选中的部分
    const section = searchParams.get('section')
    if (section) {
      setActiveSection(section)
    }
  }, [searchParams])

  useEffect(() => {
    // TODO: 从API获取团队信息
    setIsLoading(true)
    
    // 模拟数据
    const mockTeam = {
      id: teamId,
      name: '信竞训练营',
      description: '我们的目标是提高算法编程能力，一起备战信息学竞赛！',
      avatar: null,
      type: 'contest',
      tags: ['算法', '竞赛', '编程'],
      memberCount: 18,
      adminCount: 2,
      homeworkCount: 5,
      ongoingHomeworkCount: 3,
      examCount: 2,
      upcomingExamCount: 1,
      problemSetCount: 3,
      problemCount: 65,
      createdAt: '2023-09-15',
      isCreator: true
    }
    
    // 模拟API延迟
    setTimeout(() => {
      setTeam(mockTeam)
      setIsLoading(false)
    }, 500)
  }, [teamId])

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex justify-center items-center h-64">
          <div className="inline-block w-12 h-12 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
          <p className="ml-4 text-gray-500">正在加载团队信息...</p>
        </div>
      )
    }

    switch (activeSection) {
      case 'basic-info':
        return (
          <>
            {/* 基本信息设置 */}
            <TeamBasicSettings team={team} />
            
            {/* 功能卡片 */}
            <div className="my-8">
              <TeamManagementCards team={team} />
            </div>
            
            {/* 最近活动 */}
            <TeamActivityLog teamId={teamId} />
          </>
        )
      case 'members':
        return <TeamMemberManagement teamId={teamId} />
      case 'statistics':
        return <TeamStatisticsManagement teamId={teamId} />
      case 'homework':
        return (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-bold mb-6 text-gray-800">作业管理</h2>
            <p className="text-gray-500">作业管理功能正在开发中...</p>
          </div>
        )
      case 'exams':
        return (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-bold mb-6 text-gray-800">考试管理</h2>
            <p className="text-gray-500">考试管理功能正在开发中...</p>
          </div>
        )
      case 'problem-sets':
        return (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-bold mb-6 text-gray-800">题单管理</h2>
            <p className="text-gray-500">题单管理功能正在开发中...</p>
          </div>
        )
      case 'problems':
        return (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-bold mb-6 text-gray-800">题目库管理</h2>
            <p className="text-gray-500">题目库管理功能正在开发中...</p>
          </div>
        )
      case 'activity-log':
        return <TeamActivityLog teamId={teamId} />
      case 'advanced':
        return (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-bold mb-6 text-gray-800">高级设置</h2>
            <div className="space-y-4">
              <div className="p-4 border border-red-200 rounded-lg bg-red-50">
                <h3 className="text-lg font-medium text-red-700 mb-2">危险操作</h3>
                <p className="text-sm text-red-600 mb-4">
                  这些操作不可逆，请谨慎操作。
                </p>
                <div className="space-y-4">
                  <div>
                    <button className="px-4 py-2 bg-white text-red-600 border border-red-300 rounded-lg hover:bg-red-50">
                      解散团队
                    </button>
                    <p className="mt-1 text-xs text-red-500">
                      解散后，所有成员将无法访问团队内容，但内容将保留90天。
                    </p>
                  </div>
                  <div>
                    <button className="px-4 py-2 bg-white text-red-600 border border-red-300 rounded-lg hover:bg-red-50">
                      删除团队
                    </button>
                    <p className="mt-1 text-xs text-red-500">
                      删除后，所有团队内容将永久删除且无法恢复。
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )
      default:
        return (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-bold mb-6 text-gray-800">设置</h2>
            <p className="text-gray-500">请从左侧选择要管理的内容</p>
          </div>
        )
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">

      {/* 团队导航 */}
      <div className="bg-white shadow-sm">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Link
                href={`/teams/${teamId}`}
                className="py-4 px-6 text-indigo-600 font-medium flex items-center"
              >
                <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
                返回团队主页
              </Link>
              <h1 className="text-xl font-bold text-gray-800">团队管理</h1>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧菜单 */}
          <div className="lg:col-span-1">
            <TeamManagementSidebar 
              activeSection={activeSection} 
              onSectionChange={setActiveSection} 
            />
          </div>

          {/* 右侧内容区 */}
          <div className="lg:col-span-2">
            {renderContent()}
          </div>
        </div>
      </div>
    </div>
  )
} 