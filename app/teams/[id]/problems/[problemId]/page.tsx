'use client'

import { useState, useEffect } from 'react'
import { useParams, useRouter } from 'next/navigation'
import Link from 'next/link'
import TeamHeader from '@/app/components/team/TeamHeader'
import TeamNavigation from '@/app/components/team/TeamNavigation'
import ProblemDetail from '@/app/components/team/problem/ProblemDetail'
import ProblemSubmission from '@/app/components/team/problem/ProblemSubmission'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faArrowLeft, faChartLine } from '@fortawesome/free-solid-svg-icons'

interface Problem {
  id: string
  title: string
  content: string
  inputFormat: string
  outputFormat: string
  examples: {
    input: string
    output: string
    explanation?: string
  }[]
  constraints: string[]
  difficulty: 'easy' | 'medium' | 'hard'
  tags: string[]
  solvedCount: number
  totalCount: number
  acceptance: number
  timeLimit: number // 单位: ms
  memoryLimit: number // 单位: MB
  createdAt: string
}

export default function ProblemDetailPage() {
  const params = useParams()
  const router = useRouter()
  const teamId = params.id as string
  const problemId = params.problemId as string
  
  const [problem, setProblem] = useState<Problem | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // TODO: 从API获取题目详情
    setIsLoading(true)
    
    // 模拟数据
    const mockProblem: Problem = {
      id: problemId,
      title: '两数之和',
      content: `
给定一个整数数组 \`nums\` 和一个整数目标值 \`target\`，请你在该数组中找出和为目标值 \`target\` 的那两个整数，并返回它们的数组下标。

你可以假设每种输入只会对应一个答案。但是，数组中同一个元素在答案里不能重复出现。

你可以按任意顺序返回答案。
      `,
      inputFormat: `
- 第一行包含一个整数 n，表示数组 nums 的长度。
- 第二行包含 n 个整数，表示数组 nums 的元素。
- 第三行包含一个整数 target，表示目标值。
      `,
      outputFormat: `
输出两个整数，表示答案元素的下标，用空格分隔。
      `,
      examples: [
        {
          input: `4\n2 7 11 15\n9`,
          output: `0 1`,
          explanation: '因为 nums[0] + nums[1] == 2 + 7 == 9，所以返回 [0, 1]。'
        },
        {
          input: `3\n3 2 4\n6`,
          output: `1 2`
        },
        {
          input: `2\n3 3\n6`,
          output: `0 1`
        }
      ],
      constraints: [
        '2 <= nums.length <= 10^4',
        '-10^9 <= nums[i] <= 10^9',
        '-10^9 <= target <= 10^9',
        '只会存在一个有效答案'
      ],
      difficulty: 'easy',
      tags: ['数组', '哈希表'],
      solvedCount: 256,
      totalCount: 320,
      acceptance: 80.0,
      timeLimit: 1000,
      memoryLimit: 256,
      createdAt: '2023-10-15'
    }
    
    // 模拟API延迟
    setTimeout(() => {
      setProblem(mockProblem)
      setIsLoading(false)
    }, 500)
  }, [teamId, problemId])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <TeamHeader teamId={teamId} />
        <TeamNavigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <div className="inline-block w-12 h-12 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
            <p className="ml-4 text-gray-500">正在加载题目...</p>
          </div>
        </div>
      </div>
    )
  }

  if (!problem) {
    return (
      <div className="min-h-screen bg-gray-50">
        <TeamHeader teamId={teamId} />
        <TeamNavigation />
        <div className="container mx-auto px-4 py-8">
          <div className="bg-white rounded-xl shadow-sm p-8 text-center">
            <h3 className="text-lg font-medium text-gray-800 mb-2">题目未找到</h3>
            <p className="text-gray-500">
              无法找到该题目，它可能已被删除或您没有访问权限。
            </p>
            <Link 
              href={`/teams/${teamId}/problems`}
              className="mt-4 inline-flex items-center text-indigo-600 hover:text-indigo-800"
            >
              <FontAwesomeIcon icon={faArrowLeft} className="mr-1" />
              返回题目列表
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <TeamHeader teamId={teamId} />
      <TeamNavigation />
      
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <Link 
            href={`/teams/${teamId}/problems`}
            className="text-indigo-600 hover:text-indigo-800 flex items-center"
          >
            <FontAwesomeIcon icon={faArrowLeft} className="mr-1" />
            返回题目列表
          </Link>
          
          <Link
            href={`/teams/${teamId}/problems/${problemId}/statistics`}
            className="text-indigo-600 hover:text-indigo-800 flex items-center"
          >
            <FontAwesomeIcon icon={faChartLine} className="mr-1" />
            题目统计
          </Link>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 题目详情 */}
          <div className="lg:col-span-2">
            <ProblemDetail problem={problem} />
          </div>
          
          {/* 提交区域 */}
          <div className="lg:col-span-1">
            <ProblemSubmission
              teamId={teamId}
              problemId={problemId}
            />
          </div>
        </div>
      </div>
    </div>
  )
} 