'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faCircle,
  faCheckCircle,
  faQuestionCircle,
  faTimes,
  faPlus,
  faTrash,
  faArrowLeft,
  faSave
} from '@fortawesome/free-solid-svg-icons'
import dynamic from 'next/dynamic'
import CommonDropdown, { DropdownOption } from '@/app/components/CommonDropdown'
import TagSelector from '@/app/components/TagSelector'
import TagService, { Tag, TagGroup } from '@/app/service/tag-service'

const RichEditor = dynamic(() => import('@/app/components/RichEditor'), {
  ssr: false,
  loading: () => <div className="w-full h-[200px] border border-gray-300 rounded-lg bg-gray-50 flex items-center justify-center text-gray-400">加载编辑器中...</div>
})

interface Option {
  id: string
  content: string
  isCorrect: boolean
}

// 难度选项
const difficultyOptions: DropdownOption<number>[] = [
  { label: '简单', value: 1 },
  { label: '中等', value: 2 },
  { label: '困难', value: 3 }
]

export default function CreateProblemPage() {
  const router = useRouter()
  const params = useParams()
  const teamId = params.id as string

  // 基本信息
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [difficulty, setDifficulty] = useState<number>(1)
  const [tags, setTags] = useState<Tag[]>([])
  const [tagGroups, setTagGroups] = useState<TagGroup[]>([])
  const [problemType, setProblemType] = useState<'single' | 'multiple' | 'trueFalse'>('single')
  const [explanation, setExplanation] = useState('')
  
  // 选项（单选题/多选题）
  const [options, setOptions] = useState<Option[]>([
    { id: '1', content: '', isCorrect: false },
    { id: '2', content: '', isCorrect: false },
  ])
  
  // 判断题答案
  const [trueFalseAnswer, setTrueFalseAnswer] = useState<boolean | null>(null)
  
  // 标签选择器相关
  const [showTagSelector, setShowTagSelector] = useState(false)
  const tagSelectorRef = useRef<HTMLDivElement>(null)
  
  // 加载标签数据
  useEffect(() => {
    const loadTags = async () => {
      try {
        // 获取基础题标签（type=1）
        const response = await TagService.getTagList(1)
        setTagGroups(response.data)
      } catch (error) {
        console.error('加载标签失败:', error)
        // 使用空数组作为备用
        setTagGroups([])
      }
    }
    
    loadTags()
  }, [])
  
  // 添加新选项
  const addOption = () => {
    const newOption: Option = {
      id: String(options.length + 1),
      content: '',
      isCorrect: false
    }
    setOptions([...options, newOption])
  }
  
  // 删除选项
  const removeOption = (idToRemove: string) => {
    if (options.length <= 2) return // 至少保留两个选项
    setOptions(options.filter(option => option.id !== idToRemove))
  }
  
  // 更新选项内容
  const updateOptionContent = (id: string, content: string) => {
    setOptions(options.map(option => 
      option.id === id ? { ...option, content } : option
    ))
  }
  
  // 设置正确答案
  const setCorrectOption = (id: string) => {
    if (problemType === 'single') {
      setOptions(options.map(option => 
        ({ ...option, isCorrect: option.id === id })
      ))
    } else if (problemType === 'multiple') {
      setOptions(options.map(option => 
        option.id === id ? { ...option, isCorrect: !option.isCorrect } : option
      ))
    }
  }
  
  // 处理标签变更
  const handleTagsChange = (selectedTags: Tag[]) => {
    setTags(selectedTags)
  }
  
  // 关闭标签选择器
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (tagSelectorRef.current && !tagSelectorRef.current.contains(event.target as Node)) {
        setShowTagSelector(false)
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])
  
  // 处理表单提交
  const handleSubmit = () => {
    // 表单验证
    if (!title.trim() || !description.trim()) {
      alert('请填写题目标题和描述')
      return
    }
    
    if (problemType === 'single' || problemType === 'multiple') {
      // 检查选项是否都填写了内容
      if (options.some(option => !option.content.trim())) {
        alert('请填写所有选项内容')
        return
      }
      
      // 检查是否有正确答案
      if (!options.some(option => option.isCorrect)) {
        alert('请选择至少一个正确答案')
        return
      }
    }
    
    if (problemType === 'trueFalse' && trueFalseAnswer === null) {
      alert('请选择判断题答案')
      return
    }
    
    // 构建问题数据
    const problemData = {
      type: 'basic',
      title,
      description,
      difficulty,
      tagIds: tags.map(tag => tag.id),
      problemType,
      options: problemType === 'trueFalse' ? [] : options,
      trueFalseAnswer: problemType === 'trueFalse' ? trueFalseAnswer : null,
      explanation,
      teamId
    }
    
    // TODO: 发送API请求保存题目
    console.log('提交题目数据:', problemData)
    
    // 模拟保存成功
    alert('题目创建成功！')
    
    // 返回题目列表页
    router.push(`/teams/${teamId}/problems`)
  }
  
  // 返回上一页
  const handleGoBack = () => {
    router.back()
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* 头部导航 */}
        <div className="mb-6">
          <button
            onClick={handleGoBack}
            className="flex items-center text-gray-600 hover:text-gray-900"
          >
            <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
            返回题目列表
          </button>
        </div>
        
        {/* 页面标题 */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">创建基础题目</h1>
            <p className="text-gray-600">创建新的基础题目到题库中</p>
          </div>
          <button
            onClick={handleSubmit}
            className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition flex items-center"
          >
            <FontAwesomeIcon icon={faSave} className="mr-2" />
            保存题目
          </button>
        </div>
        
        {/* 表单容器 */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
          <h2 className="text-lg font-medium text-gray-800 mb-4">基本信息</h2>
          
          {/* 题目类型 */}
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-medium mb-2">
              题目类型
            </label>
            <div className="grid grid-cols-3 gap-4">
              <button 
                onClick={() => setProblemType('single')}
                className={`p-4 rounded-lg border flex flex-col items-center ${
                  problemType === 'single' 
                    ? 'border-indigo-600 bg-indigo-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <FontAwesomeIcon 
                  icon={faCircle} 
                  className={`text-2xl mb-2 ${
                    problemType === 'single' ? 'text-indigo-600' : 'text-gray-400'
                  }`} 
                />
                <span className="font-medium">单选题</span>
              </button>
              <button 
                onClick={() => setProblemType('multiple')}
                className={`p-4 rounded-lg border flex flex-col items-center ${
                  problemType === 'multiple' 
                    ? 'border-indigo-600 bg-indigo-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <FontAwesomeIcon 
                  icon={faCheckCircle} 
                  className={`text-2xl mb-2 ${
                    problemType === 'multiple' ? 'text-indigo-600' : 'text-gray-400'
                  }`} 
                />
                <span className="font-medium">多选题</span>
              </button>
              <button 
                onClick={() => setProblemType('trueFalse')}
                className={`p-4 rounded-lg border flex flex-col items-center ${
                  problemType === 'trueFalse' 
                    ? 'border-indigo-600 bg-indigo-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <FontAwesomeIcon 
                  icon={faQuestionCircle} 
                  className={`text-2xl mb-2 ${
                    problemType === 'trueFalse' ? 'text-indigo-600' : 'text-gray-400'
                  }`} 
                />
                <span className="font-medium">判断题</span>
              </button>
            </div>
          </div>
          
          {/* 题目标题 */}
          <div className="mb-6">
            <label htmlFor="title" className="block text-gray-700 text-sm font-medium mb-2">
              题目标题
            </label>
            <input
              id="title"
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              placeholder="输入题目标题"
            />
          </div>
          
          {/* 题目描述（富文本） */}
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-medium mb-2">
              题目描述
            </label>
            <RichEditor
              value={description}
              onChange={setDescription}
              placeholder="输入题目描述..."
              height={200}
            />
          </div>
          
          {/* 难度级别 - 下拉框 */}
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-medium mb-2">
              难度级别
            </label>
            <CommonDropdown
              label="难度"
              options={difficultyOptions}
              value={difficulty}
              onChange={setDifficulty}
              placeholder="请选择难度级别"
              className="w-full"
              canClear={false}
            />
          </div>
          
          {/* 标签选择器 */}
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-medium mb-2">
              标签
            </label>
            <div className="relative">
              <div 
                className="w-full border border-gray-300 rounded-lg px-4 py-2 bg-white cursor-pointer flex items-center justify-between"
                onClick={() => setShowTagSelector(!showTagSelector)}
              >
                {tags.length > 0 ? (
                  <div className="flex flex-wrap gap-1">
                    {tags.slice(0, 3).map((tag) => (
                      <span key={tag.id} className="bg-indigo-100 text-indigo-800 text-xs px-2 py-1 rounded">
                        {tag.name}
                      </span>
                    ))}
                    {tags.length > 3 && (
                      <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">
                        +{tags.length - 3}
                      </span>
                    )}
                  </div>
                ) : (
                  <span className="text-gray-500">选择标签</span>
                )}
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
              
              {showTagSelector && (
                <div 
                  ref={tagSelectorRef}
                  className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg"
                >
                  <div className="p-2">
                    <TagSelector
                      tags={tagGroups}
                      onTagsChange={handleTagsChange}
                      initialSelectedTags={tags}
                      className="h-[500px]"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* 题目内容 */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
          <h2 className="text-lg font-medium text-gray-800 mb-4">题目内容</h2>
          
          {/* 选项区域 - 单选题或多选题 */}
          {(problemType === 'single' || problemType === 'multiple') && (
            <div className="mb-6">
              <label className="block text-gray-700 text-sm font-medium mb-2">
                {problemType === 'single' ? '选项（单选）' : '选项（多选）'}
              </label>
              {options.map((option) => (
                <div key={option.id} className="mb-4">
                  <div className="flex items-center mb-2">
                    <button
                      onClick={() => setCorrectOption(option.id)}
                      className={`w-6 h-6 rounded-full mr-2 flex items-center justify-center border ${
                        option.isCorrect
                          ? 'bg-indigo-600 text-white border-indigo-600'
                          : 'bg-white text-gray-400 border-gray-300'
                      }`}
                    >
                      {problemType === 'single' ? (
                        <div className={`w-3 h-3 rounded-full ${option.isCorrect ? 'bg-white' : ''}`}></div>
                      ) : (
                        <FontAwesomeIcon icon={faCheckCircle} className={option.isCorrect ? 'text-white' : 'text-transparent'} />
                      )}
                    </button>
                    <div className="flex-1 font-medium">选项 {option.id}</div>
                    <button
                      onClick={() => removeOption(option.id)}
                      className="ml-2 text-gray-400 hover:text-red-500"
                      disabled={options.length <= 2}
                    >
                      <FontAwesomeIcon icon={faTrash} />
                    </button>
                  </div>
                  <div className="ml-8">
                    <RichEditor
                      value={option.content}
                      onChange={(content) => updateOptionContent(option.id, content)}
                      placeholder={`输入选项 ${option.id} 内容...`}
                      height={120}
                    />
                  </div>
                </div>
              ))}
              <button
                onClick={addOption}
                className="mt-2 flex items-center text-indigo-600 hover:text-indigo-800"
              >
                <FontAwesomeIcon icon={faPlus} className="mr-1" />
                添加选项
              </button>
            </div>
          )}
          
          {/* 判断题答案 */}
          {problemType === 'trueFalse' && (
            <div className="mb-6">
              <label className="block text-gray-700 text-sm font-medium mb-2">
                正确答案
              </label>
              <div className="flex space-x-4">
                <button
                  onClick={() => setTrueFalseAnswer(true)}
                  className={`flex-1 py-3 rounded-lg border flex justify-center items-center ${
                    trueFalseAnswer === true
                      ? 'border-green-600 bg-green-50 text-green-600'
                      : 'border-gray-300 text-gray-600 hover:border-gray-400'
                  }`}
                >
                  <FontAwesomeIcon icon={faCheckCircle} className="mr-2" />
                  正确
                </button>
                <button
                  onClick={() => setTrueFalseAnswer(false)}
                  className={`flex-1 py-3 rounded-lg border flex justify-center items-center ${
                    trueFalseAnswer === false
                      ? 'border-red-600 bg-red-50 text-red-600'
                      : 'border-gray-300 text-gray-600 hover:border-gray-400'
                  }`}
                >
                  <FontAwesomeIcon icon={faTimes} className="mr-2" />
                  错误
                </button>
              </div>
            </div>
          )}
          
          {/* 解析（富文本） */}
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-medium mb-2">
              答案解析
            </label>
            <RichEditor
              value={explanation}
              onChange={setExplanation}
              placeholder="输入答案解析..."
              height={200}
            />
          </div>
        </div>
        
        {/* 操作按钮 */}
        <div className="flex justify-between">
          <button
            onClick={handleGoBack}
            className="px-6 py-2 border border-gray-300 text-gray-600 rounded-lg hover:bg-gray-50 transition"
          >
            取消
          </button>
          <button
            onClick={handleSubmit}
            className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition"
          >
            创建题目
          </button>
        </div>
      </div>
    </div>
  )
} 