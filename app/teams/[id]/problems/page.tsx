'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import TeamHeader from '@/app/components/team/TeamHeader'
import TeamNavigation from '@/app/components/team/TeamNavigation'
import ProblemList from '@/app/components/team/problem/ProblemList'
import ProblemFilter from '@/app/components/team/problem/ProblemFilter'

interface Problem {
  id: string
  title: string
  difficulty: 'easy' | 'medium' | 'hard'
  tags: string[]
  solvedCount: number
  totalCount: number
  acceptance: number
  createdAt: string
  type: 'basic' | 'programming'
}

export default function TeamProblemsPage() {
  const params = useParams()
  const teamId = params.id as string
  const [problems, setProblems] = useState<Problem[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [filter, setFilter] = useState<'all' | 'easy' | 'medium' | 'hard'>('all')
  const [problemType, setProblemType] = useState<'basic' | 'programming'>('basic')

  useEffect(() => {
    // TODO: 从API获取题目数据
    setIsLoading(true)

    // 模拟数据
    const mockProblems: Problem[] = [
      {
        id: '101',
        title: '斐波那契数列',
        difficulty: 'easy',
        tags: ['动态规划', '数学'],
        solvedCount: 245,
        totalCount: 300,
        acceptance: 81.7,
        createdAt: '2023-10-15',
        type: 'basic'
      },
      {
        id: '102',
        title: '爬楼梯',
        difficulty: 'easy',
        tags: ['动态规划'],
        solvedCount: 210,
        totalCount: 280,
        acceptance: 75.0,
        createdAt: '2023-10-20',
        type: 'basic'
      },
      {
        id: '103',
        title: '打家劫舍',
        difficulty: 'medium',
        tags: ['动态规划', '数组'],
        solvedCount: 150,
        totalCount: 240,
        acceptance: 62.5,
        createdAt: '2023-11-01',
        type: 'basic'
      },
      {
        id: '104',
        title: '最长递增子序列',
        difficulty: 'medium',
        tags: ['动态规划', '二分查找'],
        solvedCount: 120,
        totalCount: 220,
        acceptance: 54.5,
        createdAt: '2023-11-05',
        type: 'basic'
      },
      {
        id: '105',
        title: '编辑距离',
        difficulty: 'hard',
        tags: ['动态规划', '字符串'],
        solvedCount: 95,
        totalCount: 200,
        acceptance: 47.5,
        createdAt: '2023-11-10',
        type: 'basic'
      },
      {
        id: '106',
        title: 'SQL查询优化',
        difficulty: 'medium',
        tags: ['数据库', 'SQL'],
        solvedCount: 135,
        totalCount: 230,
        acceptance: 58.7,
        createdAt: '2023-11-15',
        type: 'programming'
      },
      {
        id: '107',
        title: 'RESTful API设计',
        difficulty: 'medium',
        tags: ['后端', 'API'],
        solvedCount: 130,
        totalCount: 210,
        acceptance: 61.9,
        createdAt: '2023-11-20',
        type: 'programming'
      },
      {
        id: '108',
        title: '简单计算器实现',
        difficulty: 'easy',
        tags: ['前端', 'JavaScript'],
        solvedCount: 190,
        totalCount: 240,
        acceptance: 79.2,
        createdAt: '2023-11-25',
        type: 'programming'
      },
      {
        id: '109',
        title: '微服务架构设计',
        difficulty: 'medium',
        tags: ['系统设计', '微服务'],
        solvedCount: 180,
        totalCount: 250,
        acceptance: 72.0,
        createdAt: '2023-12-01',
        type: 'programming'
      },
      {
        id: '110',
        title: '分布式系统设计',
        difficulty: 'hard',
        tags: ['系统设计', '分布式'],
        solvedCount: 85,
        totalCount: 190,
        acceptance: 44.7,
        createdAt: '2023-12-05',
        type: 'programming'
      }
    ]

    // 模拟API延迟
    setTimeout(() => {
      setProblems(mockProblems)
      setIsLoading(false)
    }, 500)
  }, [teamId])

  // 根据题目类型和难度过滤题目
  const filteredProblems = problems.filter(
    problem => 
      problem.type === problemType && 
      (filter === 'all' || problem.difficulty === filter)
  )

  // 当前类型的所有题目
  const currentTypeProblems = problems.filter(problem => problem.type === problemType)

  return (
    <div className="min-h-screen bg-gray-50">

      {/* 主要内容 */}
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">题目库</h1>
            <p className="text-gray-600 mt-1">浏览和练习团队所有题目</p>
          </div>
          <div className="mt-4 lg:mt-0">
            <Link 
              href={`/teams/${teamId}/problems/create`}
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              创建题目
            </Link>
          </div>
        </div>

        {/* 题目类型切换 */}
        <div className="bg-white rounded-xl shadow-sm mb-6">
          <div className="flex">
            <button
              onClick={() => setProblemType('basic')}
              className={`flex-1 py-4 px-6 font-medium transition ${
                problemType === 'basic'
                  ? 'bg-indigo-50 text-indigo-600 border-b-2 border-indigo-600'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              基础题目库
            </button>
            <button
              onClick={() => setProblemType('programming')}
              className={`flex-1 py-4 px-6 font-medium transition ${
                problemType === 'programming'
                  ? 'bg-indigo-50 text-indigo-600 border-b-2 border-indigo-600'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              编程题目库
            </button>
          </div>
        </div>

        {/* 筛选器 */}
        <ProblemFilter
          currentFilter={filter}
          onFilterChange={setFilter}
          problemCount={{
            all: currentTypeProblems.length,
            easy: currentTypeProblems.filter(p => p.difficulty === 'easy').length,
            medium: currentTypeProblems.filter(p => p.difficulty === 'medium').length,
            hard: currentTypeProblems.filter(p => p.difficulty === 'hard').length
          }}
        />

        {/* 题目列表 */}
        <ProblemList
          problems={filteredProblems}
          isLoading={isLoading}
          teamId={teamId}
        />
      </div>
    </div>
  )
}
