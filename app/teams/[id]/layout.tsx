'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import TeamHeader from '@/app/components/team/TeamHeader'
import TeamNavigation from '@/app/components/team/TeamNavigation'
import teamService, { TeamDetailView } from '@/app/service/team-service'
import { TeamContext } from '@/app/contexts/TeamContext'
import React from 'react'

interface TeamLayoutProps {
  children: React.ReactNode
}

export default function TeamLayout({ children }: TeamLayoutProps) {
  const params = useParams()
  const teamId = params.id as string
  const [team, setTeam] = useState<TeamDetailView | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchTeamDetail()
  }, [teamId])

  const fetchTeamDetail = async () => {
    try {
      setLoading(true)
      const response = await teamService.getTeamDetail(parseInt(teamId))
      setTeam(response.data)
    } catch (error) {
      console.error('获取团队详情失败:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  if (!team) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-gray-600">团队不存在或已被删除</div>
      </div>
    )
  }

  return (
    <TeamContext.Provider value={{ team }}>
      <div className="min-h-screen bg-gray-50">
        {/* 团队头部信息 */}
        <TeamHeader team={team} />

        {/* 团队导航 */}
        <TeamNavigation />

        {/* 主要内容 */}
        {children}
      </div>
    </TeamContext.Provider>
  )
} 