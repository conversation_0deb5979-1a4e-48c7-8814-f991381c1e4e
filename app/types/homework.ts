export type QuestionType = 'single' | 'multiple' | 'judge' | 'programming'

export interface Question {
  id: string
  title: string
  type: QuestionType
  difficulty: 'easy' | 'medium' | 'hard'
  defaultPoints: number
  points: number
  source: '公共题库' | '团队题库'
  options?: Array<{
    label: string
    content: string
    isCorrect: boolean
  }>
  description?: string
  inputExample?: string
  outputExample?: string
  tags?: string[]
} 