import { IconProp } from '@fortawesome/fontawesome-svg-core'

export interface Team {
  id: string
  name: string
  description: string
  memberCount: number
  createdAt: string
  isCreator: boolean
  icon: IconProp
  gradientColors: {
    from: string
    to: string
  }
}

export interface TeamActivity {
  id: string
  type: 'homework' | 'member' | 'exam' | 'problem'
  title: string
  description: string
  time: string
  icon: IconProp
  iconBgColor: string
  iconTextColor: string
  link?: string
}

export interface TeamStats {
  joinedTeams: number
  createdTeams: number
  homeworkCount: number
}

export type TeamType = 'class' | 'contest' | 'group'

export type JoinMethod = 'open' | 'password' | 'verify'

export type TeamVisibility = 'public' | 'private'

export interface TeamPermissions {
  viewContent: boolean
  submitAnswer: boolean
  participate: boolean
  invite: boolean
}

export interface CreateTeamForm {
  name: string
  description: string
  type: TeamType
  joinMethod: JoinMethod
  password?: string
}

export interface TeamDetail extends Team {
  announcements: TeamAnnouncement[]
  recentHomeworks: TeamHomework[]
  recentExams: TeamExam[]
  popularProblemSets: TeamProblemSet[]
  topMembers: TeamMember[]
  stats: TeamDetailStats
}

export interface TeamAnnouncement {
  id: string
  content: string
  updatedAt: string
  pinned: boolean
}

export interface TeamHomework {
  id: string
  title: string
  description: string
  dueDate: string
  submissionCount: number
  totalMembers: number
  status: 'ongoing' | 'ended' | 'graded'
}

export interface TeamExam {
  id: string
  title: string
  description: string
  startTime: string
  duration: number // 分钟
  participantCount: number
  status: 'upcoming' | 'ongoing' | 'ended'
  totalQuestions?: number
  totalScore?: number
}

export interface TeamProblemSet {
  id: string
  title: string
  description: string
  problemCount: number
  difficulty: 'easy' | 'medium' | 'hard'
  recommended: boolean
}

export interface TeamMember {
  id: string
  name: string
  avatar: string
  role: 'creator' | 'admin' | 'member'
  activityScore: number
}

export interface TeamDetailStats {
  homeworkCompletionRate: number
  memberActivityDistribution: {
    high: number
    medium: number
    low: number
  }
  examScoreTrend: {
    date: string
    averageScore: number
  }[]
} 