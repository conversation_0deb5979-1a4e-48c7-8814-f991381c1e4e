'use client';

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUser, faShield } from '@fortawesome/free-solid-svg-icons';
import ProfileSettings from './components/ProfileSettings';
import SecuritySettings from './components/SecuritySettings';
import { useAppSelector, useAppDispatch } from '@/app/redux/hooks';
import { selectIsAuthenticated, logout } from '@/app/redux/features/authSlice';
import { useRouter } from 'next/navigation';
import ProfileService from '../service/profile-service';
import { MobileBindModal } from './components/modals';

// 绑定状态接口
interface BindingStatus {
  mobileBound: boolean;
  emailBound: boolean;
  wechatBound: boolean;
}

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState<'profile' | 'security'>('profile');
  const [showMobileBindModal, setShowMobileBindModal] = useState(false);
  const [bindingStatus, setBindingStatus] = useState<BindingStatus>({
    mobileBound: false,
    emailBound: false,
    wechatBound: false
  });
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const router = useRouter();
  const dispatch = useAppDispatch();

  // 如果未登录，重定向到登录页
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, router]);

  // 检查用户是否绑定手机号、邮箱和微信
  useEffect(() => {
    if (isAuthenticated) {
      ProfileService.getProfile()
        .then(response => {
          const profile = response.data;
          
          // 更新绑定状态
          setBindingStatus({
            mobileBound: profile.hasBindMobile,
            emailBound: profile.hasBindEmail,
            wechatBound: profile.hasBindWeChat
          });
          
          // 首先检查手机号绑定状态，优先级最高
          if (!profile.hasBindMobile) {
            // 如果未绑定手机号，切换到安全设置tab并显示绑定Modal
            setActiveTab('security');
            setShowMobileBindModal(true);
          }
        })
        .catch(error => {
          console.error('获取用户资料失败:', error);
        });
    }
  }, [isAuthenticated]);

  // 处理Modal关闭事件
  const handleModalClose = (success?: boolean) => {
    // 关闭弹窗
    setShowMobileBindModal(false);

    if (success) {
      // 绑定成功，刷新当前页面
      window.location.reload();
    } else {
      // 如果用户手动关闭绑定手机Modal，则退出登录
      dispatch(logout());
    }
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8 text-gray-800">账户设置</h1>
      
      {/* 标签导航 */}
      <div className="flex border-b border-gray-200 mb-8">
        <button
          className={`px-4 py-2 font-medium text-sm mr-4 border-b-2 ${
            activeTab === 'profile' 
              ? 'border-indigo-600 text-indigo-600' 
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          }`}
          onClick={() => setActiveTab('profile')}
        >
          <FontAwesomeIcon icon={faUser} className="mr-2" />
          用户资料
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm border-b-2 ${
            activeTab === 'security' 
              ? 'border-indigo-600 text-indigo-600' 
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          }`}
          onClick={() => setActiveTab('security')}
        >
          <FontAwesomeIcon icon={faShield} className="mr-2" />
          账号安全
        </button>
      </div>
      
      {/* 标签内容 */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        {activeTab === 'profile' ? (
          <ProfileSettings />
        ) : (
          <SecuritySettings 
            mobileBound={bindingStatus.mobileBound}
            emailBound={bindingStatus.emailBound}
            wechatBound={bindingStatus.wechatBound}
          />
        )}
      </div>

      {/* 绑定手机号Modal */}
      <MobileBindModal 
        isOpen={showMobileBindModal}
        onClose={handleModalClose}
      />
    </div>
  );
} 