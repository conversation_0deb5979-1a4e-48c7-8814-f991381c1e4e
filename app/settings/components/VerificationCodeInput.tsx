'use client';

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSpinner } from '@fortawesome/free-solid-svg-icons';

interface VerificationCodeInputProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onRequestCode: () => void;
  isLoading: boolean;
  error?: string;
  label?: string;
  placeholder?: string;
}

export const VerificationCodeInput: React.FC<VerificationCodeInputProps> = ({
  value,
  onChange,
  onRequestCode,
  isLoading,
  error,
  label = "验证码",
  placeholder = "请输入验证码"
}) => {
  const [countdown, setCountdown] = useState(0);

  const startCountdown = () => {
    setCountdown(60);
    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const handleRequestCode = () => {
    if (countdown > 0 || isLoading) return;
    onRequestCode();
    startCountdown();
  };

  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-1">{label}</label>
      <div className="flex items-center">
        <input
          type="text"
          value={value}
          onChange={onChange}
          className={`flex-1 px-3 py-2 border ${error ? 'border-red-500' : 'border-gray-300'} rounded-l-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent`}
          placeholder={placeholder}
        />
        <button
          type="button"
          onClick={handleRequestCode}
          disabled={countdown > 0 || isLoading}
          className="px-3 py-2 bg-indigo-600 text-white rounded-r-md shadow-sm hover:bg-indigo-700 focus:outline-none disabled:bg-indigo-300 min-w-[100px]"
        >
          {isLoading ? (
            <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
          ) : countdown > 0 ? (
            `${countdown}秒`
          ) : (
            "获取验证码"
          )}
        </button>
      </div>
      {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
    </div>
  );
};

export default VerificationCodeInput; 