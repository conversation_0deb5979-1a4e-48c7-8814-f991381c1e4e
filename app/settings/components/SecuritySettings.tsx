'use client';

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faEnvelope,
  faMobile,
  faLock,
  faInfoCircle,
  faSpinner,
  faCheck,
  faTimes
} from '@fortawesome/free-solid-svg-icons';
import { faWeixin } from '@fortawesome/free-brands-svg-icons';
import { useAppSelector } from '@/app/redux/hooks';
import { selectUser } from '@/app/redux/features/authSlice';
import {
  EmailBindModal,
  EmailChangeModal,
  MobileBindModal,
  MobileChangeModal,
  PasswordResetModal,
  WechatBindModal,
  WechatUnbindModal
} from './modals';
import EmailUnbindModal from './modals/EmailUnbindModal';

type SecuritySection = 'email' | 'mobile' | 'password' | 'wechat';

type ModalType =
  | 'emailBind'
  | 'emailChange'
  | 'emailUnbind'
  | 'mobileBind'
  | 'mobileChange'
  | 'passwordReset'
  | 'wechatBind'
  | 'wechatUnbind'
  | null;

interface SecuritySettingsProps {
  mobileBound: boolean;
  emailBound: boolean;
  wechatBound: boolean;
}


export default function SecuritySettings({ mobileBound, emailBound: initialEmailBound, wechatBound }: SecuritySettingsProps) {
  const user = useAppSelector(selectUser);
  const [activeModal, setActiveModal] = useState<ModalType>(null);
  // 使用本地状态跟踪邮箱绑定状态
  const [emailBound, setEmailBound] = useState<boolean>(initialEmailBound);
  // 使用本地状态跟踪微信绑定状态
  const [wechatBoundState, setWechatBound] = useState<boolean>(wechatBound);

  // 处理打开弹窗的函数
  const handleOpenModal = (modalType: ModalType) => {
    setActiveModal(modalType);
  };

  // 处理关闭弹窗的函数
  const handleCloseModal = () => {
    setActiveModal(null);
    window.location.hash = '';
  };

  // 邮箱解绑成功后的回调
  const handleEmailUnbindSuccess = () => {
    setEmailBound(false);
    handleCloseModal();
  };

  // 邮箱绑定成功后的回调
  const handleEmailBindSuccess = () => {
    setEmailBound(true);
    handleCloseModal();
  };

  // 邮箱更换成功后的回调
  const handleEmailChangeSuccess = () => {
    setEmailBound(true);
    handleCloseModal();
  };

  // 微信绑定成功后的回调
  const handleWechatBindSuccess = () => {
    setWechatBound(true);
    handleCloseModal();
  };

  // 微信解绑成功后的回调
  const handleWechatUnbindSuccess = () => {
    setWechatBound(false);
    handleCloseModal();
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">账号安全</h2>

      <div className="space-y-6">
        {/* 电子邮箱 */}
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          <div className="flex items-center justify-between px-4 py-3 bg-gray-50">
            <div className="flex items-center">
              <FontAwesomeIcon icon={faEnvelope} className="text-indigo-600 mr-3" />
              <h3 className="font-medium">电子邮箱</h3>
            </div>
            {emailBound ? (
              <span className="text-sm text-gray-600">{user?.email}</span>
            ) : (
              <span className="text-sm text-gray-500">未绑定</span>
            )}
          </div>

          <div className="px-4 py-3 flex justify-end">
            <button
              onClick={() => handleOpenModal(emailBound ? 'emailChange' : 'emailBind')}
              className="text-indigo-600 text-sm hover:text-indigo-800"
            >
              {emailBound ? '更换邮箱' : '绑定邮箱'}
            </button>
            {emailBound && (
              <button
                onClick={() => handleOpenModal('emailUnbind')}
                className="text-red-600 text-sm hover:text-red-800 ml-4"
              >
                解绑邮箱
              </button>
            )}
          </div>
        </div>

        {/* 手机号 */}
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          <div className="flex items-center justify-between px-4 py-3 bg-gray-50">
            <div className="flex items-center">
              <FontAwesomeIcon icon={faMobile} className="text-indigo-600 mr-3" />
              <h3 className="font-medium">手机号</h3>
            </div>
            {mobileBound ? (
              <span className="text-sm text-gray-600">{user?.mobile}</span>
            ) : (
              <span className="text-sm text-gray-500">未绑定</span>
            )}
          </div>

          <div className="px-4 py-3 flex justify-end">
            {!mobileBound ? (
              <button
                onClick={() => handleOpenModal('mobileBind')}
                className="text-indigo-600 text-sm hover:text-indigo-800"
              >
                绑定手机号
              </button>
            ) : (
              <button
                onClick={() => handleOpenModal('mobileChange')}
                className="text-indigo-600 text-sm hover:text-indigo-800"
              >
                更换手机号
              </button>
            )}
          </div>
        </div>

        {/* 账户密码 */}
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          <div className="flex items-center justify-between px-4 py-3 bg-gray-50">
            <div className="flex items-center">
              <FontAwesomeIcon icon={faLock} className="text-indigo-600 mr-3" />
              <h3 className="font-medium">账户密码</h3>
            </div>
            <span className="text-sm text-gray-600">********</span>
          </div>

          <div className="px-4 py-3 flex justify-end">
            <button
              onClick={() => handleOpenModal('passwordReset')}
              className="text-indigo-600 text-sm hover:text-indigo-800"
            >
              重置密码
            </button>
          </div>
        </div>

        {/* 微信绑定 */}
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          <div className="flex items-center justify-between px-4 py-3 bg-gray-50">
            <div className="flex items-center">
              <FontAwesomeIcon icon={faWeixin} className="text-green-600 mr-3" />
              <h3 className="font-medium">微信绑定</h3>
            </div>
            <span className="text-sm text-gray-500">
              {wechatBoundState ? '已绑定' : '未绑定'}
            </span>
          </div>

          <div className="px-4 py-3 flex justify-end">
            {!wechatBoundState ? (
              <button
                onClick={() => handleOpenModal('wechatBind')}
                className="text-indigo-600 text-sm hover:text-indigo-800"
              >
                绑定微信
              </button>
            ) : (
              <button
                onClick={() => handleOpenModal('wechatUnbind')}
                className="text-red-600 text-sm hover:text-red-800"
              >
                取消绑定
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 各种Modal */}
      <EmailBindModal
        isOpen={activeModal === 'emailBind'}
        onClose={handleCloseModal}
        userMobile={user?.mobile || ''}
        onSuccess={handleEmailBindSuccess}
      />

      <EmailChangeModal
        isOpen={activeModal === 'emailChange'}
        onClose={handleCloseModal}
        userEmail={user?.email || ''}
        userMobile={user?.mobile || ''}
        onSuccess={handleEmailChangeSuccess}
      />

      <MobileBindModal
        isOpen={activeModal === 'mobileBind'}
        onClose={handleCloseModal}
      />

      <MobileChangeModal
        isOpen={activeModal === 'mobileChange'}
        onClose={handleCloseModal}
        userMobile={user?.mobile || ''}
      />

      <PasswordResetModal
        isOpen={activeModal === 'passwordReset'}
        onClose={handleCloseModal}
        userMobile={user?.mobile || ''}
      />

      <WechatBindModal
        isOpen={activeModal === 'wechatBind'}
        onClose={handleCloseModal}
        onSuccess={handleWechatBindSuccess}
      />

      <WechatUnbindModal
        isOpen={activeModal === 'wechatUnbind'}
        onClose={handleCloseModal}
        onSuccess={handleWechatUnbindSuccess}
      />

      <EmailUnbindModal
        isOpen={activeModal === 'emailUnbind'}
        onClose={handleCloseModal}
        userEmail={user?.email || ''}
        onSuccess={handleEmailUnbindSuccess}
      />
    </div>
  );
}
