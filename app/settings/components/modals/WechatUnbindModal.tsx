'use client';

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faSpinner, faCheck, faInfoCircle, faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';
import ProfileService from '@/app/service/profile-service';
import { useDispatch } from 'react-redux';
import { setUser } from '@/app/redux/features/authSlice';

interface WechatUnbindModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export const WechatUnbindModal: React.FC<WechatUnbindModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  if (!isOpen) return null;

  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 处理解绑微信
  const handleUnbindWechat = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // 解绑微信
      await ProfileService.unbindWeChat();

      // 获取更新后的用户信息并更新Redux
      const profileResponse = await ProfileService.getProfile();
      if (profileResponse && profileResponse.data) {
        // 更新Redux中的用户信息
        dispatch(setUser({
          wechatBound: false
        }));
      }

      setSuccess(true);

      // 如果提供了onSuccess回调，则调用它
      if (onSuccess) {
        setTimeout(() => {
          onSuccess();
        }, 1500);
      } else {
        setTimeout(() => {
          onClose();
        }, 1500);
      }
    } catch (error) {
      console.error('解绑微信失败:', error);
      setError('解绑失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
        <div className="relative w-full max-w-md bg-white rounded-lg shadow-xl mx-4">
          <div className="px-6 py-8 flex flex-col items-center">
            <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-4">
              <FontAwesomeIcon icon={faCheck} className="text-green-600 text-lg" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">微信解绑成功</h3>
            <p className="text-sm text-gray-500">您的账号已成功解除微信绑定</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="relative w-full max-w-md bg-white rounded-lg shadow-xl mx-4">
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
        >
          <FontAwesomeIcon icon={faTimes} className="text-xl" />
        </button>

        {/* 主内容 */}
        <div className="px-6 py-8">
          {/* 标题 */}
          <h2 className="text-xl font-bold text-center mb-6">解绑微信</h2>

          <div className="text-center py-4">
            <div className="mb-6 flex flex-col items-center">
              <div className="w-16 h-16 rounded-full bg-yellow-50 flex items-center justify-center mb-4">
                <FontAwesomeIcon icon={faExclamationTriangle} className="text-yellow-500 text-xl" />
              </div>
              <p className="mb-2 text-gray-700 font-medium">确定要解绑微信吗？</p>
              <p className="text-sm text-gray-500 max-w-xs">
                解绑后，您将无法使用微信快捷登录，需要重新绑定才能恢复
              </p>
            </div>

            {error && (
              <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md">
                {error}
              </div>
            )}
          </div>

          <div className="mt-4 flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none"
            >
              取消
            </button>
            <button
              type="button"
              onClick={handleUnbindWechat}
              disabled={isLoading}
              className="px-4 py-2 bg-red-600 text-white rounded-md shadow-sm hover:bg-red-700 focus:outline-none disabled:bg-red-300 flex items-center justify-center min-w-[80px]"
            >
              {isLoading ? (
                <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
              ) : '确认解绑'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WechatUnbindModal;
