'use client';

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faSpinner, faCheck, faInfoCircle } from '@fortawesome/free-solid-svg-icons';
import { VerificationCodeInput } from '../VerificationCodeInput';
import MessageService from '@/app/service/message-service';
import ProfileService from '@/app/service/profile-service';
import { useDispatch } from 'react-redux';
import { setUser } from '@/app/redux/features/authSlice';

interface EmailUnbindModalProps {
  isOpen: boolean;
  onClose: () => void;
  userEmail: string;
  onSuccess?: () => void;
}

export const EmailUnbindModal: React.FC<EmailUnbindModalProps> = ({
  isOpen,
  onClose,
  userEmail,
  onSuccess
}) => {
  if (!isOpen) return null;

  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  // 表单状态
  const [emailCode, setEmailCode] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 请求邮箱验证码
  const requestEmailCode = async () => {
    if (!userEmail) {
      setErrors(prev => ({...prev, email: '您尚未绑定邮箱'}));
      return;
    }

    setIsLoading(true);
    try {
      await MessageService.sendEmailVerifyCode(userEmail);
      // 清除错误信息
      if (errors.email) {
        setErrors(prev => {
          const newErrors = {...prev};
          delete newErrors.email;
          return newErrors;
        });
      }
    } catch (error) {
      console.error('发送验证码失败:', error);
      setErrors(prev => ({...prev, email: '发送验证码失败，请稍后重试'}));
    } finally {
      setIsLoading(false);
    }
  };

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!emailCode) {
      newErrors.emailCode = '请输入验证码';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理表单提交
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      // 验证邮箱验证码
      const isValid = await ProfileService.validateEmailCode({
        type: 2, // 2: 解绑邮箱
        code: emailCode
      });

      if (!isValid) {
        setErrors(prev => ({...prev, emailCode: '验证码错误'}));
        setIsLoading(false);
        return;
      }

      // 解绑邮箱
      await ProfileService.unbindEmail({
        code: emailCode
      });

      // 获取更新后的用户信息并更新Redux
      const profileResponse = await ProfileService.getProfile();
      if (profileResponse && profileResponse.data) {
        // 更新Redux中的用户信息 - 清空email
        dispatch(setUser({
          email: undefined
        }));
      }

      setSuccess(true);
      // 如果提供了onSuccess回调，则调用它
      if (onSuccess) {
        setTimeout(() => {
          onSuccess();
        }, 1500);
      } else {
        setTimeout(() => {
          onClose();
        }, 1500);
      }
    } catch (error) {
      console.error('操作失败:', error);
      setErrors(prev => ({...prev, submit: '操作失败，请稍后重试'}));
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
        <div className="relative w-full max-w-md bg-white rounded-lg shadow-xl mx-4">
          <div className="px-6 py-8 flex flex-col items-center">
            <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-4">
              <FontAwesomeIcon icon={faCheck} className="text-green-600 text-lg" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">邮箱解绑成功</h3>
            <p className="text-sm text-gray-500">您的账号已成功解绑邮箱</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="relative w-full max-w-md bg-white rounded-lg shadow-xl mx-4">
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
        >
          <FontAwesomeIcon icon={faTimes} className="text-xl" />
        </button>

        {/* 主内容 */}
        <div className="px-6 py-8">
          {/* 标题 */}
          <h2 className="text-xl font-bold text-center mb-6">
            解绑邮箱
          </h2>

          {/* 提示信息 */}
          {!userEmail ? (
            <div className="mb-4 p-3 bg-yellow-50 text-yellow-800 rounded-md flex items-start">
              <FontAwesomeIcon icon={faInfoCircle} className="mr-2 mt-1" />
              <div>
                <p className="text-sm">您尚未绑定邮箱。</p>
              </div>
            </div>
          ) : (
            <div className="mb-4 p-3 bg-orange-50 text-orange-800 rounded-md flex items-start">
              <FontAwesomeIcon icon={faInfoCircle} className="mr-2 mt-1" />
              <div>
                <p className="text-sm">解绑邮箱后，您将无法接收系统通知，也无法通过邮箱找回密码。</p>
              </div>
            </div>
          )}

          <div className="space-y-4">
            {/* 验证码输入 */}
            <div>
              <VerificationCodeInput
                value={emailCode}
                onChange={(e) => {
                  setEmailCode(e.target.value.replace(/\D/g, ''));
                  if (errors.emailCode) {
                    setErrors(prev => {
                      const newErrors = {...prev};
                      delete newErrors.emailCode;
                      return newErrors;
                    });
                  }
                }}
                onRequestCode={requestEmailCode}
                isLoading={isLoading}
                error={errors.emailCode}
                label="邮箱验证码"
                placeholder="请输入验证码"
              />
              <p className="mt-2 text-sm text-gray-500">
                验证码已发送至 {userEmail}
              </p>
            </div>

            {errors.submit && (
              <p className="text-sm text-red-600">{errors.submit}</p>
            )}
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none"
            >
              取消
            </button>
            <button
              type="button"
              onClick={handleSubmit}
              disabled={isLoading || !userEmail}
              className="px-4 py-2 bg-red-600 text-white rounded-md shadow-sm hover:bg-red-700 focus:outline-none disabled:bg-gray-300 flex items-center justify-center min-w-[80px]"
            >
              {isLoading ? (
                <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
              ) : (
                '确认解绑'
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailUnbindModal;
