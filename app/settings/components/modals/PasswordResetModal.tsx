'use client';

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faSpinner, faCheck, faInfoCircle } from '@fortawesome/free-solid-svg-icons';
import { VerificationCodeInput } from '../VerificationCodeInput';
import ProfileService from '@/app/service/profile-service';
import MessageService from '@/app/service/message-service';
import { toast } from 'react-hot-toast';

interface PasswordResetModalProps {
  isOpen: boolean;
  onClose: () => void;
  userMobile: string;
}

export const PasswordResetModal: React.FC<PasswordResetModalProps> = ({
  isOpen,
  onClose,
  userMobile
}) => {
  if (!isOpen) return null;

  const [step, setStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  // 表单状态
  const [mobileCode, setMobileCode] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 请求手机验证码
  const requestMobileCode = async () => {
    if (!userMobile) {
      setErrors(prev => ({...prev, mobile: '请先绑定手机号'}));
      return;
    }

    setIsLoading(true);
    try {
      // 调用发送验证码接口
      await MessageService.sendAuthVerifyCode();
      toast.success('验证码已发送');
    } catch (error) {
      console.error('发送验证码失败:', error);
      toast.error('发送验证码失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (step === 1) {
      if (!mobileCode) {
        newErrors.mobileCode = '请输入验证码';
      }
      // 移除验证码长度校验
    } else if (step === 2) {
      if (!newPassword) {
        newErrors.newPassword = '请输入新密码';
      } else if (newPassword.length < 8) {
        newErrors.newPassword = '密码长度至少为8位';
      }

      if (!confirmPassword) {
        newErrors.confirmPassword = '请确认新密码';
      } else if (confirmPassword !== newPassword) {
        newErrors.confirmPassword = '两次输入的密码不一致';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理下一步
  const handleNextStep = async () => {
    if (!validateForm()) return;

    if (step === 1) {
      // 验证手机验证码
      setIsLoading(true);
      try {
        const isValid = await ProfileService.validatePhoneCode({
          type: 2, // 2: 重置密码
          code: mobileCode
        });

        if (isValid.data) {
          setStep(2);
        } else {
          setErrors(prev => ({...prev, mobileCode: '验证码错误'}));
          toast.error('验证码错误，请重新输入');
        }
      } catch (error) {
        console.error('验证码校验失败:', error);
        toast.error('验证码校验失败，请稍后重试');
      } finally {
        setIsLoading(false);
      }
      return;
    }

    await handleSubmit();
  };

  // 处理表单提交
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      // 调用重置密码接口
      await ProfileService.updatePassword({
        password: newPassword
      });

      setSuccess(true);
      toast.success('密码重置成功');
      setTimeout(() => {
        onClose();
      }, 1500);
    } catch (error) {
      console.error('重置密码失败:', error);
      toast.error('重置密码失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
        <div className="relative w-full max-w-md bg-white rounded-lg shadow-xl mx-4">
          <div className="px-6 py-8 flex flex-col items-center">
            <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-4">
              <FontAwesomeIcon icon={faCheck} className="text-green-600 text-lg" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">密码重置成功</h3>
            <p className="text-sm text-gray-500">您的账号密码已成功重置</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="relative w-full max-w-md bg-white rounded-lg shadow-xl mx-4">
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
        >
          <FontAwesomeIcon icon={faTimes} className="text-xl" />
        </button>

        {/* 主内容 */}
        <div className="px-6 py-8">
          {/* 步骤指示器 */}
          <div className="flex items-center justify-center mb-8 space-x-4">
            <div className="flex items-center">
              <div className={`flex items-center justify-center w-10 h-10 rounded-full text-white bg-indigo-600`}>
                1
              </div>
              <div className={`h-1 w-16 ${step > 1 ? 'bg-indigo-600' : 'bg-gray-200'}`} />
              <div className={`flex items-center justify-center w-10 h-10 rounded-full text-white ${
                step >= 2 ? 'bg-indigo-600' : 'bg-gray-200'
              }`}>
                2
              </div>
            </div>
          </div>

          {/* 步骤标题 */}
          <h2 className="text-xl font-bold text-center mb-6">
            {step === 1 ? '验证身份' : '设置新密码'}
          </h2>

          {/* 提示信息 */}
          {!userMobile && (
            <div className="mb-4 p-3 bg-yellow-50 text-yellow-800 rounded-md flex items-start">
              <FontAwesomeIcon icon={faInfoCircle} className="mr-2 mt-1" />
              <div>
                <p className="text-sm">您需要先绑定手机号才能重置密码。</p>
              </div>
            </div>
          )}

          <div className="space-y-4">
            {/* 第一步：手机验证码 */}
            {step === 1 && (
              <div>
                <VerificationCodeInput
                  value={mobileCode}
                  onChange={(e) => {
                    setMobileCode(e.target.value.replace(/\D/g, ''));
                    if (errors.mobileCode) {
                      setErrors(prev => {
                        const newErrors = {...prev};
                        delete newErrors.mobileCode;
                        return newErrors;
                      });
                    }
                  }}
                  onRequestCode={requestMobileCode}
                  isLoading={isLoading}
                  error={errors.mobileCode}
                  label="手机验证码"
                  placeholder="请输入验证码"
                />
                {userMobile && (
                  <p className="mt-2 text-sm text-gray-500">
                    验证码已发送至 {userMobile}
                  </p>
                )}
              </div>
            )}

            {/* 第二步：设置新密码 */}
            {step === 2 && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    新密码
                  </label>
                  <input
                    type="password"
                    value={newPassword}
                    onChange={(e) => {
                      setNewPassword(e.target.value);
                      if (errors.newPassword) {
                        setErrors(prev => {
                          const newErrors = {...prev};
                          delete newErrors.newPassword;
                          return newErrors;
                        });
                      }
                    }}
                    className={`w-full px-3 py-2 border ${errors.newPassword ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent`}
                    placeholder="请输入新密码"
                  />
                  {errors.newPassword && (
                    <p className="mt-1 text-sm text-red-600">{errors.newPassword}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    确认新密码
                  </label>
                  <input
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => {
                      setConfirmPassword(e.target.value);
                      if (errors.confirmPassword) {
                        setErrors(prev => {
                          const newErrors = {...prev};
                          delete newErrors.confirmPassword;
                          return newErrors;
                        });
                      }
                    }}
                    className={`w-full px-3 py-2 border ${errors.confirmPassword ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent`}
                    placeholder="请再次输入新密码"
                  />
                  {errors.confirmPassword && (
                    <p className="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>
                  )}
                </div>
              </>
            )}
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none"
            >
              取消
            </button>
            <button
              type="button"
              onClick={handleNextStep}
              disabled={isLoading || !userMobile}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none disabled:bg-gray-300 flex items-center justify-center min-w-[80px]"
            >
              {isLoading ? (
                <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
              ) : (
                <>
                  {step === 1 ? '下一步' : '确认'}
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PasswordResetModal;
