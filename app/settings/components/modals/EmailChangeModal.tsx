'use client';

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faSpinner, faCheck, faInfoCircle } from '@fortawesome/free-solid-svg-icons';
import { VerificationCodeInput } from '../VerificationCodeInput';
import MessageService from '@/app/service/message-service';
import ProfileService from '@/app/service/profile-service';
import { useDispatch } from 'react-redux';
import { setUser, selectUser } from '@/app/redux/features/authSlice';
import { useAppSelector } from '@/app/redux/hooks';

interface EmailChangeModalProps {
  isOpen: boolean;
  onClose: () => void;
  userEmail: string;
  userMobile: string;
  onSuccess?: () => void;
}

export const EmailChangeModal: React.FC<EmailChangeModalProps> = ({
  isOpen,
  onClose,
  userEmail,
  userMobile,
  onSuccess
}) => {
  if (!isOpen) return null;

  const dispatch = useDispatch();
  const currentUser = useAppSelector(selectUser);
  const [step, setStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  // 表单状态
  const [newEmail, setNewEmail] = useState('');
  const [currentEmailCode, setCurrentEmailCode] = useState('');
  const [newEmailCode, setNewEmailCode] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 请求当前邮箱验证码
  const requestCurrentEmailCode = async () => {
    if (!userEmail) {
      setErrors(prev => ({...prev, currentEmail: '您尚未绑定邮箱'}));
      return;
    }

    setIsLoading(true);
    try {
      await MessageService.sendEmailVerifyCode(userEmail);
      // 清除错误信息
      if (errors.currentEmail) {
        setErrors(prev => {
          const newErrors = {...prev};
          delete newErrors.currentEmail;
          return newErrors;
        });
      }
    } catch (error) {
      console.error('发送验证码失败:', error);
      setErrors(prev => ({...prev, currentEmail: '发送验证码失败，请稍后重试'}));
    } finally {
      setIsLoading(false);
    }
  };

  // 请求新邮箱验证码
  const requestNewEmailCode = async () => {
    if (!validateEmail(newEmail)) {
      setErrors(prev => ({...prev, email: '请输入有效的邮箱地址'}));
      return;
    }

    setIsLoading(true);
    try {
      await MessageService.sendEmailVerifyCode(newEmail);
      // 清除错误信息
      if (errors.email) {
        setErrors(prev => {
          const newErrors = {...prev};
          delete newErrors.email;
          return newErrors;
        });
      }
    } catch (error) {
      console.error('发送验证码失败:', error);
      setErrors(prev => ({...prev, email: '发送验证码失败，请稍后重试'}));
    } finally {
      setIsLoading(false);
    }
  };

  // 验证邮箱格式
  const validateEmail = (email: string): boolean => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (step === 1) {
      if (!currentEmailCode) {
        newErrors.currentEmailCode = '请输入验证码';
      }
    } else if (step === 2) {
      if (!newEmail) {
        newErrors.email = '请输入新邮箱';
      } else if (!validateEmail(newEmail)) {
        newErrors.email = '请输入有效的邮箱地址';
      }
      if (!newEmailCode) {
        newErrors.newEmailCode = '请输入验证码';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理下一步
  const handleNextStep = async () => {
    if (!validateForm()) return;

    if (step === 1) {
      // 验证当前邮箱验证码
      setIsLoading(true);
      try {
        const isValid = await ProfileService.validateEmailCode({
          type: 1, // 1: 更换邮箱
          code: currentEmailCode
        });

        if (isValid.data) {
          setStep(2);
        } else {
          setErrors(prev => ({...prev, currentEmailCode: '验证码错误'}));
        }
      } catch (error) {
        console.error('验证失败:', error);
        setErrors(prev => ({...prev, currentEmailCode: '验证失败，请稍后重试'}));
      } finally {
        setIsLoading(false);
      }
    } else if (step === 2) {
      await handleSubmit();
    }
  };

  // 处理表单提交
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      // 更新邮箱
      await ProfileService.updateEmail({
        email: newEmail,
        code: newEmailCode
      });
      
      // 获取更新后的用户信息并更新Redux
      const profileResponse = await ProfileService.getProfile();
      if (profileResponse && profileResponse.data) {
        // 更新Redux中的用户信息
        if (currentUser) {
          dispatch(setUser({
            ...currentUser,
            email: newEmail
          }));
        }
      }
      
      setSuccess(true);
      
      // 如果提供了onSuccess回调，则调用它
      if (onSuccess) {
        setTimeout(() => {
          onSuccess();
        }, 1500);
      } else {
        setTimeout(() => {
          onClose();
        }, 1500);
      }
    } catch (error) {
      console.error('操作失败:', error);
      setErrors(prev => ({...prev, submit: '操作失败，请稍后重试'}));
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
        <div className="relative w-full max-w-md bg-white rounded-lg shadow-xl mx-4">
          <div className="px-6 py-8 flex flex-col items-center">
            <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-4">
              <FontAwesomeIcon icon={faCheck} className="text-green-600 text-lg" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">邮箱更换成功</h3>
            <p className="text-sm text-gray-500">您的账号已成功更换邮箱</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="relative w-full max-w-md bg-white rounded-lg shadow-xl mx-4">
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
        >
          <FontAwesomeIcon icon={faTimes} className="text-xl" />
        </button>

        {/* 主内容 */}
        <div className="px-6 py-8">
          {/* 步骤指示器 */}
          <div className="flex items-center justify-center mb-8 space-x-4">
            <div className="flex items-center">
              <div className={`flex items-center justify-center w-10 h-10 rounded-full text-white bg-indigo-600`}>
                1
              </div>
              <div className={`h-1 w-16 ${step > 1 ? 'bg-indigo-600' : 'bg-gray-200'}`} />
              <div className={`flex items-center justify-center w-10 h-10 rounded-full text-white ${
                step >= 2 ? 'bg-indigo-600' : 'bg-gray-200'
              }`}>
                2
              </div>
            </div>
          </div>

          {/* 步骤标题 */}
          <h2 className="text-xl font-bold text-center mb-6">
            {step === 1 ? '验证当前邮箱' : '输入新邮箱'}
          </h2>

          {/* 提示信息 */}
          {!userEmail && (
            <div className="mb-4 p-3 bg-yellow-50 text-yellow-800 rounded-md flex items-start">
              <FontAwesomeIcon icon={faInfoCircle} className="mr-2 mt-1" />
              <div>
                <p className="text-sm">您需要先绑定邮箱才能进行更换操作。</p>
              </div>
            </div>
          )}

          <div className="space-y-4">
            {/* 第一步：验证当前邮箱 */}
            {step === 1 && (
              <div>
                <VerificationCodeInput
                  value={currentEmailCode}
                  onChange={(e) => {
                    setCurrentEmailCode(e.target.value.replace(/\D/g, ''));
                    if (errors.currentEmailCode) {
                      setErrors(prev => {
                        const newErrors = {...prev};
                        delete newErrors.currentEmailCode;
                        return newErrors;
                      });
                    }
                  }}
                  onRequestCode={requestCurrentEmailCode}
                  isLoading={isLoading}
                  error={errors.currentEmailCode}
                  label="邮箱验证码"
                  placeholder="请输入验证码"
                />
                <p className="mt-2 text-sm text-gray-500">
                  验证码已发送至 {userEmail}
                </p>
              </div>
            )}

            {/* 第二步：输入新邮箱和验证码 */}
            {step === 2 && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    新邮箱地址
                  </label>
                  <input
                    type="email"
                    value={newEmail}
                    onChange={(e) => {
                      setNewEmail(e.target.value);
                      if (errors.email) {
                        setErrors(prev => {
                          const newErrors = {...prev};
                          delete newErrors.email;
                          return newErrors;
                        });
                      }
                    }}
                    className={`w-full px-3 py-2 border ${errors.email ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent`}
                    placeholder="请输入新邮箱地址"
                  />
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-600">{errors.email}</p>
                  )}
                </div>

                <div>
                  <VerificationCodeInput
                    value={newEmailCode}
                    onChange={(e) => {
                      setNewEmailCode(e.target.value.replace(/\D/g, ''));
                      if (errors.newEmailCode) {
                        setErrors(prev => {
                          const newErrors = {...prev};
                          delete newErrors.newEmailCode;
                          return newErrors;
                        });
                      }
                    }}
                    onRequestCode={requestNewEmailCode}
                    isLoading={isLoading}
                    error={errors.newEmailCode}
                    label="邮箱验证码"
                    placeholder="请输入验证码"
                  />
                  <p className="mt-2 text-sm text-gray-500">
                    验证码已发送至 {newEmail}
                  </p>
                </div>

                {errors.submit && (
                  <p className="text-sm text-red-600">{errors.submit}</p>
                )}
              </div>
            )}
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none"
            >
              取消
            </button>
            <button
              type="button"
              onClick={handleNextStep}
              disabled={isLoading || !userEmail}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none disabled:bg-gray-300 flex items-center justify-center min-w-[80px]"
            >
              {isLoading ? (
                <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
              ) : (
                <>
                  {step < 2 ? '下一步' : '确认'}
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailChangeModal;
