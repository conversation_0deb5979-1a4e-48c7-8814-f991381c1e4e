'use client';

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faSpinner, faCheck } from '@fortawesome/free-solid-svg-icons';
import { VerificationCodeInput } from '../VerificationCodeInput';
import ProfileService, { Profile } from '@/app/service/profile-service';
import MessageService from '@/app/service/message-service';
import { toast } from 'react-hot-toast';
import { useDispatch } from 'react-redux';
import { setUser } from '@/app/redux/features/authSlice';

interface MobileChangeModalProps {
  isOpen: boolean;
  onClose: () => void;
  userMobile: string;
}

export const MobileChangeModal: React.FC<MobileChangeModalProps> = ({
  isOpen,
  onClose,
  userMobile
}) => {
  if (!isOpen) return null;

  const dispatch = useDispatch();
  const [step, setStep] = useState<number>(1);
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  // 表单状态
  const [newMobile, setNewMobile] = useState('');
  const [oldMobileCode, setOldMobileCode] = useState('');
  const [newMobileCode, setNewMobileCode] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 请求旧手机验证码
  const requestOldMobileCode = async () => {
    if (!userMobile) {
      return;
    }

    setIsLoading(true);
    try {
      // 调用发送验证码接口
      await MessageService.sendAuthVerifyCode();
      toast.success('验证码已发送');
    } catch (error) {
      console.error('发送验证码失败:', error);
      toast.error('发送验证码失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 请求新手机验证码
  const requestNewMobileCode = async () => {
    if (!validateMobile(newMobile)) {
      setErrors(prev => ({...prev, mobile: '请输入有效的手机号'}));
      return;
    }

    setIsLoading(true);
    try {
      // 调用发送验证码接口
      await MessageService.sendAuthVerifyCode(newMobile);
      toast.success('验证码已发送');
    } catch (error) {
      console.error('发送验证码失败:', error);
      toast.error('发送验证码失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 验证手机格式
  const validateMobile = (mobile: string): boolean => {
    return /^1[3-9]\d{9}$/.test(mobile);
  };

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (step === 1) {
      if (!oldMobileCode) {
        newErrors.oldMobileCode = '请输入旧手机验证码';
      }
    } else if (step === 2) {
      if (!newMobile) {
        newErrors.mobile = '请输入手机号';
      } else if (!validateMobile(newMobile)) {
        newErrors.mobile = '请输入有效的手机号';
      }

      if (!newMobileCode) {
        newErrors.newMobileCode = '请输入验证码';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理下一步
  const handleNextStep = async () => {
    if (!validateForm()) return;

    if (step === 1) {
      // 验证当前手机号验证码
      setIsLoading(true);
      try {
        const isValid = await ProfileService.validatePhoneCode({
          type: 1, // 1: 更换手机号
          code: oldMobileCode
        });

        if (isValid.data) {
          setStep(2);
        } else {
          setErrors(prev => ({...prev, oldMobileCode: '验证码错误'}));
          toast.error('验证码错误，请重新输入');
        }
      } catch (error) {
        console.error('验证码校验失败:', error);
        toast.error('验证码校验失败，请稍后重试');
      } finally {
        setIsLoading(false);
      }
      return;
    }

    await handleSubmit();
  };

  // 处理表单提交
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      // 调用更新手机号接口
      ProfileService.updatePhone({
        phone: newMobile,
        code: newMobileCode
      }).then(async () => {
        // 更新成功后获取最新的用户信息
        const profileResponse = await ProfileService.getProfile();
        // 转换 Profile 类型为 UserInfo 类型
        const userInfo = {
          ...profileResponse.data,
          // 将 age 从 number 转换为 string
          age: profileResponse.data.age ? String(profileResponse.data.age) : undefined,
          // 将 Date 类型转换为 string
          membershipExpireTime: profileResponse.data.membershipExpireTime ?
            profileResponse.data.membershipExpireTime.toISOString() : undefined
        };
        // 更新 Redux 中的用户信息
        dispatch(setUser(userInfo));

        setSuccess(true);
        toast.success('手机号更换成功');
        setTimeout(() => {
          onClose();
        }, 1500);
      }).catch(err => {
        console.log(err)
        if (err.code === 30086 || err.code === 30084) {
          setErrors(prev => ({...prev, mobile: err.msg}));
        } else if (err.code === 20019 || err.code === 20010) {
          setErrors(prev => ({...prev, newMobileCode: '验证码错误，请检查后输入'}));
        }
      })
    } catch (error) {
      console.error('操作失败:', error);
      toast.error('手机号更换失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
        <div className="relative w-full max-w-md bg-white rounded-lg shadow-xl mx-4">
          <div className="px-6 py-8 flex flex-col items-center">
            <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-4">
              <FontAwesomeIcon icon={faCheck} className="text-green-600 text-lg" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">手机号更换成功</h3>
            <p className="text-sm text-gray-500">您的账号已成功更换手机号</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="relative w-full max-w-md bg-white rounded-lg shadow-xl mx-4">
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
        >
          <FontAwesomeIcon icon={faTimes} className="text-xl" />
        </button>

        {/* 主内容 */}
        <div className="px-6 py-8">
          {/* 标题 */}
          {step === 1 && (
            <h2 className="text-xl font-bold text-center mb-6">验证当前手机号</h2>
          )}

          {/* 第二步标题 */}
          {step === 2 && (
            <h2 className="text-xl font-bold text-center mb-6">绑定新手机号</h2>
          )}

          {/* 步骤提示 - 在两个步骤都显示 */}
          <div className="mb-6 flex flex-col items-center justify-center">
            <div className="flex items-center justify-center mb-2">
              <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${step === 1 ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-700'}`}>1</div>
              <div className="h-1 w-28 bg-gray-200 mx-1"></div>
              <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${step === 2 ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-700'}`}>2</div>
            </div>
            <div className="flex text-xs text-gray-500">
              <div className="w-20 text-center mr-3">验证旧手机</div>
              <div className="w-20 text-center ml-3">绑定新手机</div>
            </div>
          </div>

          <div className="space-y-4">
            {/* 第一步：验证旧手机 */}
            {step === 1 && (
              <div>
                <p className="text-sm text-gray-500 mb-4">
                  为了保障您的账号安全，更换手机号需要先验证当前手机号
                </p>
                <VerificationCodeInput
                  value={oldMobileCode}
                  onChange={(e) => {
                    setOldMobileCode(e.target.value.replace(/\D/g, ''));
                    if (errors.oldMobileCode) {
                      setErrors(prev => {
                        const newErrors = {...prev};
                        delete newErrors.oldMobileCode;
                        return newErrors;
                      });
                    }
                  }}
                  onRequestCode={requestOldMobileCode}
                  isLoading={isLoading}
                  error={errors.oldMobileCode}
                  label="旧手机验证码"
                  placeholder="请输入验证码"
                />
                <p className="mt-2 text-sm text-gray-500">
                  验证码将发送至 {userMobile}
                </p>
              </div>
            )}

            {/* 第二步：绑定新手机 */}
            {step === 2 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  新手机号
                </label>
                <input
                  type="tel"
                  value={newMobile}
                  onChange={(e) => {
                    setNewMobile(e.target.value.replace(/\D/g, ''));
                    if (errors.mobile) {
                      setErrors(prev => {
                        const newErrors = {...prev};
                        delete newErrors.mobile;
                        return newErrors;
                      });
                    }
                  }}
                  className={`w-full px-3 py-2 border ${errors.mobile ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent`}
                  placeholder="请输入手机号"
                  maxLength={11}
                />
                {errors.mobile && (
                  <p className="mt-1 text-sm text-red-600">{errors.mobile}</p>
                )}

                <div className="mt-4">
                  <VerificationCodeInput
                    value={newMobileCode}
                    onChange={(e) => {
                      setNewMobileCode(e.target.value.replace(/\D/g, ''));
                      if (errors.newMobileCode) {
                        setErrors(prev => {
                          const newErrors = {...prev};
                          delete newErrors.newMobileCode;
                          return newErrors;
                        });
                      }
                    }}
                    onRequestCode={requestNewMobileCode}
                    isLoading={isLoading}
                    error={errors.newMobileCode}
                    label="验证码"
                    placeholder="请输入验证码"
                  />
                  {newMobile && validateMobile(newMobile) && (
                    <p className="mt-2 text-sm text-gray-500">
                      验证码将发送至 {newMobile}
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none"
            >
              取消
            </button>
            <button
              type="button"
              onClick={handleNextStep}
              disabled={isLoading}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none disabled:bg-gray-300 flex items-center justify-center min-w-[80px]"
            >
              {isLoading ? (
                <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
              ) : (
                <>
                  {step === 1 ? '下一步' : '确认'}
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileChangeModal;
