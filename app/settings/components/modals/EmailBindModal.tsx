'use client';

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faSpinner, faCheck, faInfoCircle } from '@fortawesome/free-solid-svg-icons';
import { VerificationCodeInput } from '../VerificationCodeInput';
import MessageService from "@/app/service/message-service";
import ProfileService, { Profile } from "@/app/service/profile-service";
import { toast } from 'react-hot-toast';
import { useDispatch } from 'react-redux';
import { setUser, selectUser } from '@/app/redux/features/authSlice';
import { useAppSelector } from '@/app/redux/hooks';

interface EmailBindModalProps {
  isOpen: boolean;
  onClose: () => void;
  userMobile: string;
  onSuccess?: () => void;
}

export const EmailBindModal: React.FC<EmailBindModalProps> = ({
  isOpen,
  onClose,
  userMobile,
  onSuccess
}) => {
  if (!isOpen) return null;

  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [countdown, setCountdown] = useState(0);
  const currentUser = useAppSelector(selectUser);

  // 表单状态
  const [newEmail, setNewEmail] = useState('');
  const [emailCode, setEmailCode] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 请求邮箱验证码
  const requestEmailCode = async () => {
    if (!validateEmail(newEmail)) {
      setErrors(prev => ({...prev, email: '请输入有效的邮箱地址'}));
      return;
    }

    setIsLoading(true);
    try {
      await MessageService.sendEmailVerifyCode(newEmail);
      toast.success('验证码已发送');
    } catch (error) {
      console.error('发送验证码失败:', error);
      toast.error('发送验证码失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 验证邮箱格式
  const validateEmail = (email: string): boolean => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!newEmail) {
      newErrors.email = '请输入邮箱';
    } else if (!validateEmail(newEmail)) {
      newErrors.email = '请输入有效的邮箱地址';
    }

    if (!emailCode) {
      newErrors.emailCode = '请输入邮箱验证码';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理表单提交
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      // 调用绑定邮箱接口
      await ProfileService.bindEmail({
        email: newEmail,
        code: emailCode
      }).then(async () => {
        // 更新 Redux 中的用户信息
        if (currentUser) {
          dispatch(setUser({
            ...currentUser,
            email: newEmail,
          }));
        }

        setSuccess(true);
        toast.success('邮箱绑定成功');
        
        // 如果提供了onSuccess回调，则调用它
        if (onSuccess) {
          setTimeout(() => {
            onSuccess();
          }, 1500);
        } else {
          setTimeout(() => {
            onClose();
          }, 1500);
        }
      }).catch((error: any) => {
        console.error('操作失败:', error);
        if (error.code === 30086 || error.code === 30084) {
          setErrors(prev => ({...prev, email: error.msg}));
        } else if (error.code === 20019 || error.code === 20010) {
          setErrors(prev => ({...prev, emailCode: '验证码错误，请检查后输入'}));
        } else {
          toast.error('邮箱绑定失败，请稍后重试');
        }
      });
    } catch (error) {
      console.error('操作失败:', error);
      toast.error('邮箱绑定失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
        <div className="relative w-full max-w-md bg-white rounded-lg shadow-xl mx-4">
          <div className="px-6 py-8 flex flex-col items-center">
            <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-4">
              <FontAwesomeIcon icon={faCheck} className="text-green-600 text-lg" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">邮箱绑定成功</h3>
            <p className="text-sm text-gray-500">您的账号已成功绑定新邮箱</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="relative w-full max-w-md bg-white rounded-lg shadow-xl mx-4">
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
        >
          <FontAwesomeIcon icon={faTimes} className="text-xl" />
        </button>

        {/* 主内容 */}
        <div className="px-6 py-8">
          {/* 标题 */}
          <h2 className="text-xl font-bold text-center mb-6">绑定邮箱</h2>

          {/* 提示信息 */}
          {!userMobile && (
            <div className="mb-4 p-3 bg-yellow-50 text-yellow-800 rounded-md flex items-start">
              <FontAwesomeIcon icon={faInfoCircle} className="mr-2 mt-1" />
              <div>
                <p className="text-sm">您需要先绑定手机号才能绑定邮箱。</p>
              </div>
            </div>
          )}

          <div className="space-y-4">
            {/* 邮箱输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                邮箱地址
              </label>
              <input
                type="email"
                value={newEmail}
                onChange={(e) => {
                  setNewEmail(e.target.value);
                  if (errors.email) {
                    setErrors(prev => {
                      const newErrors = {...prev};
                      delete newErrors.email;
                      return newErrors;
                    });
                  }
                }}
                className={`w-full px-3 py-2 border ${errors.email ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent`}
                placeholder="请输入邮箱地址"
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email}</p>
              )}
            </div>

            {/* 邮箱验证码 */}
            <div>
              <VerificationCodeInput
                value={emailCode}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                  setEmailCode(e.target.value.replace(/\D/g, ''));
                  if (errors.emailCode) {
                    setErrors(prev => {
                      const newErrors = {...prev};
                      delete newErrors.emailCode;
                      return newErrors;
                    });
                  }
                }}
                onRequestCode={requestEmailCode}
                isLoading={isLoading}
                error={errors.emailCode}
                label="邮箱验证码"
                placeholder="请输入邮箱验证码"
              />
              {newEmail && validateEmail(newEmail) && emailCode && (
                <p className="mt-2 text-sm text-gray-500">
                  验证码已发送至 {newEmail}
                </p>
              )}
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none"
            >
              取消
            </button>
            <button
              type="button"
              onClick={handleSubmit}
              disabled={isLoading || !userMobile}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none disabled:bg-gray-300 flex items-center justify-center min-w-[80px]"
            >
              {isLoading ? (
                <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
              ) : "确认"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailBindModal;
