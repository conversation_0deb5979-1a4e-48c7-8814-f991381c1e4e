'use client';

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faSpinner, faCheck } from '@fortawesome/free-solid-svg-icons';
import { VerificationCodeInput } from '../VerificationCodeInput';
import MessageService from '@/app/service/message-service';
import ProfileService from '@/app/service/profile-service';
import { useAppDispatch } from '@/app/redux/hooks';
import { setUser } from '@/app/redux/features/authSlice';

interface MobileBindModalProps {
  isOpen: boolean;
  onClose: (success?: boolean) => void;
}

export const MobileBindModal: React.FC<MobileBindModalProps> = ({
  isOpen,
  onClose
}) => {
  if (!isOpen) return null;

  const dispatch = useAppDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [sendingCode, setSendingCode] = useState(false);
  const [success, setSuccess] = useState(false);

  // 表单状态
  const [newMobile, setNewMobile] = useState('');
  const [newMobileCode, setNewMobileCode] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 处理取消按钮点击
  const handleCancel = () => {
    onClose(false); // 传递false表示未成功绑定
  };

  // 请求手机验证码
  const requestNewMobileCode = async () => {
    if (!validateMobile(newMobile)) {
      setErrors(prev => ({...prev, mobile: '请输入有效的手机号'}));
      return;
    }

    setSendingCode(true);
    try {
      await MessageService.sendAuthVerifyCode(newMobile);
      // 验证码发送成功后可以添加提示
    } catch (error) {
      console.error('发送验证码失败:', error);
      setErrors(prev => ({...prev, mobile: '验证码发送失败，请稍后重试'}));
    } finally {
      setSendingCode(false);
    }
  };

  // 验证手机格式
  const validateMobile = (mobile: string): boolean => {
    return /^1[3-9]\d{9}$/.test(mobile);
  };

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!newMobile) {
      newErrors.mobile = '请输入手机号';
    } else if (!validateMobile(newMobile)) {
      newErrors.mobile = '请输入有效的手机号';
    }

    if (!newMobileCode) {
      newErrors.newMobileCode = '请输入验证码';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理提交
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      // 调用绑定手机号API
      await ProfileService.bindMobile({
        mobile: newMobile,
        verifyCode: newMobileCode
      });

      // 绑定成功后，重新获取用户信息更新状态
      const profileResponse = await ProfileService.getProfile();
      const userProfile = profileResponse.data;

      // 更新Redux中的用户数据
      dispatch(setUser({
        mobile: newMobile
      }));

      // 重置全局缓存状态 - 通过全局事件通知
      if (typeof window !== 'undefined') {
        // 创建一个自定义事件
        const event = new CustomEvent('mobileBindSuccess');
        window.dispatchEvent(event);
      }

      setSuccess(true);
      setTimeout(() => {
        onClose(true); // 传递true表示成功绑定
      }, 1500);
    } catch (error: any) {
      console.error('绑定手机号失败:', error);
      // 处理API返回的错误信息
      if (error.response?.data?.message) {
        setErrors(prev => ({...prev, submit: error.response.data.message}));
      } else {
        setErrors(prev => ({...prev, submit: '绑定失败，请稍后重试'}));
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
        <div className="relative w-full max-w-md bg-white rounded-lg shadow-xl mx-4">
          <div className="px-6 py-8 flex flex-col items-center">
            <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-4">
              <FontAwesomeIcon icon={faCheck} className="text-green-600 text-lg" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">手机号绑定成功</h3>
            <p className="text-sm text-gray-500">您的账号已成功绑定手机号</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="relative w-full max-w-md bg-white rounded-lg shadow-xl mx-4">
        {/* 关闭按钮 */}
        <button
          onClick={handleCancel}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
        >
          <FontAwesomeIcon icon={faTimes} className="text-xl" />
        </button>

        {/* 主内容 */}
        <div className="px-6 py-8">
          {/* 标题 */}
          <h2 className="text-xl font-bold text-center mb-6">绑定手机号</h2>

          {/* 政策提示信息 */}
          <div className="mb-4 p-3 bg-blue-50 text-gray-700 rounded-md text-sm">
            <p>应相关政策要求，账号需绑定手机号完成实名认证，绑定后方可继续使用信竞星球。</p>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                手机号
              </label>
              <input
                type="tel"
                value={newMobile}
                onChange={(e) => {
                  setNewMobile(e.target.value.replace(/\D/g, ''));
                  if (errors.mobile) {
                    setErrors(prev => {
                      const newErrors = {...prev};
                      delete newErrors.mobile;
                      return newErrors;
                    });
                  }
                }}
                className={`w-full px-3 py-2 border ${errors.mobile ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent`}
                placeholder="请输入手机号"
                maxLength={11}
              />
              {errors.mobile && (
                <p className="mt-1 text-sm text-red-600">{errors.mobile}</p>
              )}
            </div>

            <div>
              <VerificationCodeInput
                value={newMobileCode}
                onChange={(e) => {
                  setNewMobileCode(e.target.value.replace(/\D/g, ''));
                  if (errors.newMobileCode) {
                    setErrors(prev => {
                      const newErrors = {...prev};
                      delete newErrors.newMobileCode;
                      return newErrors;
                    });
                  }
                }}
                onRequestCode={requestNewMobileCode}
                isLoading={sendingCode}
                error={errors.newMobileCode}
                label="验证码"
                placeholder="请输入验证码"
              />
              {newMobile && validateMobile(newMobile) && (
                <p className="mt-2 text-sm text-gray-500">
                  验证码将发送至 {newMobile}
                </p>
              )}
            </div>

            {/* 提交错误信息显示 */}
            {errors.submit && (
              <div className="p-2 bg-red-50 text-red-600 rounded-md text-sm">
                {errors.submit}
              </div>
            )}
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              onClick={handleCancel}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none"
            >
              取消
            </button>
            <button
              type="button"
              onClick={handleSubmit}
              disabled={isLoading}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none disabled:bg-gray-300 flex items-center justify-center min-w-[80px]"
            >
              {isLoading ? (
                <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
              ) : '确认'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileBindModal;
