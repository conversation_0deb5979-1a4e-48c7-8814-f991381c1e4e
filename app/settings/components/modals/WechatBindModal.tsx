'use client';

import React, { useState, useEffect, useRef } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faSpinner, faCheck, faRefresh, faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';
import ProfileService from '@/app/service/profile-service';
import { useDispatch } from 'react-redux';
import { setUser } from '@/app/redux/features/authSlice';
import { QRCodeCanvas } from 'qrcode.react';

interface WechatBindModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export const WechatBindModal: React.FC<WechatBindModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  if (!isOpen) return null;

  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [ticket, setTicket] = useState<string>('');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [qrCodeExpired, setQrCodeExpired] = useState(false);
  const [qrCodeBound, setQrCodeBound] = useState(false);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);

  // 获取微信绑定二维码
  const fetchWechatQRCode = async () => {
    setIsLoading(true);
    setErrorMessage('');
    setQrCodeExpired(false);
    setQrCodeBound(false);
    try {
      const response = await ProfileService.getBindWeChatTicket();
      setQrCodeUrl(response.data.url);
      setTicket(response.data.ticket);

      // 开始轮询状态
      startPolling(response.data.ticket);
    } catch (error) {
      console.error('获取微信二维码失败:', error);
      setErrorMessage('获取微信二维码失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 开始轮询微信绑定状态
  const startPolling = (ticketValue: string) => {
    // 清除已有的轮询
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
    }

    // 设置轮询间隔（2秒）
    pollingRef.current = setInterval(() => {
      checkBindStatus(ticketValue);
    }, 2000);
  };

  // 检查绑定状态
  const checkBindStatus = async (ticketValue: string) => {
    try {
      const response = await ProfileService.getWeChatBindStatus(ticketValue);
      const status = response.data;

      if (status === -2) {
        // 该微信已被其他账号绑定
        clearInterval(pollingRef.current!);
        setErrorMessage('该微信已被其他账号绑定');
        setQrCodeBound(true);
      } else if (status === -1) {
        // 二维码已失效
        clearInterval(pollingRef.current!);
        setQrCodeExpired(true);
      } else if (status === 1) {
        // 绑定成功
        clearInterval(pollingRef.current!);
        handleBindSuccess();
      }
      // status === 0 表示未扫码，继续轮询
    } catch (error) {
      console.error('检查绑定状态失败:', error);
      clearInterval(pollingRef.current!);
      setErrorMessage('检查绑定状态失败，请刷新二维码');
    }
  };

  // 处理绑定成功
  const handleBindSuccess = async () => {
    setIsLoading(true);
    try {
      // 获取最新用户信息
      const profileResponse = await ProfileService.getProfile();
      if (profileResponse && profileResponse.data) {
        // 更新Redux中的用户信息
        dispatch(setUser({
          wechatBound: true
        }));
      }

      setSuccess(true);

      // 如果提供了onSuccess回调，则延迟调用
      if (onSuccess) {
        setTimeout(() => {
          onSuccess();
        }, 1500);
      } else {
        setTimeout(() => {
          onClose();
        }, 1500);
      }
    } catch (error) {
      console.error('更新用户信息失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 组件加载时获取微信二维码
  useEffect(() => {
    if (isOpen) {
      fetchWechatQRCode();
    }

    // 组件卸载时清除轮询
    return () => {
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
      }
    };
  }, [isOpen]);

  if (success) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
        <div className="relative w-full max-w-md bg-white rounded-lg shadow-xl mx-4">
          <div className="px-6 py-8 flex flex-col items-center">
            <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-4">
              <FontAwesomeIcon icon={faCheck} className="text-green-600 text-lg" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">微信绑定成功</h3>
            <p className="text-sm text-gray-500">您的账号已成功绑定微信</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="relative w-full max-w-md bg-white rounded-lg shadow-xl mx-4">
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
        >
          <FontAwesomeIcon icon={faTimes} className="text-xl" />
        </button>

        {/* 主内容 */}
        <div className="px-6 py-8">
          {/* 标题 */}
          <h2 className="text-xl font-bold text-center mb-6">绑定微信</h2>

          <div className="text-center py-4">
            <p className="mb-4 text-sm text-gray-600">
              使用微信扫描二维码绑定账号
            </p>

            {isLoading ? (
              <div className="w-48 h-48 mx-auto mb-4 flex items-center justify-center">
                <FontAwesomeIcon icon={faSpinner} className="text-gray-400 text-2xl animate-spin" />
              </div>
            ) : errorMessage || qrCodeExpired || qrCodeBound ? (
              <div className="w-48 h-48 bg-gray-100 mx-auto mb-4 flex flex-col items-center justify-center p-4 border border-gray-200 rounded">
                <FontAwesomeIcon icon={faExclamationTriangle} className="text-yellow-500 text-2xl mb-2" />
                <p className="text-sm text-gray-600 mb-3">
                  {errorMessage || (qrCodeExpired ? '二维码已失效' : '该微信已被其他账号绑定')}
                </p>
                {(errorMessage || qrCodeExpired) && (
                  <button
                    onClick={fetchWechatQRCode}
                    className="text-indigo-600 text-sm flex items-center"
                  >
                    <FontAwesomeIcon icon={faRefresh} className="mr-1" />
                    刷新二维码
                  </button>
                )}
              </div>
            ) : qrCodeUrl ? (
              <div className="w-48 h-48 bg-white mx-auto mb-4 flex items-center justify-center p-2 border border-gray-200 rounded">
                <QRCodeCanvas value={qrCodeUrl} size={180} />
              </div>
            ) : (
              <div className="w-48 h-48 bg-gray-200 mx-auto mb-4 flex items-center justify-center">
                <span className="text-gray-500">加载中...</span>
              </div>
            )}

            <p className="text-xs text-gray-500">
              {qrCodeUrl && !qrCodeExpired && !errorMessage && !qrCodeBound ?
                "请使用微信扫描二维码完成绑定" :
                "绑定后可使用微信快捷登录"}
            </p>
          </div>

          <div className="mt-6 flex justify-end">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WechatBindModal;
