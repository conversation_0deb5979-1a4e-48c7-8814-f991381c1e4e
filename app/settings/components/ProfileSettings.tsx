'use client';

import React, { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCamera, faSpinner, faCheck } from '@fortawesome/free-solid-svg-icons';
import { useAppSelector, useAppDispatch } from '@/app/redux/hooks';
import { selectUser, setUser } from '@/app/redux/features/authSlice';
import CommonDropdown from '@/app/components/CommonDropdown';
import type { DropdownOption } from '@/app/components/CommonDropdown';
import ProfileService from '@/app/service/profile-service';
import ReactCrop, { Crop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';

type Gender = 'male' | 'female' | 'other' | '';

interface ProfileFormData {
  nickname: string;
  realName: string;
  avatar: string;
  gender: Gender;
  age: string;
}

export default function ProfileSettings() {
  const user = useAppSelector(selectUser);
  const dispatch = useAppDispatch();

  const [formData, setFormData] = useState<ProfileFormData>({
    nickname: '',
    realName: '',
    avatar: '',
    gender: '',
    age: '',
  });

  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [avatarFile, setAvatarFile] = useState<File | null>(null)
  const [crop, setCrop] = useState<Crop>({
    unit: 'px',
    width: 200,
    height: 200,
    x: 0,
    y: 0
  });
  const [imageRef, setImageRef] = useState<HTMLImageElement | null>(null);
  const [showCropModal, setShowCropModal] = useState(false);
  const [tempImageUrl, setTempImageUrl] = useState<string>('');
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 当用户信息加载完成后更新表单
  useEffect(() => {
    if (user) {
      console.log(user)
      setFormData({
        nickname: user.nickname || '',
        realName: user.name || '',
        avatar: 'https:' + user.avatar || '',
        gender: user.sex === 1 ? 'male' : user.sex === 2 ? 'female' : 'other',
        age: user.age || '',
      });
    }
  }, [user]);

  // 处理表单字段变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // 清除该字段的错误信息
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }

    // 清除成功状态
    if (success) {
      setSuccess(false);
    }
  };

  // 修改验证表单函数
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.nickname.trim()) {
      newErrors.nickname = '昵称不能为空';
    }

    if (formData.age) {
      const ageNum = Number(formData.age);
      if (isNaN(ageNum)) {
        newErrors.age = '年龄必须是有效数字';
      } else if (ageNum <= 0 || ageNum >= 100) {
        newErrors.age = '年龄必须在1-99之间';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 修改文件上传处理函数
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      setErrors(prev => ({
        ...prev,
        avatar: '请上传图片文件'
      }));
      return;
    }

    // 验证文件大小（限制为5MB）
    if (file.size > 5 * 1024 * 1024) {
      setErrors(prev => ({
        ...prev,
        avatar: '图片大小不能超过5MB'
      }));
      return;
    }

    // 创建临时URL
    const reader = new FileReader();
    reader.onload = (e) => {
      const img = new window.Image();
      img.onload = () => {
        // 检查图片尺寸
        if (img.width < 200 || img.height < 200) {
          setErrors(prev => ({
            ...prev,
            avatar: '图片尺寸不能小于200x200像素'
          }));
          return;
        }

        setTempImageUrl(e.target?.result as string);
        setShowCropModal(true);
      };
      img.src = e.target?.result as string;
    };
    reader.readAsDataURL(file);

    // 重置文件输入，允许重复选择相同文件
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 修改裁剪完成处理函数
  const handleCropComplete = () => {
    if (!imageRef || !canvasRef.current || !crop.width || !crop.height) return;

    const canvas = canvasRef.current;
    const scaleX = imageRef.naturalWidth / imageRef.width;
    const scaleY = imageRef.naturalHeight / imageRef.height;

    // 设置画布大小为200x200
    canvas.width = 200;
    canvas.height = 200;
    const ctx = canvas.getContext('2d');

    if (!ctx) return;

    // 计算裁剪区域的实际像素值
    const cropX = crop.x * scaleX;
    const cropY = crop.y * scaleY;
    const cropWidth = crop.width * scaleX;
    const cropHeight = crop.height * scaleY;

    // 绘制裁剪后的图片
    ctx.drawImage(
      imageRef,
      cropX,
      cropY,
      cropWidth,
      cropHeight,
      0,
      0,
      200,
      200
    );

    // 转换为base64
    const base64Image = canvas.toDataURL('image/jpeg', 0.8);

    // 将base64转换为File对象
    fetch(base64Image)
      .then(res => res.blob())
      .then(blob => {
        const file = new File([blob], 'avatar.jpg', { type: 'image/jpeg' });
        setAvatarFile(file);
        setFormData(prev => ({
          ...prev,
          avatar: base64Image
        }));
        setShowCropModal(false);
      })
      .catch(error => {
        console.error('转换图片失败:', error);
        setErrors(prev => ({
          ...prev,
          avatar: '图片处理失败，请重试'
        }));
      });
  };

  // 修改性别选项
  const genderOptions: DropdownOption<Gender>[] = [
    { label: '男', value: 'male' },
    { label: '女', value: 'female' },
    { label: '其他', value: 'other' }
  ];

  // 修改表单提交函数
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      // 调用ProfileService更新用户信息
      await ProfileService.updateUserInfo(
        avatarFile,
        formData.nickname,
        formData.realName,
        formData.gender === 'male' ? 1 : formData.gender === 'female' ? 2 : null,
        formData.age ? parseInt(formData.age) : null
      );

      // 获取最新的用户信息
      const response = await ProfileService.getProfile();
      const updatedUser = response.data;

      // 更新Redux状态
      dispatch(setUser({
        nickname: updatedUser.nickname,
        name: updatedUser.name,
        avatar: updatedUser.avatar,
        sex: updatedUser.sex,
        age: updatedUser.age?.toString() || '',
        currentMembershipLevel: updatedUser.currentMembershipLevel,
        membershipExpireTime: updatedUser.membershipExpireTime?.toString() || ''
      }));

      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (error) {
      console.error('保存失败:', error);
      setErrors(prev => ({
        ...prev,
        submit: '保存失败，请稍后重试'
      }));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">用户资料</h2>

      <form onSubmit={handleSubmit}>
        {/* 头像上传/选择 */}
        <div className="mb-8 flex flex-col items-center">
          <div className="relative mb-4">
            <Image
              src={formData.avatar || 'https://picsum.photos/seed/default/256/256'}
              alt="用户头像"
              width={100}
              height={100}
              className="w-24 h-24 rounded-full object-cover border-4 border-indigo-100"
            />
            <label className="absolute bottom-0 right-0 bg-indigo-600 text-white p-2 rounded-full shadow-md hover:bg-indigo-700 transition cursor-pointer">
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileUpload}
                className="hidden"
              />
              <FontAwesomeIcon icon={faCamera} />
            </label>
          </div>
          <p className="text-sm text-gray-500">点击相机图标上传头像</p>
          {errors.avatar && (
            <p className="mt-1 text-sm text-red-600">{errors.avatar}</p>
          )}
        </div>

        {/* 裁剪模态框 */}
        {showCropModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-4 rounded-lg max-w-2xl w-full">
              <h3 className="text-lg font-semibold mb-4">裁剪头像</h3>
              <div className="relative">
                <ReactCrop
                  crop={crop}
                  onChange={c => setCrop(c)}
                  aspect={1}
                  className="max-h-[400px]"
                  minWidth={200}
                  minHeight={200}
                  keepSelection
                >
                  <img
                    src={tempImageUrl}
                    alt="待裁剪图片"
                    onLoad={e => setImageRef(e.currentTarget)}
                    style={{ maxWidth: '100%', maxHeight: '400px' }}
                  />
                </ReactCrop>
                <canvas ref={canvasRef} className="hidden" />
              </div>
              <div className="mt-4 flex justify-end gap-2">
                <button
                  type="button"
                  onClick={() => setShowCropModal(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800"
                >
                  取消
                </button>
                <button
                  type="button"
                  onClick={handleCropComplete}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                >
                  确定
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 昵称 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              昵称 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              name="nickname"
              value={formData.nickname}
              onChange={handleChange}
              className={`w-full px-3 py-2 border ${errors.nickname ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent`}
            />
            {errors.nickname && (
              <p className="mt-1 text-sm text-red-600">{errors.nickname}</p>
            )}
          </div>

          {/* 姓名 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              姓名
            </label>
            <input
              type="text"
              name="realName"
              value={formData.realName}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            />
          </div>

          {/* 性别 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              性别
            </label>
            <CommonDropdown<Gender>
              label="性别"
              options={genderOptions}
              value={formData.gender}
              onChange={(value) => {
                setFormData(prev => ({
                  ...prev,
                  gender: value || ''
                }));
              }}
              placeholder="请选择性别"
            />
          </div>

          {/* 年龄 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              年龄
            </label>
            <input
              type="number"
              name="age"
              aria-controls='false'
              value={formData.age}
              onChange={handleChange}
              className={`w-full px-3 py-2 border ${errors.age ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent`}
            />
            {errors.age && (
              <p className="mt-1 text-sm text-red-600">{errors.age}</p>
            )}
          </div>
        </div>

        {/* 表单按钮 */}
        <div className="mt-8 flex justify-end">
          <button
            type="submit"
            disabled={isLoading}
            className="px-6 py-2 bg-indigo-600 text-white rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none flex items-center justify-center min-w-[120px]"
          >
            {isLoading ? (
              <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
            ) : success ? (
              <>
                <FontAwesomeIcon icon={faCheck} className="mr-2" />
                已保存
              </>
            ) : '保存'}
          </button>
        </div>
      </form>
    </div>
  );
}
