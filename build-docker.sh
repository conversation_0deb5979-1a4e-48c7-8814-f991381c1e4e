#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}开始构建 xjxq-next Docker 镜像 (x86 架构)...${NC}"

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo -e "${RED}错误: Docker 未安装，请先安装 Docker${NC}"
    exit 1
fi

# 检查 Docker Compose 是否安装
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}错误: Docker Compose 未安装，请先安装 Docker Compose${NC}"
    exit 1
fi

# 构建镜像
echo -e "${YELLOW}构建 Docker 镜像...${NC}"
docker-compose build

# 检查构建是否成功
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Docker 镜像构建成功!${NC}"
    
    # 询问是否启动容器
    read -p "是否立即启动容器? (y/n): " start_container
    if [[ $start_container == "y" || $start_container == "Y" ]]; then
        echo -e "${YELLOW}启动 Docker 容器...${NC}"
        docker-compose up -d
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}Docker 容器启动成功!${NC}"
            echo -e "${GREEN}应用已部署在 http://localhost:3000${NC}"
        else
            echo -e "${RED}Docker 容器启动失败${NC}"
        fi
    else
        echo -e "${YELLOW}您可以使用以下命令手动启动容器:${NC}"
        echo -e "${GREEN}docker-compose up -d${NC}"
    fi
else
    echo -e "${RED}Docker 镜像构建失败${NC}"
fi 