#!/bin/bash

# 设置变量
REGISTRY="crpi-kdbdcpjbidi7bhfa.cn-shenzhen.personal.cr.aliyuncs.com"
NAMESPACE="xjxq"
IMAGE_NAME="xjxq-next"
TAG="latest"
USERNAME="chenjiahui@1826113689025326"
CONTAINER_NAME="xjxq-next"
PORT="3000"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    --tag|-t)
      TAG="$2"
      shift 2
      ;;
    --port|-p)
      PORT="$2"
      shift 2
      ;;
    --help|-h)
      echo "用法: $0 [选项]"
      echo "选项:"
      echo "  -t, --tag TAG   指定镜像标签 (默认: latest)"
      echo "  -p, --port PORT 指定端口映射 (默认: 3000)"
      echo "  -h, --help      显示帮助信息"
      exit 0
      ;;
    *)
      echo "未知选项: $1"
      echo "使用 --help 查看帮助"
      exit 1
      ;;
  esac
done

# 显示执行的命令
set -x

# 登录阿里云Docker Registry
echo "🔑 登录阿里云Docker Registry..."
docker login --username=${USERNAME} ${REGISTRY}

# 拉取镜像
echo "⬇️ 拉取镜像..."
docker pull ${REGISTRY}/${NAMESPACE}/${IMAGE_NAME}:${TAG}

# 检查容器是否已存在
if docker ps -a | grep -q ${CONTAINER_NAME}; then
  echo "🔄 停止并删除已存在的容器..."
  docker stop ${CONTAINER_NAME} || true
  docker rm ${CONTAINER_NAME} || true
fi

# 运行容器
echo "🚀 启动容器..."
docker run -d \
  --name ${CONTAINER_NAME} \
  -p ${PORT}:3000 \
  --restart unless-stopped \
  ${REGISTRY}/${NAMESPACE}/${IMAGE_NAME}:${TAG}

# 检查容器状态
echo "📊 容器状态:"
docker ps | grep ${CONTAINER_NAME}

echo "✅ 部署完成！"
echo "应用已启动，可通过 http://localhost:${PORT} 访问" 