/**
 * MockService类
 * 用于模拟API请求，提供前端开发时的模拟数据
 */
export class MockService {
  private static instance: MockService;
  private mockData: Map<string, any>;
  
  private constructor() {
    this.mockData = new Map();
    console.log('初始化 MockService');
  }
  
  /**
   * 获取MockService的单例实例
   */
  public static getInstance(): MockService {
    if (!MockService.instance) {
      MockService.instance = new MockService();
    }
    return MockService.instance;
  }
  
  /**
   * 添加模拟数据
   * @param url API URL
   * @param data 响应数据
   */
  public addMockData(url: string, data: any): void {
    console.log(`添加mock数据: ${url}`);
    this.mockData.set(url, data);
  }
  
  /**
   * 获取模拟数据
   * @param url API URL
   * @returns 对应URL的模拟数据
   */
  public getMockData(url: string): any {
    return this.mockData.get(url);
  }
  
  /**
   * 删除模拟数据
   * @param url API URL
   */
  public removeMockData(url: string): void {
    this.mockData.delete(url);
  }
  
  /**
   * 清除所有模拟数据
   */
  public clearMockData(): void {
    this.mockData.clear();
  }
  
  /**
   * 模拟fetch请求
   * @param url API URL
   * @param options 请求选项
   * @returns 模拟的响应
   */
  public async fetchMockData(url: string, options?: RequestInit): Promise<any> {
    console.log(`Mock请求: ${url}`);
    
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const data = this.mockData.get(url);
    
    if (!data) {
      console.warn(`未找到mock数据: ${url}`);
      return null;
    }
    
    console.log(`返回mock数据: ${url}`);
    return data;
  }
} 