#!/bin/bash

# 设置默认变量
REGISTRY="crpi-kdbdcpjbidi7bhfa.cn-shenzhen.personal.cr.aliyuncs.com"
NAMESPACE="xjxq"
IMAGE_NAME="xjxq-next"
TAG="latest"
USERNAME="chenjiahui@1826113689025326"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    --tag|-t)
      TAG="$2"
      shift 2
      ;;
    --registry|-r)
      REGISTRY="$2"
      shift 2
      ;;
    --namespace|-n)
      NAMESPACE="$2"
      shift 2
      ;;
    --username|-u)
      USERNAME="$2"
      shift 2
      ;;
    --help|-h)
      echo "用法: $0 [选项]"
      echo "选项:"
      echo "  -t, --tag TAG       指定镜像标签 (默认: latest)"
      echo "  -r, --registry REG  指定Registry地址"
      echo "  -n, --namespace NS  指定命名空间 (默认: xjxq)"
      echo "  -u, --username USER 指定用户名"
      echo "  -h, --help          显示帮助信息"
      exit 0
      ;;
    *)
      echo "未知选项: $1"
      echo "使用 --help 查看帮助"
      exit 1
      ;;
  esac
done

# 显示执行的命令
set -x

# 构建生产环境
echo "🚀 开始构建生产环境..."
npm run build:prod

# 构建Docker镜像
echo "🐳 构建Docker镜像..."
docker build -t ${IMAGE_NAME}:${TAG} .

# 登录阿里云Docker Registry
echo "🔑 登录阿里云Docker Registry..."
docker login --username=${USERNAME} ${REGISTRY}

# 为镜像打标签
echo "🏷️ 为镜像打标签..."
docker tag ${IMAGE_NAME}:${TAG} ${REGISTRY}/${NAMESPACE}/${IMAGE_NAME}:${TAG}

# 推送镜像到阿里云Registry
echo "⬆️ 推送镜像到阿里云Registry..."
docker push ${REGISTRY}/${NAMESPACE}/${IMAGE_NAME}:${TAG}

echo "✅ 部署完成！"
echo "镜像地址: ${REGISTRY}/${NAMESPACE}/${IMAGE_NAME}:${TAG}"

# 清理本地镜像（可选）
# echo "🧹 清理本地镜像..."
# docker rmi ${IMAGE_NAME}:${TAG}
# docker rmi ${REGISTRY}/${NAMESPACE}/${IMAGE_NAME}:${TAG} 